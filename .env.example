# Application Configuration
APP_NAME=Pydantic AI Backend
APP_VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# AI Provider API Keys
# OpenAI (required for backward compatibility)
OPENAI_API_KEY=your_openai_api_key_here

# Additional AI Providers (optional - configure as needed)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
GROQ_API_KEY=your_groq_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
COHERE_API_KEY=your_cohere_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
TOGETHER_API_KEY=your_together_api_key_here

# Provider Selection and Fallback Configuration
PRIMARY_PROVIDER=openai
FALLBACK_PROVIDERS=groq,google
ENABLE_FALLBACK=true

# AI Model Configuration
DEFAULT_MODEL=gpt-4o-mini
MAX_TOKENS=1000
TEMPERATURE=0.7

# Free Model Preferences
PREFER_FREE_MODELS=true
FREE_MODELS=llama-3.3-70b-versatile,gemini-2.0-flash,deepseek/deepseek-chat,meta-llama/llama-3.1-70b-instruct:free

# System Prompt
DEFAULT_SYSTEM_PROMPT=You are a helpful AI assistant built with Pydantic AI. You can help with various tasks and have access to tools for calculations, weather information, and other utilities.
