# Design Document

## Overview

The AI Brain Foundation design establishes a robust, scalable microservices architecture that transforms the current Pydantic AI backend into a comprehensive AI platform. The design follows the hybrid approach outlined in the development guide, combining Pydantic AI's type-safe reliability with specialized frameworks, delivered through FastAPI microservices, and backed by Supabase as the unified data layer.

The foundation implements a layered architecture with clear separation of concerns: API Gateway layer for request routing and authentication, Service layer for business logic, Data layer for persistence and vector operations, and Infrastructure layer for monitoring, configuration, and deployment. This design supports the six core AI capabilities while maintaining high availability, scalability, and maintainability.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "External Clients"
        WEB[Web Applications]
        API[API Clients]
        MOB[Mobile Apps]
    end

    subgraph "API Gateway Layer"
        GATEWAY[FastAPI Gateway]
        AUTH[Auth Middleware]
        RATE[Rate Limiter]
        CORS[CORS Handler]
    end

    subgraph "Service Layer"
        CHAT[Chat Service]
        RAG[RAG Service]
        VISION[Vision Service]
        WORKFLOW[Workflow Service]
        CONTENT[Content Service]
        AGENT[Agent Management]
    end

    subgraph "Data Layer"
        SUPABASE[(Supabase)]
        VECTOR[(pgvector)]
        CACHE[(<PERSON>is Cache)]
    end

    subgraph "Infrastructure Layer"
        CONFIG[Configuration]
        MONITOR[Monitoring]
        LOGGING[Logging]
        HEALTH[Health Checks]
    end

    subgraph "External Services"
        OPENAI[OpenAI]
        ANTHROPIC[Anthropic]
        GOOGLE[Google AI]
        GROQ[Groq]
    end

    WEB --> GATEWAY
    API --> GATEWAY
    MOB --> GATEWAY

    GATEWAY --> AUTH
    AUTH --> RATE
    RATE --> CORS

    CORS --> CHAT
    CORS --> RAG
    CORS --> VISION
    CORS --> WORKFLOW
    CORS --> CONTENT
    CORS --> AGENT

    CHAT --> SUPABASE
    RAG --> VECTOR
    VISION --> SUPABASE
    WORKFLOW --> SUPABASE
    CONTENT --> SUPABASE
    AGENT --> SUPABASE

    CHAT --> CACHE
    RAG --> CACHE

    CHAT --> OPENAI
    CHAT --> ANTHROPIC
    CHAT --> GOOGLE
    CHAT --> GROQ

    CONFIG --> MONITOR
    MONITOR --> LOGGING
    LOGGING --> HEALTH
```

### Service Architecture Patterns

The foundation implements several key architectural patterns:

1. **API Gateway Pattern**: Single entry point for all external requests with centralized authentication, rate limiting, and routing
2. **Service Registry Pattern**: Dynamic service discovery and health monitoring
3. **Circuit Breaker Pattern**: Fault tolerance for external provider calls
4. **Repository Pattern**: Abstracted data access layer with consistent interfaces
5. **Observer Pattern**: Event-driven architecture for cross-service communication

## Components and Interfaces

### 1. API Gateway Component

**Purpose**: Centralized request routing, authentication, and cross-cutting concerns

**Key Interfaces**:
```python
class APIGateway:
    async def route_request(self, request: Request) -> Response
    async def authenticate_request(self, request: Request) -> AuthContext
    async def apply_rate_limiting(self, request: Request) -> bool
    async def log_request(self, request: Request, response: Response) -> None
```

**Responsibilities**:
- Request routing to appropriate services
- JWT token validation and user context extraction
- Rate limiting per client/endpoint
- Request/response logging with correlation IDs
- CORS handling and security headers

### 2. Database Service Component

**Purpose**: Unified data access layer with vector capabilities

**Key Interfaces**:
```python
class DatabaseService:
    async def initialize_schema(self) -> bool
    async def execute_migration(self, version: str) -> bool
    async def create_conversation(self, conversation: ConversationCreate) -> Conversation
    async def store_embedding(self, embedding: EmbeddingCreate) -> Embedding
    async def vector_search(self, query_vector: List[float], limit: int) -> List[Document]
```

**Schema Design**:
- `conversations`: Chat conversation metadata and history
- `messages`: Individual chat messages with embeddings
- `documents`: Document storage with vector embeddings
- `users`: User profiles and authentication data
- `agents`: Custom agent configurations
- `provider_logs`: Provider usage and performance metrics

### 3. Authentication Service Component

**Purpose**: Comprehensive authentication and authorization

**Key Interfaces**:
```python
class AuthService:
    async def authenticate_user(self, credentials: UserCredentials) -> AuthResult
    async def validate_jwt_token(self, token: str) -> TokenPayload
    async def check_permissions(self, user: User, resource: str, action: str) -> bool
    async def create_service_token(self, service_name: str) -> str
```

**Security Features**:
- Supabase Auth integration for user management
- JWT token generation and validation
- Role-based access control (RBAC)
- Row Level Security (RLS) policy enforcement
- API key management for service-to-service communication

### 4. Provider Management Component

**Purpose**: Multi-provider AI service management with fallback

**Key Interfaces**:
```python
class ProviderManager:
    async def get_available_providers(self) -> List[ProviderInfo]
    async def select_optimal_provider(self, request: AIRequest) -> Provider
    async def execute_with_fallback(self, request: AIRequest) -> AIResponse
    async def track_provider_metrics(self, provider: str, metrics: ProviderMetrics) -> None
```

**Provider Strategy**:
- Primary provider selection based on configuration
- Automatic fallback chain with configurable order
- Free model prioritization when enabled
- Provider health monitoring and circuit breaker implementation
- Cost optimization through intelligent provider selection

### 5. Configuration Management Component

**Purpose**: Centralized configuration with environment support

**Key Interfaces**:
```python
class ConfigManager:
    def load_configuration(self, environment: str) -> Configuration
    def validate_configuration(self, config: Configuration) -> ValidationResult
    def get_provider_configs(self) -> Dict[str, ProviderConfig]
    def reload_configuration(self) -> bool
```

**Configuration Structure**:
- Environment-specific settings (dev, staging, production)
- Provider API keys and model configurations
- Database connection strings and pool settings
- Monitoring and logging configurations
- Feature flags and experimental settings

### 6. Monitoring and Observability Component

**Purpose**: Comprehensive system monitoring and observability

**Key Interfaces**:
```python
class ObservabilityService:
    async def record_metric(self, metric: Metric) -> None
    async def log_event(self, event: LogEvent) -> None
    async def create_trace(self, operation: str) -> TraceContext
    async def check_system_health(self) -> HealthStatus
```

**Monitoring Capabilities**:
- Request/response metrics with percentiles
- Error rate tracking per service and endpoint
- Provider performance and availability monitoring
- Database query performance and connection pool metrics
- Custom business metrics for AI operations

## Data Models

### Core Data Models

```python
# User and Authentication Models
class User(BaseModel):
    id: UUID
    email: str
    role: UserRole
    created_at: datetime
    last_login: Optional[datetime]
    metadata: Dict[str, Any]

class Conversation(BaseModel):
    id: UUID
    user_id: UUID
    title: Optional[str]
    created_at: datetime
    updated_at: datetime
    message_count: int
    metadata: Dict[str, Any]

class Message(BaseModel):
    id: UUID
    conversation_id: UUID
    role: MessageRole
    content: str
    embedding: Optional[List[float]]
    provider_used: str
    model_used: str
    tokens_used: Optional[int]
    created_at: datetime

# Document and Vector Models
class Document(BaseModel):
    id: UUID
    title: str
    content: str
    embedding: List[float]
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

class EmbeddingIndex(BaseModel):
    id: UUID
    document_id: UUID
    chunk_index: int
    content: str
    embedding: List[float]
    metadata: Dict[str, Any]

# Agent and Configuration Models
class AgentConfig(BaseModel):
    id: UUID
    name: str
    description: str
    system_prompt: str
    provider: str
    model: str
    tools: List[str]
    created_by: UUID
    created_at: datetime
    is_active: bool
```

### Database Schema

```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Users table with RLS
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    role TEXT NOT NULL DEFAULT 'user',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_login TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}'
);

-- Conversations table
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    message_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'
);

-- Messages table with vector embeddings
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    role TEXT NOT NULL,
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI embedding dimension
    provider_used TEXT,
    model_used TEXT,
    tokens_used INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Documents table for RAG
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    embedding vector(1536),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Agent configurations
CREATE TABLE agent_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    system_prompt TEXT NOT NULL,
    provider TEXT NOT NULL,
    model TEXT NOT NULL,
    tools JSONB DEFAULT '[]',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Provider usage logs
CREATE TABLE provider_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider TEXT NOT NULL,
    model TEXT NOT NULL,
    request_tokens INTEGER,
    response_tokens INTEGER,
    response_time_ms INTEGER,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_embedding ON messages USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX idx_documents_embedding ON documents USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX idx_provider_logs_created_at ON provider_logs(created_at);
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
```

## Error Handling

### Error Classification

The system implements a comprehensive error handling strategy with standardized error types:

1. **Validation Errors** (400): Invalid request data or parameters
2. **Authentication Errors** (401): Invalid or missing authentication
3. **Authorization Errors** (403): Insufficient permissions
4. **Not Found Errors** (404): Requested resource doesn't exist
5. **Rate Limit Errors** (429): Request rate limits exceeded
6. **Provider Errors** (502): External AI provider failures
7. **Internal Errors** (500): System or database failures

### Error Response Format

```python
class ErrorResponse(BaseModel):
    error: str
    detail: Optional[str]
    error_code: str
    timestamp: datetime
    request_id: str
    retry_after: Optional[int]  # For rate limit errors
```

### Circuit Breaker Implementation

```python
class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    async def call(self, func: Callable, *args, **kwargs):
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.timeout:
                self.state = "HALF_OPEN"
            else:
                raise CircuitBreakerOpenError()

        try:
            result = await func(*args, **kwargs)
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
            
            raise e
```

## Testing Strategy

### Testing Pyramid

The foundation implements a comprehensive testing strategy following the testing pyramid:

1. **Unit Tests** (70%): Fast, isolated tests for individual components
2. **Integration Tests** (20%): Tests for component interactions and database operations
3. **End-to-End Tests** (10%): Full system tests simulating real user scenarios

### Test Categories

**Unit Tests**:
- Provider management logic
- Authentication and authorization functions
- Data model validation
- Configuration management
- Error handling scenarios

**Integration Tests**:
- Database operations and migrations
- Supabase integration
- Provider API interactions
- Service-to-service communication
- Cache operations

**End-to-End Tests**:
- Complete chat workflows
- Multi-provider fallback scenarios
- Authentication flows
- API gateway routing
- Performance under load

### Test Infrastructure

```python
# Test configuration
class TestConfig:
    database_url: str = "postgresql://test_user:test_pass@localhost:5432/test_db"
    supabase_url: str = "http://localhost:54321"
    redis_url: str = "redis://localhost:6379/1"
    test_api_keys: Dict[str, str] = {
        "openai": "test-key-openai",
        "anthropic": "test-key-anthropic"
    }

# Test fixtures
@pytest.fixture
async def test_db():
    # Setup test database with migrations
    async with create_test_database() as db:
        await run_migrations(db)
        yield db
        await cleanup_test_data(db)

@pytest.fixture
async def authenticated_client():
    # Create test client with authentication
    client = TestClient(app)
    token = create_test_jwt_token()
    client.headers.update({"Authorization": f"Bearer {token}"})
    return client
```

### Performance Testing

The foundation includes performance testing capabilities:

- Load testing with configurable concurrent users
- Stress testing for provider fallback scenarios
- Database performance testing with large datasets
- Memory and CPU profiling under load
- Response time monitoring and alerting

## Deployment Architecture

### Container Strategy

The foundation uses a multi-container deployment strategy with Docker:

```dockerfile
# Multi-stage Dockerfile for optimization
FROM python:3.11-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim as runtime
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose Configuration

```yaml
version: '3.8'
services:
  ai-brain-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=ai_brain
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

volumes:
  redis_data:
  postgres_data:
```

### Environment Configuration

The foundation supports multiple deployment environments:

- **Development**: Local development with hot reloading
- **Testing**: Automated testing environment with test databases
- **Staging**: Production-like environment for integration testing
- **Production**: High-availability production deployment

Each environment has specific configuration files and deployment scripts to ensure consistency and reliability across the deployment pipeline.