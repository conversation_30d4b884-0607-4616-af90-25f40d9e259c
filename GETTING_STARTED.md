# Getting Started with Your Pydantic AI App

## 🎉 Congratulations!

Your Pydantic AI application has been successfully built and is ready to use! This is a modern, production-ready AI backend featuring:

- **Pydantic AI** for intelligent agent capabilities
- **FastAPI** for high-performance REST API
- **Function calling** with 10+ built-in tools
- **Conversation management** with persistent context
- **Type safety** with full Pydantic validation
- **Comprehensive testing** with pytest

## 🚀 Quick Start (3 Steps)

### 1. Set Your OpenAI API Key

Edit the `.env` file and replace the placeholder with your actual OpenAI API key:

```bash
# Open .env file and update:
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
```

### 2. Start the Server

```bash
python run.py
```

You should see:
```
Starting Pydantic AI Backend v1.0.0
✓ Configuration validated successfully
Server will run on 0.0.0.0:8000
```

### 3. Test the API

Open your browser to:
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🧪 Try It Out

### Using the Interactive Docs

1. Go to http://localhost:8000/docs
2. Click on "POST /chat"
3. Click "Try it out"
4. Enter a message like: `"Calculate the square root of 144"`
5. Click "Execute"

### Using curl

```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What is 15 * 23?"}'
```

### Using Python

```python
import httpx

response = httpx.post("http://localhost:8000/chat", json={
    "message": "Generate a UUID and tell me the current time"
})

print(response.json()["response"])
```

## 🛠️ Available Tools

Your AI agent comes with these built-in tools:

1. **Calculator** - Math operations, trigonometry, square roots
2. **Weather** - Mock weather data for any location
3. **UUID Generator** - Create unique identifiers
4. **Time** - Get current timestamp
5. **Base64** - Encode/decode text
6. **Password Generator** - Create secure passwords
7. **Text Processing** - Word count, reverse text, case conversion
8. **Email Extraction** - Find email addresses in text

## 📁 Project Structure

```
ai_backend/
├── app/
│   ├── main.py          # FastAPI application
│   ├── agent.py         # Pydantic AI agent
│   ├── models.py        # Data models
│   ├── config.py        # Configuration
│   └── tools.py         # AI tools
├── tests/               # Test suite
├── examples/            # Usage examples
├── requirements.txt     # Dependencies
├── .env                 # Environment variables
└── run.py              # Application entry point
```

## 🔧 Configuration

Key environment variables in `.env`:

- `OPENAI_API_KEY` - Your OpenAI API key (required)
- `DEFAULT_MODEL` - AI model (default: gpt-4o-mini)
- `PORT` - Server port (default: 8000)
- `DEBUG` - Debug mode (default: true)

## 🧪 Running Tests

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test file
python -m pytest tests/test_tools.py -v

# Run with coverage
python -m pytest tests/ --cov=app
```

## 📚 Example Conversations

### Basic Chat
```
User: "Hello! What can you help me with?"
AI: "Hello! I'm a helpful AI assistant built with Pydantic AI..."
```

### Using Tools
```
User: "Calculate sin(pi/2) + cos(0)"
AI: "I'll calculate that for you. Result of 'sin(pi/2) + cos(0)' = 2.0"
```

### Weather Information
```
User: "What's the weather in Tokyo?"
AI: "Weather in Tokyo: Sunny, 23.4°C, Humidity: 65%, Wind: 8.2 km/h"
```

### Text Processing
```
User: "Count words in: 'Pydantic AI is amazing'"
AI: "Text statistics: 4 words, 21 characters, 1 lines"
```

## 🔍 API Endpoints

- `GET /` - Welcome message
- `GET /health` - Health check
- `POST /chat` - Chat with AI
- `GET /conversations` - List conversations
- `GET /conversations/{id}` - Get conversation details
- `GET /tools` - List available tools
- `GET /agent/config` - Get agent configuration

## 🚨 Troubleshooting

### "Configuration error: OPENAI_API_KEY environment variable is required"
- Make sure you've set a valid OpenAI API key in your `.env` file

### "Import error" when starting
- Run: `pip install -r requirements.txt`

### Tests failing with API key errors
- This is expected if you don't have a valid API key set
- The core functionality tests should still pass

## 🎯 Next Steps

1. **Get an OpenAI API Key**: Visit https://platform.openai.com/api-keys
2. **Customize the System Prompt**: Edit `DEFAULT_SYSTEM_PROMPT` in `.env`
3. **Add Custom Tools**: Extend the tools in `app/tools.py`
4. **Deploy**: Use Docker, Heroku, or your preferred platform
5. **Monitor**: Add logging and monitoring for production use

## 📖 Learn More

- **Pydantic AI Docs**: https://ai.pydantic.dev/
- **FastAPI Docs**: https://fastapi.tiangolo.com/
- **OpenAI API**: https://platform.openai.com/docs/

## 🤝 Support

If you encounter any issues:
1. Check the logs in the terminal
2. Verify your `.env` configuration
3. Review the API documentation at `/docs`
4. Run the test suite to identify issues

---

**Happy coding with Pydantic AI! 🚀**
