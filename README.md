# Pydantic AI Backend

A modern AI backend built with **Pydantic AI** and **FastAPI**, featuring **multi-provider support**, function calling, conversation management, and a comprehensive set of tools.

## Features

- 🤖 **Multi-Provider AI Support**: OpenAI, Anthropic, Google Gemini, Groq, OpenRouter, DeepSeek, and more
- 🔄 **Automatic Fallback**: Seamless failover between providers for reliability
- 💰 **Free Model Priority**: Automatically prefer free/low-cost models when available
- 🚀 **FastAPI Backend**: High-performance async API with automatic documentation
- 🛠️ **Function Calling**: Rich set of tools including calculator, weather, text processing, and utilities
- 💬 **Conversation Management**: Persistent conversation context and history
- 🔧 **Configurable**: Environment-based configuration with validation
- 📚 **Auto Documentation**: Interactive API docs with Swagger UI
- 🧪 **Type Safety**: Full type hints and Pydantic validation
- 🔒 **Error Handling**: Comprehensive error handling and logging

## Quick Start

### 1. Clone and Setup

```bash
git clone <your-repo>
cd ai_backend
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure Environment

Copy the example environment file and configure your AI provider API keys:

```bash
cp .env.example .env
```

Edit `.env` and set at least one AI provider API key:

```env
# Required: At least one provider API key
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Additional providers for fallback and variety
GROQ_API_KEY=your_groq_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Provider configuration
PRIMARY_PROVIDER=openai
FALLBACK_PROVIDERS=groq,google
PREFER_FREE_MODELS=true
```

### 4. Run the Application

```bash
python run.py
```

The server will start on `http://localhost:8000`

## Multi-Provider Configuration

This application supports multiple AI providers with automatic fallback and free model prioritization.

### Supported Providers

| Provider | Free Tier | Models | API Key Required |
|----------|-----------|--------|------------------|
| **OpenAI** | Limited | GPT-4o-mini, GPT-4o, GPT-3.5-turbo | `OPENAI_API_KEY` |
| **Groq** | ✅ Yes | Llama 3.3 70B, Mixtral 8x7B, Gemma2 9B | `GROQ_API_KEY` |
| **Google Gemini** | ✅ Yes | Gemini 2.0 Flash, Gemini 1.5 Flash/Pro | `GOOGLE_API_KEY` |
| **OpenRouter** | ✅ Yes | DeepSeek, Llama 3.1 70B Free, Claude 3.5 | `OPENROUTER_API_KEY` |
| **DeepSeek** | ✅ Yes | DeepSeek Chat, DeepSeek Coder | `DEEPSEEK_API_KEY` |
| **Anthropic** | Limited | Claude 3.5 Sonnet/Haiku, Claude 3 Opus | `ANTHROPIC_API_KEY` |

### Configuration Options

```env
# Primary provider (first choice)
PRIMARY_PROVIDER=groq

# Fallback providers (comma-separated, in order of preference)
FALLBACK_PROVIDERS=google,openrouter,openai

# Enable automatic fallback on errors
ENABLE_FALLBACK=true

# Prefer free models when available
PREFER_FREE_MODELS=true

# Custom free model list
FREE_MODELS=llama-3.3-70b-versatile,gemini-2.0-flash,deepseek/deepseek-chat
```

### Provider Selection

The application automatically selects providers based on:

1. **Free Model Priority**: When `PREFER_FREE_MODELS=true`, free models are preferred
2. **Primary Provider**: Your configured `PRIMARY_PROVIDER` is tried first
3. **Fallback Chain**: If primary fails, fallback providers are tried in order
4. **Availability**: Only providers with valid API keys are used

### Per-Request Provider Override

You can specify a provider for individual requests:

```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello!",
    "provider": "groq",
    "model": "llama-3.3-70b-versatile"
  }'
```

## API Documentation

Once running, visit:
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## Available Tools

The AI agent comes with the following built-in tools:

### 🧮 Calculator
- **Function**: `calculate(expression)`
- **Example**: "Calculate 2 + 2 * 3"
- **Supports**: Basic math, sqrt, sin, cos, tan, log, pi, e

### 🌤️ Weather (Mock)
- **Function**: `get_weather(location)`
- **Example**: "What's the weather in New York?"
- **Returns**: Temperature, conditions, humidity, wind speed

### 🔧 Utilities
- **generate_uuid()**: Create random UUIDs
- **get_current_time()**: Get current timestamp
- **encode_base64(text)**: Encode text to base64
- **decode_base64(encoded)**: Decode base64 text
- **generate_password(length)**: Generate secure passwords

### 📝 Text Processing
- **count_words(text)**: Count words, characters, lines
- **reverse_text(text)**: Reverse text
- **extract_emails(text)**: Extract email addresses

## API Endpoints

### Chat Completion
```http
POST /chat
Content-Type: application/json

{
  "message": "Calculate the square root of 16",
  "conversation_id": "optional-conversation-id",
  "max_tokens": 1000,
  "temperature": 0.7,
  "use_tools": true
}
```

### Context-Prompt Chat
```http
POST /chat/context-prompt
Content-Type: application/json

{
  "context": "You are a helpful math tutor who explains concepts clearly",
  "prompt": "What is the Pythagorean theorem?",
  "provider": "google",
  "max_tokens": 1000,
  "temperature": 0.7,
  "conversation_id": "optional-conversation-id",
  "use_tools": true
}
```

**Features:**
- **Structured Input**: Separate context and prompt fields for optimal AI processing
- **Smart Provider Selection**: Google Gemini as primary, Groq as fallback
- **Context-Aware**: Uses context as system prompt for better AI understanding
- **Provider Override**: Optional provider selection per request

### List Conversations
```http
GET /conversations
```

### Get Conversation
```http
GET /conversations/{conversation_id}
```

### List Available Tools
```http
GET /tools
```

## Example Usage

### Basic Chat
```python
import httpx

response = httpx.post("http://localhost:8000/chat", json={
    "message": "Hello! Can you calculate 15 * 23 for me?"
})

print(response.json())
```

### Using Tools
```python
# Calculator
response = httpx.post("http://localhost:8000/chat", json={
    "message": "What's the square root of 144?"
})

# Weather
response = httpx.post("http://localhost:8000/chat", json={
    "message": "What's the weather like in London?"
})

# Text processing
response = httpx.post("http://localhost:8000/chat", json={
    "message": "Count the words in this sentence: 'Hello world from Pydantic AI'"
})
```

### Context-Prompt Chat
```python
import httpx

# Basic context-prompt usage
response = httpx.post("http://localhost:8000/chat/context-prompt", json={
    "context": "You are a helpful coding assistant who provides clear, practical examples.",
    "prompt": "How do I create a simple REST API in Python?"
})

print(response.json())

# With specific provider and settings
response = httpx.post("http://localhost:8000/chat/context-prompt", json={
    "context": "You are a math tutor explaining concepts to a high school student.",
    "prompt": "Explain the quadratic formula and when to use it.",
    "provider": "google",
    "max_tokens": 800,
    "temperature": 0.5
})

# Context-aware conversation
response = httpx.post("http://localhost:8000/chat/context-prompt", json={
    "context": "We are working on a Python project that processes CSV files.",
    "prompt": "What's the best way to handle missing data in pandas?",
    "conversation_id": "project-discussion-123"
})
```

### Provider Selection Examples
```python
# Use Google Gemini (default primary provider)
response = httpx.post("http://localhost:8000/chat/context-prompt", json={
    "context": "You are a creative writing assistant.",
    "prompt": "Write a short story about a robot learning to paint.",
    "provider": "google"
})

# Use Groq for faster responses
response = httpx.post("http://localhost:8000/chat/context-prompt", json={
    "context": "You are a technical documentation writer.",
    "prompt": "Explain how to set up a Docker container for a Node.js app.",
    "provider": "groq"
})

# Let the system choose (Google primary, Groq fallback)
response = httpx.post("http://localhost:8000/chat/context-prompt", json={
    "context": "You are a helpful assistant with expertise in multiple domains.",
    "prompt": "What are the benefits of renewable energy?"
})
```

## Configuration

Environment variables (set in `.env`):

| Variable | Default | Description |
|----------|---------|-------------|
| `OPENAI_API_KEY` | - | **Required**: Your OpenAI API key |
| `APP_NAME` | "Pydantic AI Backend" | Application name |
| `DEBUG` | false | Enable debug mode |
| `HOST` | "0.0.0.0" | Server host |
| `PORT` | 8000 | Server port |
| `DEFAULT_MODEL` | "gpt-4o-mini" | OpenAI model to use |
| `MAX_TOKENS` | 1000 | Default max tokens |
| `TEMPERATURE` | 0.7 | Default temperature |

## Development

### Project Structure
```
ai_backend/
├── app/
│   ├── __init__.py
│   ├── main.py          # FastAPI application
│   ├── agent.py         # Pydantic AI agent
│   ├── models.py        # Pydantic models
│   ├── config.py        # Configuration
│   └── tools.py         # AI tools
├── tests/
│   └── __init__.py
├── requirements.txt
├── pyproject.toml
├── .env.example
└── run.py              # Application entry point
```

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black app/
isort app/
```

### Type Checking
```bash
mypy app/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Check the [API documentation](http://localhost:8000/docs)
- Review the example usage above
- Open an issue on GitHub
