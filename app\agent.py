"""
Pydantic AI agent implementation with multi-provider support and function calling capabilities.
"""

import uuid
import logging
from typing import Any, Dict, List, Optional, Union

from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.models.groq import GroqModel
from .providers import SimpleFallbackModel

from .config import get_settings, ProviderType
from .models import AgentConfig, ChatMessage, ChatResponse, ToolCall
from .tools import CalculatorTool, TextTool, UtilityTool, WeatherTool
from .providers import get_provider_factory, ProviderFactory

logger = logging.getLogger(__name__)


class ConversationContext:
    """Context for maintaining conversation state."""
    
    def __init__(self, conversation_id: Optional[str] = None):
        self.conversation_id = conversation_id or str(uuid.uuid4())
        self.messages: List[ChatMessage] = []
        self.metadata: Dict[str, Any] = {}
    
    def add_message(self, message: ChatMessage):
        """Add a message to the conversation."""
        self.messages.append(message)
    
    def get_conversation_history(self) -> List[Dict[str, str]]:
        """Get conversation history in format expected by AI model."""
        return [
            {"role": msg.role.value, "content": msg.content}
            for msg in self.messages
        ]


class PydanticAIAgent:
    """Main AI agent class using Pydantic AI with multi-provider support."""

    def __init__(
        self,
        config: Optional[AgentConfig] = None,
        provider: Optional[ProviderType] = None,
        model_name: Optional[str] = None,
        use_fallback: Optional[bool] = None
    ):
        self.settings = get_settings()
        self.config = config or AgentConfig()
        self.conversations: Dict[str, ConversationContext] = {}
        self.provider_factory = get_provider_factory()

        # Determine provider and model
        self.provider = provider or self.settings.primary_provider
        self.model_name = model_name or self.config.model
        self.use_fallback = use_fallback if use_fallback is not None else self.settings.enable_fallback

        # Initialize the AI model
        self.model = self._initialize_model()

        # Create the agent with tools
        self.agent = Agent(
            model=self.model,
            system_prompt=self.config.system_prompt,
        )

        # Register tools if enabled
        if self.config.enable_tools:
            self._register_tools()

        logger.info(f"Initialized agent with provider: {self.provider}, model: {self.model_name}")

    def _initialize_model(self) -> Union[OpenAIModel, AnthropicModel, GeminiModel, GroqModel, SimpleFallbackModel]:
        """Initialize the AI model based on configuration."""
        try:
            if self.use_fallback:
                # Create fallback model with multiple providers
                return self.provider_factory.create_fallback_model(
                    primary_provider=self.provider,
                    model_name=self.model_name
                )
            else:
                # Create single provider model
                return self.provider_factory.create_model(
                    provider=self.provider,
                    model_name=self.model_name
                )
        except ValueError as e:
            logger.error(f"Failed to initialize model: {e}")
            # Fallback to recommended model
            try:
                recommended_provider, recommended_model = self.provider_factory.get_recommended_model()
                logger.info(f"Using recommended model: {recommended_provider} - {recommended_model}")
                return self.provider_factory.create_model(
                    provider=recommended_provider,
                    model_name=recommended_model
                )
            except Exception as fallback_error:
                logger.error(f"Failed to create fallback model: {fallback_error}")
                raise ValueError(f"No valid AI providers configured: {e}")

    def switch_provider(
        self,
        provider: ProviderType,
        model_name: Optional[str] = None
    ) -> bool:
        """
        Switch to a different provider and model.

        Args:
            provider: New provider to use
            model_name: Optional specific model name

        Returns:
            True if switch was successful, False otherwise
        """
        try:
            old_provider = self.provider
            old_model = self.model_name

            # Create new model
            new_model = self.provider_factory.create_model(
                provider=provider,
                model_name=model_name
            )

            # Update agent configuration
            self.provider = provider
            self.model_name = model_name or self.provider_factory.provider_configs[provider].models[0]
            self.model = new_model

            # Recreate agent with new model
            self.agent = Agent(
                model=self.model,
                system_prompt=self.config.system_prompt,
            )

            # Re-register tools
            if self.config.enable_tools:
                self._register_tools()

            logger.info(f"Switched from {old_provider}:{old_model} to {self.provider}:{self.model_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to switch provider: {e}")
            return False
    
    def _register_tools(self):
        """Register available tools with the agent."""
        
        @self.agent.tool
        def calculate(ctx: RunContext[ConversationContext], expression: str) -> str:
            """Perform mathematical calculations."""
            result = CalculatorTool.calculate(expression)
            if result.error:
                return f"Error calculating '{expression}': {result.error}"
            return f"Result of '{expression}' = {result.result}"
        
        @self.agent.tool
        def get_weather(ctx: RunContext[ConversationContext], location: str) -> str:
            """Get weather information for a location."""
            weather = WeatherTool.get_weather(location)
            return (
                f"Weather in {weather.location}: {weather.description}, "
                f"{weather.temperature}°C, Humidity: {weather.humidity}%, "
                f"Wind: {weather.wind_speed} km/h"
            )
        
        @self.agent.tool
        def generate_uuid(ctx: RunContext[ConversationContext]) -> str:
            """Generate a random UUID."""
            return f"Generated UUID: {UtilityTool.generate_uuid()}"
        
        @self.agent.tool
        def get_current_time(ctx: RunContext[ConversationContext]) -> str:
            """Get the current timestamp."""
            return f"Current time: {UtilityTool.get_current_time()}"
        
        @self.agent.tool
        def encode_base64(ctx: RunContext[ConversationContext], text: str) -> str:
            """Encode text to base64."""
            encoded = UtilityTool.encode_base64(text)
            return f"Base64 encoded: {encoded}"
        
        @self.agent.tool
        def decode_base64(ctx: RunContext[ConversationContext], encoded: str) -> str:
            """Decode base64 text."""
            decoded = UtilityTool.decode_base64(encoded)
            return f"Decoded text: {decoded}"
        
        @self.agent.tool
        def generate_password(ctx: RunContext[ConversationContext], length: int = 12) -> str:
            """Generate a random password."""
            password = UtilityTool.generate_password(length)
            return f"Generated password: {password}"
        
        @self.agent.tool
        def count_words(ctx: RunContext[ConversationContext], text: str) -> str:
            """Count words, characters, and lines in text."""
            stats = TextTool.count_words(text)
            return (
                f"Text statistics: {stats['words']} words, "
                f"{stats['characters']} characters, {stats['lines']} lines"
            )
        
        @self.agent.tool
        def reverse_text(ctx: RunContext[ConversationContext], text: str) -> str:
            """Reverse the given text."""
            reversed_text = TextTool.reverse_text(text)
            return f"Reversed text: {reversed_text}"
        
        @self.agent.tool
        def extract_emails(ctx: RunContext[ConversationContext], text: str) -> str:
            """Extract email addresses from text."""
            emails = TextTool.extract_emails(text)
            if emails:
                return f"Found email addresses: {', '.join(emails)}"
            return "No email addresses found in the text."
    
    def get_or_create_conversation(self, conversation_id: Optional[str] = None) -> ConversationContext:
        """Get existing conversation or create a new one."""
        if conversation_id and conversation_id in self.conversations:
            return self.conversations[conversation_id]
        
        new_conversation = ConversationContext(conversation_id)
        self.conversations[new_conversation.conversation_id] = new_conversation
        return new_conversation
    
    async def chat(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        provider: Optional[ProviderType] = None,
        model_name: Optional[str] = None
    ) -> ChatResponse:
        """
        Process a chat message and return a response.

        Args:
            message: User's message
            conversation_id: Optional conversation ID for context
            system_prompt: Optional system prompt override
            max_tokens: Optional max tokens override
            temperature: Optional temperature override
            provider: Optional provider override for this request
            model_name: Optional model name override for this request

        Returns:
            ChatResponse with the AI's response
        """
        # Switch provider/model if requested for this chat
        original_provider = self.provider
        original_model = self.model_name
        switched = False

        if provider and provider != self.provider:
            switched = self.switch_provider(provider, model_name)
            if not switched:
                logger.warning(f"Failed to switch to {provider}, using current provider {self.provider}")

        # Get or create conversation context
        conversation = self.get_or_create_conversation(conversation_id)

        # Add user message to conversation
        user_message = ChatMessage(role="user", content=message)
        conversation.add_message(user_message)

        try:
            # Run the agent with the message
            result = await self.agent.run(
                message,
                deps=conversation,
                model_settings={
                    "max_tokens": max_tokens or self.config.max_tokens,
                    "temperature": temperature or self.config.temperature,
                }
            )

            # Add assistant response to conversation
            assistant_message = ChatMessage(role="assistant", content=result.data)
            conversation.add_message(assistant_message)

            # Create response
            usage = result.usage()
            response = ChatResponse(
                response=result.data,
                conversation_id=conversation.conversation_id,
                model_used=f"{self.provider}:{self.model_name}",
                tokens_used=usage.total_tokens,
                tool_calls=self._extract_tool_calls(result),
                metadata={
                    "message_count": len(conversation.messages),
                    "cost": usage.details.get('cost') if usage.details else None,
                    "provider": self.provider.value,
                    "fallback_used": isinstance(self.model, SimpleFallbackModel),
                }
            )

            return response

        except Exception as e:
            # Add error message to conversation
            error_message = ChatMessage(role="assistant", content=f"I encountered an error: {str(e)}")
            conversation.add_message(error_message)

            # Handle errors gracefully
            error_response = ChatResponse(
                response=f"I apologize, but I encountered an error: {str(e)}",
                conversation_id=conversation.conversation_id,
                model_used=f"{self.provider}:{self.model_name}",
                metadata={"error": str(e), "provider": self.provider.value}
            )
            return error_response

        finally:
            # Switch back to original provider if we switched for this request
            if switched and provider != original_provider:
                self.switch_provider(original_provider, original_model)
    
    def _extract_tool_calls(self, result) -> Optional[List[Dict[str, Any]]]:
        """Extract tool calls from the result."""
        # This would depend on the specific structure of the result
        # For now, return None as tool calls are handled internally
        return None
    
    def get_conversation_summary(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get summary of a conversation."""
        if conversation_id not in self.conversations:
            return None

        conversation = self.conversations[conversation_id]
        return {
            "conversation_id": conversation_id,
            "message_count": len(conversation.messages),
            "created_at": conversation.messages[0].timestamp if conversation.messages else None,
            "last_updated": conversation.messages[-1].timestamp if conversation.messages else None,
        }

    def get_available_providers(self) -> List[ProviderType]:
        """Get list of available providers."""
        return list(self.provider_factory.provider_configs.keys())

    def get_available_models(self, provider: Optional[ProviderType] = None) -> Dict[ProviderType, List[str]]:
        """Get available models for all or specific provider."""
        return self.provider_factory.get_available_models(provider)

    def get_current_provider_info(self) -> Dict[str, Any]:
        """Get information about the current provider and model."""
        return {
            "provider": self.provider.value,
            "model": self.model_name,
            "use_fallback": self.use_fallback,
            "fallback_active": isinstance(self.model, SimpleFallbackModel),
            "available_providers": [p.value for p in self.get_available_providers()],
        }

    def get_provider_info(self) -> Dict[str, Any]:
        """Alias for get_current_provider_info for backward compatibility."""
        return self.get_current_provider_info()

    def get_recommended_free_models(self) -> Dict[str, str]:
        """Get recommended free models by provider."""
        recommendations = {}
        for provider in self.get_available_providers():
            free_models = self.provider_factory._get_free_models_for_provider(provider)
            if free_models:
                recommendations[provider.value] = free_models[0]
        return recommendations


# Global agent instance
_agent_instance: Optional[PydanticAIAgent] = None


def get_agent() -> PydanticAIAgent:
    """Get the global agent instance."""
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = PydanticAIAgent()
    return _agent_instance


def create_agent(
    config: Optional[AgentConfig] = None,
    provider: Optional[ProviderType] = None,
    model_name: Optional[str] = None,
    use_fallback: Optional[bool] = None
) -> PydanticAIAgent:
    """Create a new agent instance with optional configuration."""
    return PydanticAIAgent(
        config=config,
        provider=provider,
        model_name=model_name,
        use_fallback=use_fallback
    )


def reset_global_agent():
    """Reset the global agent instance (useful for testing or reconfiguration)."""
    global _agent_instance
    _agent_instance = None
