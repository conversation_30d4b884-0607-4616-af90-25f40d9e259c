"""
Chatbot agent that combines plain chat with optional RAG capabilities.
"""

import logging
import time
from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic_ai import Agent, RunContext
from pydantic import BaseModel

from ..config import get_settings
from ..services.router_service import router_service
from ..services.task_analyzer import complexity_analyzer
from ..services.observability import observability_service, SpanMetadata
from ..providers import get_provider_factory
from ..models import ChatResponse, ChatMessage


logger = logging.getLogger(__name__)


class ChatbotContext(BaseModel):
    """Context for chatbot conversations."""
    conversation_id: str
    user_id: Optional[str] = None
    session_data: Dict[str, Any] = {}
    rag_enabled: bool = False
    knowledge_base: Optional[str] = None


class ChatbotAgent:
    """
    Advanced chatbot agent with intelligent model routing and optional RAG.
    
    This agent automatically selects the most cost-efficient model based on
    task complexity and can optionally use retrieval-augmented generation.
    """
    
    def __init__(self, enable_rag: bool = False):
        self.settings = get_settings()
        self.provider_factory = get_provider_factory()
        self.enable_rag = enable_rag
        self.conversations: Dict[str, List[ChatMessage]] = {}
        
        # Initialize with a default model (will be overridden by router)
        self.current_model = self.provider_factory.create_model(
            provider=self.settings.primary_provider
        )
        
        # Create the agent
        self.agent = Agent(
            model=self.current_model,
            system_prompt=self._get_system_prompt(),
        )
        
        self._register_tools()
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for the chatbot."""
        base_prompt = """You are an intelligent AI assistant with access to multiple AI models and tools.
        
I can help you with a wide variety of tasks including:
- Answering questions and providing information
- Writing and editing text
- Code generation and debugging
- Mathematical calculations
- Data analysis and reasoning
- Creative tasks like storytelling
- Problem-solving and planning

I automatically select the most appropriate and cost-efficient AI model for each task based on its complexity.
For simple tasks, I use free models to keep costs low. For complex tasks requiring advanced reasoning,
I use more powerful models when necessary.
"""
        
        if self.enable_rag:
            base_prompt += """
I also have access to a knowledge base that I can search to provide more accurate and up-to-date information.
When you ask questions that might benefit from external knowledge, I'll automatically search for relevant information.
"""
        
        return base_prompt
    
    def _register_tools(self):
        """Register tools for the chatbot agent."""
        
        @self.agent.tool
        def search_knowledge_base(ctx: RunContext[ChatbotContext], query: str) -> str:
            """Search the knowledge base for relevant information."""
            if not ctx.deps.rag_enabled:
                return "Knowledge base search is not enabled for this conversation."
            
            # TODO: Implement actual RAG search
            # This would integrate with Qdrant vector database
            return f"Searched knowledge base for: {query}. (RAG implementation pending)"
        
        @self.agent.tool
        def get_conversation_history(ctx: RunContext[ChatbotContext]) -> str:
            """Get recent conversation history for context."""
            conv_id = ctx.deps.conversation_id
            if conv_id not in self.conversations:
                return "No conversation history available."
            
            messages = self.conversations[conv_id][-5:]  # Last 5 messages
            history = []
            for msg in messages:
                history.append(f"{msg.role}: {msg.content}")
            
            return "Recent conversation:\n" + "\n".join(history)
        
        @self.agent.tool
        def analyze_task_complexity(ctx: RunContext[ChatbotContext], task_description: str) -> str:
            """Analyze the complexity of a given task."""
            analysis = complexity_analyzer.analyze(task_description)
            return f"Task complexity: {analysis.task_class} (score: {analysis.score:.2f}). {analysis.reason}"
    
    async def chat(
        self,
        message: str,
        conversation_id: str,
        user_id: Optional[str] = None,
        enable_rag: Optional[bool] = None,
        knowledge_base: Optional[str] = None,
        **kwargs
    ) -> ChatResponse:
        """
        Process a chat message with intelligent model routing.

        Args:
            message: User message
            conversation_id: Conversation identifier
            user_id: Optional user identifier
            enable_rag: Whether to enable RAG for this conversation
            knowledge_base: Specific knowledge base to use
            **kwargs: Additional parameters

        Returns:
            ChatResponse with the AI's response
        """
        start_time = time.time()

        # Create observability metadata
        metadata = SpanMetadata(
            operation_type="agent_execution",
            user_id=user_id,
            conversation_id=conversation_id
        )

        async with observability_service.trace_operation("chatbot_agent_chat", metadata) as span:
            try:
                # Analyze task complexity and get optimal model
                routing_result = router_service.pick(
                    text=message,
                    modality="text",
                    preferred_provider=kwargs.get('preferred_provider'),
                    max_cost=kwargs.get('max_cost')
                )

                # Update model if routing suggests a different one
                if routing_result.selected_model != getattr(self.current_model, 'model_name', ''):
                    logger.info(f"Switching to model: {routing_result.selected_model} ({routing_result.provider})")
                    self.current_model = self.provider_factory.create_model(
                        provider=routing_result.provider,
                        model_name=routing_result.selected_model
                    )

                    # Update agent with new model
                    self.agent = Agent(
                        model=self.current_model,
                        system_prompt=self._get_system_prompt(),
                    )
                    self._register_tools()

                # Create context
                context = ChatbotContext(
                    conversation_id=conversation_id,
                    user_id=user_id,
                    rag_enabled=enable_rag if enable_rag is not None else self.enable_rag,
                    knowledge_base=knowledge_base
                )

                # Store user message
                user_message = ChatMessage(role="user", content=message)
                if conversation_id not in self.conversations:
                    self.conversations[conversation_id] = []
                self.conversations[conversation_id].append(user_message)

                # Get AI response
                result = await self.agent.run(message, deps=context)

                # Store assistant message
                assistant_message = ChatMessage(role="assistant", content=result.data)
                self.conversations[conversation_id].append(assistant_message)

                # Create response
                usage = result.usage()
                response = ChatResponse(
                    response=result.data,
                    conversation_id=conversation_id,
                    model_used=f"{routing_result.provider}:{routing_result.selected_model}",
                    tokens_used=usage.total_tokens,
                    metadata={
                        "routing_reason": routing_result.reason,
                        "estimated_cost": float(routing_result.estimated_cost),
                        "fallback_used": routing_result.fallback_used,
                        "alternatives": routing_result.alternatives,
                        "rag_enabled": context.rag_enabled,
                        "message_count": len(self.conversations[conversation_id])
                    }
                )

                # Log agent execution with observability
                duration_ms = (time.time() - start_time) * 1000
                observability_service.log_agent_execution(
                    agent_type="chatbot",
                    request_data={
                        "message_length": len(message),
                        "conversation_id": conversation_id,
                        "user_id": user_id,
                        "rag_enabled": context.rag_enabled,
                        "knowledge_base": knowledge_base
                    },
                    response_data={
                        "model_used": response.model_used,
                        "tokens_used": response.tokens_used,
                        "response": response.response,
                        "conversation_id": response.conversation_id,
                        "metadata": response.metadata
                    },
                    duration_ms=duration_ms,
                    model_name=routing_result.selected_model,
                    provider=routing_result.provider,
                    estimated_cost=float(routing_result.estimated_cost)
                )

                return response

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                logger.error(f"Error in chatbot agent: {e}")

                # Log the error
                observability_service.log_error(
                    operation_name="chatbot_agent_chat",
                    error=e,
                    context={
                        "message_length": len(message),
                        "conversation_id": conversation_id,
                        "user_id": user_id,
                        "duration_ms": duration_ms
                    }
                )

                raise
    
    def get_conversation_summary(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get summary of a conversation."""
        if conversation_id not in self.conversations:
            return None
        
        messages = self.conversations[conversation_id]
        if not messages:
            return None
        
        return {
            "conversation_id": conversation_id,
            "message_count": len(messages),
            "created_at": messages[0].timestamp if messages else datetime.now(),
            "last_message_at": messages[-1].timestamp if messages else datetime.now(),
            "preview": messages[-1].content[:100] + "..." if len(messages[-1].content) > 100 else messages[-1].content
        }
    
    def clear_conversation(self, conversation_id: str) -> bool:
        """Clear a conversation history."""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            return True
        return False


# Global chatbot agent instance
chatbot_agent = ChatbotAgent(enable_rag=False)
