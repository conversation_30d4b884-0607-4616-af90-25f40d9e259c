"""
Marketing agent for content creation, campaign planning, and marketing analysis.
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic_ai import Agent, RunContext
from pydantic import BaseModel

from ..config import get_settings
from ..services.router_service import router_service
from ..providers import get_provider_factory
from ..models import ChatResponse, ChatMessage


logger = logging.getLogger(__name__)


class MarketingContext(BaseModel):
    """Context for marketing tasks."""
    conversation_id: str
    campaign_type: str = "general"
    target_audience: Optional[str] = None
    brand_voice: str = "professional"
    content_format: str = "text"
    platform: Optional[str] = None


class MarketingAgent:
    """
    Specialized agent for marketing content creation and campaign planning.
    
    This agent optimizes for creative and persuasive content generation while
    maintaining cost efficiency through intelligent model routing.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.provider_factory = get_provider_factory()
        self.conversations: Dict[str, List[ChatMessage]] = {}
        
        # Initialize with a default model (will be overridden by router)
        self.current_model = self.provider_factory.create_model(
            provider=self.settings.primary_provider
        )
        
        # Create the agent
        self.agent = Agent(
            model=self.current_model,
            system_prompt=self._get_system_prompt(),
        )
        
        self._register_tools()
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for the marketing agent."""
        return """You are an expert marketing and content creation specialist with deep expertise in:

Content Creation:
- Social media posts and campaigns
- Blog articles and web content
- Email marketing campaigns
- Ad copy and promotional materials
- Product descriptions and landing pages
- Press releases and announcements

Campaign Strategy:
- Target audience analysis
- Brand positioning and messaging
- Content calendar planning
- Multi-channel campaign coordination
- Performance optimization strategies

Brand Development:
- Voice and tone guidelines
- Brand storytelling
- Visual content concepts
- Messaging frameworks
- Competitive analysis

I create compelling, conversion-focused content that resonates with target audiences
while maintaining brand consistency and marketing best practices. I automatically
select the most appropriate model for each creative task to balance quality and cost.

When creating marketing content:
1. Understand the target audience and objectives
2. Maintain consistent brand voice and messaging
3. Focus on clear value propositions
4. Include compelling calls-to-action
5. Optimize for the specific platform or medium
6. Ensure content is engaging and shareable
"""
    
    def _register_tools(self):
        """Register marketing-specific tools."""
        
        @self.agent.tool
        def analyze_target_audience(ctx: RunContext[MarketingContext], audience_description: str) -> str:
            """Analyze target audience characteristics and preferences."""
            return f"""Target Audience Analysis for: {audience_description}

Key Characteristics:
- Demographics: [Analysis based on description]
- Psychographics: [Interests, values, lifestyle]
- Pain Points: [Common challenges and needs]
- Preferred Channels: [Communication preferences]
- Content Preferences: [Format and style preferences]

Recommended Approach:
- Tone: {ctx.deps.brand_voice}
- Messaging Focus: [Value proposition alignment]
- Content Strategy: [Platform-specific recommendations]
"""
        
        @self.agent.tool
        def generate_hashtags(ctx: RunContext[MarketingContext], content_topic: str, platform: str = "general") -> str:
            """Generate relevant hashtags for social media content."""
            # Simple hashtag generation - in production would use more sophisticated analysis
            topic_words = content_topic.lower().split()
            hashtags = []
            
            for word in topic_words:
                if len(word) > 3:
                    hashtags.append(f"#{word}")
            
            # Add platform-specific popular hashtags
            platform_hashtags = {
                "instagram": ["#instagood", "#photooftheday", "#follow"],
                "twitter": ["#trending", "#news", "#update"],
                "linkedin": ["#professional", "#business", "#networking"],
                "tiktok": ["#fyp", "#viral", "#trending"]
            }
            
            if platform.lower() in platform_hashtags:
                hashtags.extend(platform_hashtags[platform.lower()][:2])
            
            return f"Suggested hashtags for {content_topic} on {platform}:\n" + " ".join(hashtags[:10])
        
        @self.agent.tool
        def create_content_calendar(ctx: RunContext[MarketingContext], duration_days: int, posting_frequency: str) -> str:
            """Create a content calendar framework."""
            frequency_map = {
                "daily": 1,
                "every_other_day": 2,
                "weekly": 7,
                "bi_weekly": 14
            }
            
            interval = frequency_map.get(posting_frequency, 7)
            post_count = duration_days // interval
            
            return f"""Content Calendar Framework ({duration_days} days, {posting_frequency}):

Total Posts: {post_count}
Campaign Type: {ctx.deps.campaign_type}
Target Audience: {ctx.deps.target_audience or 'General'}
Brand Voice: {ctx.deps.brand_voice}

Recommended Content Mix:
- Educational: 40%
- Promotional: 30%
- Engaging/Interactive: 20%
- Behind-the-scenes: 10%

Post Schedule: Every {interval} day(s)
Platform: {ctx.deps.platform or 'Multi-platform'}
"""
        
        @self.agent.tool
        def optimize_for_platform(ctx: RunContext[MarketingContext], content: str, platform: str) -> str:
            """Optimize content for specific social media platforms."""
            optimizations = {
                "instagram": "Focus on visual appeal, use relevant hashtags, keep captions engaging but concise",
                "twitter": "Keep under 280 characters, use trending hashtags, encourage retweets",
                "linkedin": "Professional tone, industry insights, encourage professional discussion",
                "facebook": "Engaging storytelling, encourage comments and shares, use emojis sparingly",
                "tiktok": "Trendy, entertaining, use popular sounds/effects, short and punchy"
            }
            
            optimization = optimizations.get(platform.lower(), "General social media best practices")
            
            return f"Platform Optimization for {platform}:\n{optimization}\n\nOriginal content: {content[:100]}..."
    
    async def create_social_post(
        self,
        prompt: str,
        conversation_id: str,
        platform: str = "general",
        target_audience: Optional[str] = None,
        brand_voice: str = "professional",
        include_hashtags: bool = True,
        **kwargs
    ) -> ChatResponse:
        """
        Create a social media post.
        
        Args:
            prompt: Description of the post to create
            conversation_id: Conversation identifier
            platform: Target social media platform
            target_audience: Description of target audience
            brand_voice: Brand voice/tone to use
            include_hashtags: Whether to include hashtags
            **kwargs: Additional parameters
        """
        try:
            # Enhance prompt for social media
            enhanced_prompt = f"Create a {platform} social media post: {prompt}"
            
            if target_audience:
                enhanced_prompt += f"\nTarget audience: {target_audience}"
            
            enhanced_prompt += f"\nBrand voice: {brand_voice}"
            
            if include_hashtags:
                enhanced_prompt += "\nInclude relevant hashtags"
            
            # Route to appropriate model
            routing_result = router_service.pick(
                text=enhanced_prompt,
                modality="text",
                preferred_provider=kwargs.get('preferred_provider'),
                max_cost=kwargs.get('max_cost')
            )
            
            # Update model if needed
            if routing_result.selected_model != getattr(self.current_model, 'model_name', ''):
                logger.info(f"Marketing agent switching to model: {routing_result.selected_model}")
                self.current_model = self.provider_factory.create_model(
                    provider=routing_result.provider,
                    model_name=routing_result.selected_model
                )
                
                self.agent = Agent(
                    model=self.current_model,
                    system_prompt=self._get_system_prompt(),
                )
                self._register_tools()
            
            # Create context
            context = MarketingContext(
                conversation_id=conversation_id,
                campaign_type="social_media",
                target_audience=target_audience,
                brand_voice=brand_voice,
                content_format="social_post",
                platform=platform
            )
            
            # Generate content
            result = await self.agent.run(enhanced_prompt, deps=context)
            
            # Store messages
            user_message = ChatMessage(role="user", content=f"Create {platform} post: {prompt}")
            assistant_message = ChatMessage(role="assistant", content=result.data)
            
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = []
            self.conversations[conversation_id].extend([user_message, assistant_message])
            
            # Create response
            usage = result.usage()
            response = ChatResponse(
                response=result.data,
                conversation_id=conversation_id,
                model_used=f"{routing_result.provider}:{routing_result.selected_model}",
                tokens_used=usage.total_tokens,
                metadata={
                    "routing_reason": routing_result.reason,
                    "estimated_cost": float(routing_result.estimated_cost),
                    "platform": platform,
                    "brand_voice": brand_voice,
                    "target_audience": target_audience,
                    "content_type": "social_post",
                    "agent_type": "marketing"
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in marketing agent social post creation: {e}")
            raise
    
    async def create_campaign(
        self,
        campaign_brief: str,
        conversation_id: str,
        campaign_type: str = "awareness",
        duration_days: int = 30,
        platforms: List[str] = None,
        **kwargs
    ) -> ChatResponse:
        """
        Create a marketing campaign plan.
        
        Args:
            campaign_brief: Description of the campaign
            conversation_id: Conversation identifier
            campaign_type: Type of campaign (awareness, conversion, engagement, etc.)
            duration_days: Campaign duration in days
            platforms: List of platforms to include
            **kwargs: Additional parameters
        """
        try:
            platforms = platforms or ["instagram", "facebook", "twitter"]
            
            enhanced_prompt = f"""Create a comprehensive {campaign_type} marketing campaign plan:

Campaign Brief: {campaign_brief}
Duration: {duration_days} days
Platforms: {', '.join(platforms)}

Include:
1. Campaign objectives and KPIs
2. Target audience analysis
3. Key messaging and value propositions
4. Content strategy and calendar
5. Platform-specific tactics
6. Budget considerations
7. Success metrics
"""
            
            # Route to appropriate model (campaigns are complex, may need better models)
            routing_result = router_service.pick(
                text=enhanced_prompt,
                modality="text",
                preferred_provider=kwargs.get('preferred_provider'),
                max_cost=kwargs.get('max_cost')
            )
            
            # Update model if needed
            if routing_result.selected_model != getattr(self.current_model, 'model_name', ''):
                self.current_model = self.provider_factory.create_model(
                    provider=routing_result.provider,
                    model_name=routing_result.selected_model
                )
                
                self.agent = Agent(
                    model=self.current_model,
                    system_prompt=self._get_system_prompt(),
                )
                self._register_tools()
            
            # Create context
            context = MarketingContext(
                conversation_id=conversation_id,
                campaign_type=campaign_type,
                content_format="campaign_plan"
            )
            
            # Generate campaign plan
            result = await self.agent.run(enhanced_prompt, deps=context)
            
            # Store messages
            user_message = ChatMessage(role="user", content=f"Create {campaign_type} campaign: {campaign_brief}")
            assistant_message = ChatMessage(role="assistant", content=result.data)
            
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = []
            self.conversations[conversation_id].extend([user_message, assistant_message])
            
            # Create response
            usage = result.usage()
            response = ChatResponse(
                response=result.data,
                conversation_id=conversation_id,
                model_used=f"{routing_result.provider}:{routing_result.selected_model}",
                tokens_used=usage.total_tokens,
                metadata={
                    "routing_reason": routing_result.reason,
                    "estimated_cost": float(routing_result.estimated_cost),
                    "campaign_type": campaign_type,
                    "duration_days": duration_days,
                    "platforms": platforms,
                    "content_type": "campaign_plan",
                    "agent_type": "marketing"
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in marketing campaign creation: {e}")
            raise


# Global marketing agent instance
marketing_agent = MarketingAgent()
