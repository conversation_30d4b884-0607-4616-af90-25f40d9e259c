"""
RAG (Retrieval-Augmented Generation) agent for knowledge-enhanced responses.
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic_ai import Agent, RunContext
from pydantic import BaseModel

from ..config import get_settings
from ..services.router_service import router_service
from ..services.rag_service import rag_service
from ..services.document_processor import document_processor
from ..services.observability import observability_service, SpanMetadata
from ..providers import get_provider_factory
from ..models import ChatResponse, ChatMessage


logger = logging.getLogger(__name__)


class RAGContext(BaseModel):
    """Context for RAG operations."""
    conversation_id: str
    knowledge_base: Optional[str] = None
    search_results: List[Dict[str, Any]] = []
    retrieval_enabled: bool = True


class RAGAgent:
    """
    Retrieval-Augmented Generation agent that enhances responses with external knowledge.
    
    This agent automatically searches relevant knowledge bases and incorporates
    retrieved information into responses using the most appropriate model.
    """
    
    def __init__(self, default_knowledge_base: Optional[str] = None):
        self.settings = get_settings()
        self.provider_factory = get_provider_factory()
        self.default_knowledge_base = default_knowledge_base
        self.conversations: Dict[str, List[ChatMessage]] = {}
        
        # Initialize with a default model (will be overridden by router)
        self.current_model = self.provider_factory.create_model()
        
        # Create the agent
        self.agent = Agent(
            model=self.current_model,
            system_prompt=self._get_system_prompt(),
        )
        
        self._register_tools()
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for the RAG agent."""
        return """You are an advanced AI assistant with access to external knowledge bases and retrieval systems.

Your primary capabilities include:
- Searching and retrieving relevant information from knowledge bases
- Synthesizing retrieved information with your training knowledge
- Providing accurate, well-sourced responses
- Citing sources when using retrieved information
- Handling both factual queries and complex reasoning tasks

When answering questions:
1. First search for relevant information if the query would benefit from external knowledge
2. Combine retrieved information with your existing knowledge
3. Provide comprehensive, accurate responses
4. Always cite sources when using retrieved information
5. Indicate when information comes from external sources vs. your training

You automatically use the most cost-efficient model appropriate for each task's complexity.
"""
    
    def _register_tools(self):
        """Register RAG-specific tools."""
        
        @self.agent.tool
        async def search_knowledge_base(
            ctx: RunContext[RAGContext], 
            query: str, 
            knowledge_base: Optional[str] = None,
            max_results: int = 5
        ) -> str:
            """Search the knowledge base for relevant information."""
            if not ctx.deps.retrieval_enabled:
                return "Knowledge retrieval is disabled for this conversation."
            
            kb = knowledge_base or ctx.deps.knowledge_base or self.default_knowledge_base
            
            # TODO: Implement actual Qdrant vector search
            # This would connect to the Qdrant instance and perform similarity search
            search_results = await self._perform_vector_search(query, kb, max_results)
            
            # Store results in context for potential follow-up
            ctx.deps.search_results.extend(search_results)
            
            if not search_results:
                return f"No relevant information found in knowledge base for: {query}"
            
            # Format results for the model
            formatted_results = []
            for i, result in enumerate(search_results, 1):
                formatted_results.append(
                    f"Source {i}: {result.get('title', 'Unknown')}\n"
                    f"Content: {result.get('content', '')}\n"
                    f"Relevance: {result.get('score', 0):.2f}\n"
                )
            
            return "Retrieved information:\n" + "\n".join(formatted_results)
        
        @self.agent.tool
        def get_search_history(ctx: RunContext[RAGContext]) -> str:
            """Get the search history for this conversation."""
            if not ctx.deps.search_results:
                return "No searches performed in this conversation."
            
            history = []
            for i, result in enumerate(ctx.deps.search_results, 1):
                history.append(f"{i}. {result.get('title', 'Unknown')} (score: {result.get('score', 0):.2f})")
            
            return "Search history:\n" + "\n".join(history)
        
        @self.agent.tool
        async def search_multiple_sources(
            ctx: RunContext[RAGContext],
            query: str,
            sources: List[str],
            max_results_per_source: int = 3
        ) -> str:
            """Search multiple knowledge bases simultaneously."""
            all_results = []
            
            for source in sources:
                results = await self._perform_vector_search(query, source, max_results_per_source)
                for result in results:
                    result['source_kb'] = source
                all_results.extend(results)
            
            # Sort by relevance score
            all_results.sort(key=lambda x: x.get('score', 0), reverse=True)
            
            if not all_results:
                return f"No relevant information found across sources: {', '.join(sources)}"
            
            # Format results
            formatted_results = []
            for i, result in enumerate(all_results[:10], 1):  # Top 10 results
                formatted_results.append(
                    f"Result {i} (from {result.get('source_kb', 'unknown')}):\n"
                    f"Title: {result.get('title', 'Unknown')}\n"
                    f"Content: {result.get('content', '')}\n"
                    f"Relevance: {result.get('score', 0):.2f}\n"
                )
            
            ctx.deps.search_results.extend(all_results)
            return "Multi-source search results:\n" + "\n".join(formatted_results)
    
    async def _perform_vector_search(
        self, 
        query: str, 
        knowledge_base: Optional[str], 
        max_results: int
    ) -> List[Dict[str, Any]]:
        """
        Perform vector similarity search in Qdrant.
        
        TODO: Implement actual Qdrant integration
        """
        # Placeholder implementation
        # In production, this would:
        # 1. Connect to Qdrant
        # 2. Embed the query using an embedding model
        # 3. Perform similarity search
        # 4. Return formatted results
        
        logger.info(f"Performing vector search for: {query} in KB: {knowledge_base}")

        try:
            # Use enhanced RAG service for semantic search
            filters = {"knowledge_base": knowledge_base} if knowledge_base else None

            rag_result = await rag_service.search_documents(
                query=query,
                limit=max_results,
                score_threshold=0.7,
                filters=filters
            )

            # Convert RAG results to expected format
            search_results = []
            for chunk, score in zip(rag_result.chunks, rag_result.similarity_scores):
                search_results.append({
                    "title": f"Document chunk {chunk.metadata.get('chunk_index', 'unknown')}",
                    "content": chunk.content,
                    "score": score,
                    "metadata": {
                        **chunk.metadata,
                        "chunk_id": chunk.id,
                        "retrieval_time_ms": rag_result.retrieval_time_ms,
                        "kb": knowledge_base
                    }
                })

            return search_results

        except Exception as e:
            logger.error(f"Error performing vector search: {e}")
            # Return mock results as fallback
            mock_results = [
                {
                    "title": f"Document about {query}",
                    "content": f"This is relevant information about {query}. [Fallback result due to error: {str(e)}]",
                    "score": 0.85,
                    "metadata": {"kb": knowledge_base, "doc_id": "fallback_1", "error": str(e)}
                }
            ]
            return mock_results[:max_results]

    async def upload_document(
        self,
        file,  # UploadFile
        knowledge_base: Optional[str] = None,
        chunking_strategy: str = "sentences"
    ) -> Dict[str, Any]:
        """Upload and index a document for RAG retrieval."""
        start_time = time.time()

        metadata_span = SpanMetadata(
            operation_type="document_upload",
            file_name=file.filename,
            knowledge_base=knowledge_base
        )

        async with observability_service.trace_operation("rag_upload_document", metadata_span) as span:
            try:
                # Process the document
                processed_doc = await document_processor.process_file(file)

                if not processed_doc.content:
                    return {
                        "status": "error",
                        "message": "No content extracted from document",
                        "filename": file.filename
                    }

                # Add knowledge base to metadata
                doc_metadata = {
                    **processed_doc.metadata,
                    "knowledge_base": knowledge_base or "default",
                    "document_id": f"{knowledge_base or 'default'}_{file.filename}_{int(time.time())}"
                }

                # Index the document using RAG service
                indexing_result = await rag_service.index_document(
                    content=processed_doc.content,
                    metadata=doc_metadata,
                    chunking_strategy=chunking_strategy
                )

                duration_ms = (time.time() - start_time) * 1000

                result = {
                    "status": "success",
                    "filename": file.filename,
                    "knowledge_base": knowledge_base or "default",
                    "processing_time_ms": duration_ms,
                    "document_stats": {
                        "word_count": processed_doc.word_count,
                        "char_count": processed_doc.char_count,
                        "file_type": processed_doc.file_type
                    },
                    "indexing_stats": indexing_result
                }

                # Log the upload operation
                observability_service.log_agent_execution(
                    agent_type="rag",
                    request_data={
                        "operation": "document_upload",
                        "filename": file.filename,
                        "knowledge_base": knowledge_base,
                        "chunking_strategy": chunking_strategy
                    },
                    response_data=result,
                    duration_ms=duration_ms,
                    model_name="document_processor",
                    provider="internal"
                )

                return result

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                logger.error(f"Error uploading document {file.filename}: {e}")

                observability_service.log_error(
                    operation_name="rag_upload_document",
                    error=e,
                    context={
                        "filename": file.filename,
                        "knowledge_base": knowledge_base,
                        "duration_ms": duration_ms
                    }
                )

                return {
                    "status": "error",
                    "message": str(e),
                    "filename": file.filename,
                    "processing_time_ms": duration_ms
                }

    async def chat(
        self,
        message: str,
        conversation_id: str,
        knowledge_base: Optional[str] = None,
        enable_retrieval: bool = True,
        **kwargs
    ) -> ChatResponse:
        """
        Process a chat message with RAG capabilities.
        
        Args:
            message: User message
            conversation_id: Conversation identifier
            knowledge_base: Specific knowledge base to use
            enable_retrieval: Whether to enable retrieval for this request
            **kwargs: Additional parameters
            
        Returns:
            ChatResponse with RAG-enhanced response
        """
        try:
            # Analyze task complexity and get optimal model
            routing_result = router_service.pick(
                text=message,
                modality="text",
                preferred_provider=kwargs.get('preferred_provider'),
                max_cost=kwargs.get('max_cost')
            )
            
            # Update model if routing suggests a different one
            if routing_result.selected_model != getattr(self.current_model, 'model_name', ''):
                logger.info(f"RAG agent switching to model: {routing_result.selected_model}")
                self.current_model = self.provider_factory.create_model(
                    provider_type=routing_result.provider,
                    model_name=routing_result.selected_model
                )
                
                # Update agent with new model
                self.agent = Agent(
                    model=self.current_model,
                    system_prompt=self._get_system_prompt(),
                )
                self._register_tools()
            
            # Create RAG context
            context = RAGContext(
                conversation_id=conversation_id,
                knowledge_base=knowledge_base or self.default_knowledge_base,
                retrieval_enabled=enable_retrieval
            )
            
            # Store user message
            user_message = ChatMessage(role="user", content=message)
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = []
            self.conversations[conversation_id].append(user_message)
            
            # Get AI response with RAG
            result = await self.agent.run(message, deps=context)
            
            # Store assistant message
            assistant_message = ChatMessage(role="assistant", content=result.data)
            self.conversations[conversation_id].append(assistant_message)
            
            # Create response
            usage = result.usage()
            response = ChatResponse(
                response=result.data,
                conversation_id=conversation_id,
                model_used=f"{routing_result.provider}:{routing_result.selected_model}",
                tokens_used=usage.total_tokens,
                metadata={
                    "routing_reason": routing_result.reason,
                    "estimated_cost": float(routing_result.estimated_cost),
                    "fallback_used": routing_result.fallback_used,
                    "rag_enabled": True,
                    "knowledge_base": context.knowledge_base,
                    "search_results_count": len(context.search_results),
                    "retrieval_enabled": enable_retrieval
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in RAG agent: {e}")
            raise


# Global RAG agent instance
rag_agent = RAGAgent()
