"""
Authentication and authorization framework for AI Brain Foundation.
Implements JWT validation, Supabase Auth integration, and RBAC.
"""

import jwt
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
from uuid import UUID

from fastapi import HTTPException, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

from .config import get_settings

logger = logging.getLogger(__name__)


class UserRole(str):
    """User roles for RBAC."""
    USER = "user"
    MODERATOR = "moderator"
    ADMIN = "admin"


class Permission(str):
    """System permissions."""
    READ_CONVERSATIONS = "read:conversations"
    WRITE_CONVERSATIONS = "write:conversations"
    DELETE_CONVERSATIONS = "delete:conversations"
    READ_DOCUMENTS = "read:documents"
    WRITE_DOCUMENTS = "write:documents"
    DELETE_DOCUMENTS = "delete:documents"
    READ_USERS = "read:users"
    WRITE_USERS = "write:users"
    READ_METRICS = "read:metrics"
    WRITE_METRICS = "write:metrics"
    ADMIN_ACCESS = "admin:access"


class TokenPayload(BaseModel):
    """JWT token payload."""
    sub: str  # Subject (user ID)
    email: Optional[str] = None
    role: str = UserRole.USER
    permissions: List[str] = []
    exp: datetime
    iat: datetime
    iss: str = "ai-brain"


class AuthContext(BaseModel):
    """Authentication context for requests."""
    user_id: UUID
    email: Optional[str]
    role: str
    permissions: List[str]
    is_authenticated: bool = True
    is_admin: bool = False
    
    def __init__(self, **data):
        super().__init__(**data)
        self.is_admin = self.role == UserRole.ADMIN


class AuthService:
    """Authentication and authorization service."""
    
    def __init__(self):
        self.settings = get_settings()
        self.security = HTTPBearer(auto_error=False)
        
        # Role-based permissions mapping
        self.role_permissions = {
            UserRole.USER: [
                Permission.READ_CONVERSATIONS,
                Permission.WRITE_CONVERSATIONS,
                Permission.DELETE_CONVERSATIONS,
                Permission.READ_DOCUMENTS,
                Permission.WRITE_DOCUMENTS,
            ],
            UserRole.MODERATOR: [
                Permission.READ_CONVERSATIONS,
                Permission.WRITE_CONVERSATIONS,
                Permission.DELETE_CONVERSATIONS,
                Permission.READ_DOCUMENTS,
                Permission.WRITE_DOCUMENTS,
                Permission.DELETE_DOCUMENTS,
                Permission.READ_USERS,
            ],
            UserRole.ADMIN: [
                Permission.READ_CONVERSATIONS,
                Permission.WRITE_CONVERSATIONS,
                Permission.DELETE_CONVERSATIONS,
                Permission.READ_DOCUMENTS,
                Permission.WRITE_DOCUMENTS,
                Permission.DELETE_DOCUMENTS,
                Permission.READ_USERS,
                Permission.WRITE_USERS,
                Permission.READ_METRICS,
                Permission.WRITE_METRICS,
                Permission.ADMIN_ACCESS,
            ]
        }
    
    def create_access_token(
        self, 
        user_id: str, 
        email: Optional[str] = None,
        role: str = UserRole.USER,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                hours=self.settings.security.jwt_expiration_hours
            )
        
        permissions = self.role_permissions.get(role, [])
        
        payload = {
            "sub": user_id,
            "email": email,
            "role": role,
            "permissions": permissions,
            "exp": expire,
            "iat": datetime.utcnow(),
            "iss": "ai-brain"
        }
        
        return jwt.encode(
            payload,
            self.settings.security.jwt_secret_key,
            algorithm=self.settings.security.jwt_algorithm
        )
    
    def verify_token(self, token: str) -> TokenPayload:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(
                token,
                self.settings.security.jwt_secret_key,
                algorithms=[self.settings.security.jwt_algorithm]
            )
            
            return TokenPayload(**payload)
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    def verify_supabase_token(self, token: str) -> TokenPayload:
        """Verify Supabase JWT token."""
        try:
            # Decode without verification first to get the header
            unverified_header = jwt.get_unverified_header(token)
            
            # For Supabase tokens, we need to use the JWT secret from Supabase
            if self.settings.supabase.jwt_secret:
                payload = jwt.decode(
                    token,
                    self.settings.supabase.jwt_secret,
                    algorithms=["HS256"]
                )
            else:
                # Fallback to our own secret for development
                payload = jwt.decode(
                    token,
                    self.settings.security.jwt_secret_key,
                    algorithms=[self.settings.security.jwt_algorithm]
                )
            
            # Extract user information from Supabase token
            user_id = payload.get("sub")
            email = payload.get("email")
            role = payload.get("user_metadata", {}).get("role", UserRole.USER)
            
            return TokenPayload(
                sub=user_id,
                email=email,
                role=role,
                permissions=self.role_permissions.get(role, []),
                exp=datetime.fromtimestamp(payload["exp"]),
                iat=datetime.fromtimestamp(payload["iat"])
            )
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.JWTError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Could not validate Supabase token: {str(e)}",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    async def get_current_user(
        self, 
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
    ) -> Optional[AuthContext]:
        """Get current authenticated user from request."""
        if not credentials:
            return None
        
        try:
            # Try to verify as Supabase token first
            if self.settings.supabase.is_configured():
                try:
                    token_payload = self.verify_supabase_token(credentials.credentials)
                except HTTPException:
                    # Fallback to our own token verification
                    token_payload = self.verify_token(credentials.credentials)
            else:
                token_payload = self.verify_token(credentials.credentials)
            
            return AuthContext(
                user_id=UUID(token_payload.sub),
                email=token_payload.email,
                role=token_payload.role,
                permissions=token_payload.permissions
            )
            
        except HTTPException:
            return None
    
    async def require_auth(
        self, 
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer())
    ) -> AuthContext:
        """Require authentication for endpoint."""
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        auth_context = await self.get_current_user(credentials)
        if not auth_context:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return auth_context
    
    def require_permission(self, permission: str):
        """Decorator to require specific permission."""
        async def permission_checker(
            auth_context: AuthContext = Depends(self.require_auth)
        ) -> AuthContext:
            if permission not in auth_context.permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission required: {permission}"
                )
            return auth_context
        
        return permission_checker
    
    def require_role(self, required_role: str):
        """Decorator to require specific role."""
        async def role_checker(
            auth_context: AuthContext = Depends(self.require_auth)
        ) -> AuthContext:
            role_hierarchy = {
                UserRole.USER: 1,
                UserRole.MODERATOR: 2,
                UserRole.ADMIN: 3
            }
            
            user_level = role_hierarchy.get(auth_context.role, 0)
            required_level = role_hierarchy.get(required_role, 999)
            
            if user_level < required_level:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role required: {required_role}"
                )
            
            return auth_context
        
        return role_checker


# Global auth service instance
auth_service = AuthService()


# Dependency functions for FastAPI
async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[AuthContext]:
    """Get current user dependency."""
    return await auth_service.get_current_user(credentials)


async def require_auth(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer())
) -> AuthContext:
    """Require authentication dependency."""
    return await auth_service.require_auth(credentials)


def require_permission(permission: str):
    """Require permission dependency."""
    return auth_service.require_permission(permission)


def require_role(role: str):
    """Require role dependency."""
    return auth_service.require_role(role)


# Convenience dependencies
require_admin = require_role(UserRole.ADMIN)
require_moderator = require_role(UserRole.MODERATOR)
