"""
Enhanced configuration management for AI Brain Foundation.
Supports environment-specific settings, validation, and hot-reloading.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from enum import Enum

from pydantic import BaseModel, Field, validator, root_validator
from pydantic_settings import BaseSettings

logger = logging.getLogger(__name__)


class Environment(str, Enum):
    """Deployment environments."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class LogLevel(str, Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DatabaseConfig(BaseModel):
    """Database configuration."""
    url: Optional[str] = Field(None, env="DATABASE_URL")
    host: Optional[str] = Field(None, env="DB_HOST")
    port: int = Field(5432, env="DB_PORT")
    name: str = Field("postgres", env="DB_NAME")
    username: str = Field("postgres", env="DB_USERNAME")
    password: Optional[str] = Field(None, env="DB_PASSWORD")
    min_connections: int = Field(5, env="DB_MIN_CONNECTIONS")
    max_connections: int = Field(20, env="DB_MAX_CONNECTIONS")
    command_timeout: int = Field(60, env="DB_COMMAND_TIMEOUT")
    ssl_mode: str = Field("prefer", env="DB_SSL_MODE")
    
    @root_validator
    def validate_database_config(cls, values):
        """Validate database configuration."""
        url = values.get('url')
        host = values.get('host')
        
        if not url and not host:
            raise ValueError("Either DATABASE_URL or DB_HOST must be provided")
        
        return values


class SupabaseConfig(BaseModel):
    """Supabase configuration."""
    url: Optional[str] = Field(None, env="SUPABASE_URL")
    anon_key: Optional[str] = Field(None, env="SUPABASE_ANON_KEY")
    service_role_key: Optional[str] = Field(None, env="SUPABASE_SERVICE_ROLE_KEY")
    jwt_secret: Optional[str] = Field(None, env="SUPABASE_JWT_SECRET")
    
    def is_configured(self) -> bool:
        """Check if Supabase is properly configured."""
        return bool(self.url and self.anon_key)


class RedisConfig(BaseModel):
    """Redis configuration."""
    url: Optional[str] = Field(None, env="REDIS_URL")
    host: str = Field("localhost", env="REDIS_HOST")
    port: int = Field(6379, env="REDIS_PORT")
    password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    db: int = Field(0, env="REDIS_DB")
    max_connections: int = Field(10, env="REDIS_MAX_CONNECTIONS")
    
    def get_connection_url(self) -> str:
        """Get Redis connection URL."""
        if self.url:
            return self.url
        
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.db}"


class ProviderConfig(BaseModel):
    """AI provider configuration."""
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    enabled: bool = True
    priority: int = 1
    rate_limit_rpm: Optional[int] = None
    rate_limit_tpm: Optional[int] = None
    timeout: float = 30.0
    max_retries: int = 3
    models: List[str] = Field(default_factory=list)


class MonitoringConfig(BaseModel):
    """Monitoring and observability configuration."""
    enabled: bool = Field(True, env="MONITORING_ENABLED")
    metrics_endpoint: str = Field("/metrics", env="METRICS_ENDPOINT")
    health_endpoint: str = Field("/health", env="HEALTH_ENDPOINT")
    log_level: LogLevel = Field(LogLevel.INFO, env="LOG_LEVEL")
    structured_logging: bool = Field(True, env="STRUCTURED_LOGGING")
    trace_sampling_rate: float = Field(0.1, env="TRACE_SAMPLING_RATE")
    
    # External monitoring services
    sentry_dsn: Optional[str] = Field(None, env="SENTRY_DSN")
    datadog_api_key: Optional[str] = Field(None, env="DATADOG_API_KEY")
    prometheus_enabled: bool = Field(False, env="PROMETHEUS_ENABLED")


class SecurityConfig(BaseModel):
    """Security configuration."""
    jwt_secret_key: str = Field(..., env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field("HS256", env="JWT_ALGORITHM")
    jwt_expiration_hours: int = Field(24, env="JWT_EXPIRATION_HOURS")
    
    # API security
    api_key_header: str = Field("X-API-Key", env="API_KEY_HEADER")
    rate_limit_enabled: bool = Field(True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests: int = Field(100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # CORS settings
    cors_origins: List[str] = Field(default_factory=lambda: ["*"], env="CORS_ORIGINS")
    cors_methods: List[str] = Field(default_factory=lambda: ["*"], env="CORS_METHODS")
    cors_headers: List[str] = Field(default_factory=lambda: ["*"], env="CORS_HEADERS")
    
    @validator('cors_origins', 'cors_methods', 'cors_headers', pre=True)
    def parse_cors_lists(cls, v):
        """Parse CORS configuration from string or list."""
        if isinstance(v, str):
            return [item.strip() for item in v.split(',') if item.strip()]
        return v


class FeatureFlags(BaseModel):
    """Feature flags configuration."""
    enable_vector_search: bool = Field(True, env="FEATURE_VECTOR_SEARCH")
    enable_rag: bool = Field(True, env="FEATURE_RAG")
    enable_multimodal: bool = Field(True, env="FEATURE_MULTIMODAL")
    enable_workflows: bool = Field(True, env="FEATURE_WORKFLOWS")
    enable_analytics: bool = Field(True, env="FEATURE_ANALYTICS")
    enable_caching: bool = Field(True, env="FEATURE_CACHING")
    enable_rate_limiting: bool = Field(True, env="FEATURE_RATE_LIMITING")


class EnhancedSettings(BaseSettings):
    """Enhanced application settings with comprehensive configuration."""
    
    # Environment and basic settings
    environment: Environment = Field(Environment.DEVELOPMENT, env="ENVIRONMENT")
    debug: bool = Field(False, env="DEBUG")
    app_name: str = Field("AI Brain Foundation", env="APP_NAME")
    app_version: str = Field("1.0.0", env="APP_VERSION")
    host: str = Field("0.0.0.0", env="HOST")
    port: int = Field(8000, env="PORT")
    
    # Configuration sections
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    supabase: SupabaseConfig = Field(default_factory=SupabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    features: FeatureFlags = Field(default_factory=FeatureFlags)
    
    # Provider configurations
    providers: Dict[str, ProviderConfig] = Field(default_factory=dict)
    
    # AI settings
    default_model: str = Field("gpt-4o-mini", env="DEFAULT_MODEL")
    max_tokens: int = Field(1000, env="MAX_TOKENS")
    temperature: float = Field(0.7, env="TEMPERATURE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_nested_delimiter = "__"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._load_provider_configs()
    
    def _load_provider_configs(self):
        """Load provider configurations from environment variables."""
        provider_keys = {
            "openai": os.getenv("OPENAI_API_KEY"),
            "anthropic": os.getenv("ANTHROPIC_API_KEY"),
            "google": os.getenv("GOOGLE_API_KEY"),
            "groq": os.getenv("GROQ_API_KEY"),
            "openrouter": os.getenv("OPENROUTER_API_KEY"),
            "deepseek": os.getenv("DEEPSEEK_API_KEY"),
        }
        
        for provider, api_key in provider_keys.items():
            if api_key:
                self.providers[provider] = ProviderConfig(
                    api_key=api_key,
                    enabled=True,
                    priority=1 if provider == "openai" else 2
                )
    
    def validate_configuration(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Check required configurations
        if not self.providers:
            errors.append("No AI provider API keys configured")
        
        if not self.security.jwt_secret_key:
            errors.append("JWT_SECRET_KEY is required")
        
        if not self.database.url and not self.database.host:
            errors.append("Database configuration is incomplete")
        
        return errors
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == Environment.PRODUCTION
    
    def get_database_url(self) -> str:
        """Get complete database URL."""
        if self.database.url:
            return self.database.url
        
        # Construct URL from components
        auth = f"{self.database.username}:{self.database.password}"
        host_port = f"{self.database.host}:{self.database.port}"
        return f"postgresql://{auth}@{host_port}/{self.database.name}"


# Global settings instance
settings: Optional[EnhancedSettings] = None


def get_settings() -> EnhancedSettings:
    """Get application settings singleton."""
    global settings
    if settings is None:
        settings = EnhancedSettings()
    return settings


def reload_settings() -> EnhancedSettings:
    """Reload settings from environment."""
    global settings
    settings = EnhancedSettings()
    logger.info("Configuration reloaded")
    return settings
