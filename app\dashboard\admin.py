"""
FastAPI-Admin dashboard configuration for AI backend management.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import FastAPI, Depends, HTTPException
from fastapi_admin.app import app as admin_app
from fastapi_admin.enums import Method
from fastapi_admin.file_upload import FileUpload
from fastapi_admin.resources import Field, Link, Model, ToolbarAction, Action
from fastapi_admin.widgets import displays, filters, inputs
from starlette.requests import Request
from starlette.responses import Response
from tortoise.contrib.fastapi import register_tortoise

from ..core.database import (
    ModelConfig, Provider, RoutingPolicy, UsageLog,
    ModelConfigSchema, ProviderSchema, RoutingPolicySchema, UsageLogSchema
)
from ..services.router_service import router_service
from ..services.task_analyzer import complexity_analyzer, TaskComplexity


logger = logging.getLogger(__name__)


# Admin Resources
class ModelConfigResource(Model):
    label = "Model Configurations"
    model = ModelConfig
    icon = "fas fa-cogs"
    page_pre_title = "AI Models"
    page_title = "Model Configuration Management"
    filters = [
        filters.Search(name="model_name", label="Model Name", search_mode="contains"),
        filters.Enum(enum=TaskComplexity, name="tier", label="Tier"),
        filters.Boolean(name="is_available", label="Available"),
    ]
    fields = [
        Field(name="id", label="ID", display=displays.InputOnly()),
        Field(name="model_name", label="Model Name", input_=inputs.Input()),
        Field(name="provider_id", label="Provider", input_=inputs.ForeignKey(Provider)),
        Field(name="tier", label="Tier", input_=inputs.Enum(enum=TaskComplexity)),
        Field(name="cost_per_1k_input", label="Cost per 1K Input Tokens", input_=inputs.Number(step=0.0001)),
        Field(name="cost_per_1k_output", label="Cost per 1K Output Tokens", input_=inputs.Number(step=0.0001)),
        Field(name="max_context_tokens", label="Max Context Tokens", input_=inputs.Number()),
        Field(name="supports_vision", label="Supports Vision", input_=inputs.Switch()),
        Field(name="supports_function_calling", label="Supports Function Calling", input_=inputs.Switch()),
        Field(name="is_available", label="Available", input_=inputs.Switch()),
        Field(name="created_at", label="Created", display=displays.DatetimeDisplay()),
        Field(name="updated_at", label="Updated", display=displays.DatetimeDisplay()),
    ]


class ProviderResource(Model):
    label = "AI Providers"
    model = Provider
    icon = "fas fa-cloud"
    page_pre_title = "Infrastructure"
    page_title = "AI Provider Management"
    filters = [
        filters.Search(name="name", label="Provider Name", search_mode="contains"),
        filters.Boolean(name="is_active", label="Active"),
    ]
    fields = [
        Field(name="id", label="ID", display=displays.InputOnly()),
        Field(name="name", label="Provider Name", input_=inputs.Input()),
        Field(name="api_base_url", label="API Base URL", input_=inputs.Input()),
        Field(name="api_key_env_var", label="API Key Environment Variable", input_=inputs.Input()),
        Field(name="rate_limit_rpm", label="Rate Limit (RPM)", input_=inputs.Number()),
        Field(name="rate_limit_tpm", label="Rate Limit (TPM)", input_=inputs.Number()),
        Field(name="is_active", label="Active", input_=inputs.Switch()),
        Field(name="created_at", label="Created", display=displays.DatetimeDisplay()),
        Field(name="updated_at", label="Updated", display=displays.DatetimeDisplay()),
    ]


class RoutingPolicyResource(Model):
    label = "Routing Policies"
    model = RoutingPolicy
    icon = "fas fa-route"
    page_pre_title = "Intelligence"
    page_title = "Model Routing Policy Management"
    filters = [
        filters.Enum(enum=TaskComplexity, name="task_class", label="Task Class"),
        filters.Boolean(name="is_active", label="Active"),
    ]
    fields = [
        Field(name="id", label="ID", display=displays.InputOnly()),
        Field(name="task_class", label="Task Class", input_=inputs.Enum(enum=TaskComplexity)),
        Field(name="score_threshold", label="Score Threshold", input_=inputs.Number(step=0.01, min_=0, max_=1)),
        Field(name="preferred_models", label="Preferred Models", input_=inputs.JSON()),
        Field(name="fallback_models", label="Fallback Models", input_=inputs.JSON()),
        Field(name="is_active", label="Active", input_=inputs.Switch()),
        Field(name="created_at", label="Created", display=displays.DatetimeDisplay()),
        Field(name="updated_at", label="Updated", display=displays.DatetimeDisplay()),
    ]


class UsageLogResource(Model):
    label = "Usage Logs"
    model = UsageLog
    icon = "fas fa-chart-line"
    page_pre_title = "Analytics"
    page_title = "Usage Analytics & Monitoring"
    filters = [
        filters.Search(name="model_name", label="Model Name", search_mode="contains"),
        filters.Search(name="provider_name", label="Provider", search_mode="contains"),
        filters.Date(name="timestamp", label="Date"),
        filters.Enum(enum=TaskComplexity, name="task_complexity", label="Task Complexity"),
    ]
    fields = [
        Field(name="id", label="ID", display=displays.InputOnly()),
        Field(name="model_name", label="Model", display=displays.Display()),
        Field(name="provider_name", label="Provider", display=displays.Display()),
        Field(name="task_complexity", label="Task Complexity", display=displays.Display()),
        Field(name="input_tokens", label="Input Tokens", display=displays.Display()),
        Field(name="output_tokens", label="Output Tokens", display=displays.Display()),
        Field(name="total_cost", label="Total Cost ($)", display=displays.Display()),
        Field(name="response_time_ms", label="Response Time (ms)", display=displays.Display()),
        Field(name="timestamp", label="Timestamp", display=displays.DatetimeDisplay()),
        Field(name="metadata", label="Metadata", display=displays.Json()),
    ]


# Custom Actions
async def refresh_router_cache(request: Request) -> Response:
    """Refresh the router service cache."""
    try:
        router_service._refresh_cache()
        return Response("Router cache refreshed successfully", status_code=200)
    except Exception as e:
        logger.error(f"Failed to refresh router cache: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def test_model_availability(request: Request) -> Response:
    """Test availability of all configured models."""
    try:
        # This would test actual model availability
        # For now, return a mock response
        results = {
            "tested_models": 5,
            "available_models": 4,
            "unavailable_models": 1,
            "test_timestamp": datetime.now().isoformat()
        }
        return Response(f"Model availability test completed: {results}", status_code=200)
    except Exception as e:
        logger.error(f"Failed to test model availability: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def analyze_sample_task(request: Request) -> Response:
    """Analyze a sample task to test the complexity analyzer."""
    try:
        sample_task = "Write a Python function to calculate the Fibonacci sequence using dynamic programming"
        result = complexity_analyzer.analyze(sample_task)
        
        response_data = {
            "sample_task": sample_task,
            "complexity_class": result.task_class.value,
            "complexity_score": result.score,
            "reasoning": result.reason
        }
        
        return Response(f"Sample task analysis: {response_data}", status_code=200)
    except Exception as e:
        logger.error(f"Failed to analyze sample task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Toolbar Actions
toolbar_actions = [
    ToolbarAction(
        label="Refresh Router Cache",
        icon="fas fa-sync",
        name="refresh_cache",
        method=Method.GET,
        ajax=True,
        action=refresh_router_cache,
    ),
    ToolbarAction(
        label="Test Model Availability",
        icon="fas fa-heartbeat",
        name="test_models",
        method=Method.GET,
        ajax=True,
        action=test_model_availability,
    ),
    ToolbarAction(
        label="Test Complexity Analyzer",
        icon="fas fa-brain",
        name="test_analyzer",
        method=Method.GET,
        ajax=True,
        action=analyze_sample_task,
    ),
]


# Dashboard Configuration
def setup_admin_dashboard(app: FastAPI):
    """
    Setup the FastAPI-Admin dashboard.
    
    Args:
        app: FastAPI application instance
    """
    
    # Mount admin app
    app.mount("/admin", admin_app)
    
    # Configure admin
    admin_app.add_admin_resource(ModelConfigResource)
    admin_app.add_admin_resource(ProviderResource)
    admin_app.add_admin_resource(RoutingPolicyResource)
    admin_app.add_admin_resource(UsageLogResource)
    
    # Add toolbar actions
    for action in toolbar_actions:
        admin_app.add_toolbar_action(action)
    
    # Configure admin settings
    admin_app.configure(
        logo_url="https://preview.tabler.io/static/logo-white.svg",
        template_folders=[],
        favicon_url="https://raw.githubusercontent.com/fastapi-admin/fastapi-admin/dev/images/favicon.png",
        default_locale="en-US",
        language_switch=True,
        admin_secret_key="your-secret-key-here",  # Change in production
    )
    
    logger.info("FastAPI-Admin dashboard configured successfully")


# Dashboard Analytics Functions
async def get_usage_analytics(days: int = 30) -> Dict[str, Any]:
    """
    Get usage analytics for the dashboard.
    
    Args:
        days: Number of days to analyze
        
    Returns:
        Dictionary with analytics data
    """
    try:
        # This would query the actual database
        # For now, return mock analytics
        analytics = {
            "total_requests": 1250,
            "total_cost": 15.75,
            "avg_response_time": 850,
            "most_used_model": "gemini-2.0-flash",
            "cost_savings": 45.2,  # Percentage saved by using free models
            "complexity_distribution": {
                "very_easy": 25,
                "easy": 35,
                "medium": 25,
                "hard": 12,
                "very_hard": 3
            },
            "provider_usage": {
                "google": 45,
                "groq": 30,
                "openrouter": 15,
                "openai": 8,
                "anthropic": 2
            },
            "daily_requests": [45, 52, 38, 67, 55, 48, 62]  # Last 7 days
        }
        
        return analytics
        
    except Exception as e:
        logger.error(f"Failed to get usage analytics: {e}")
        return {}


async def get_model_performance_metrics() -> Dict[str, Any]:
    """
    Get model performance metrics.
    
    Returns:
        Dictionary with performance metrics
    """
    try:
        # This would analyze actual performance data
        # For now, return mock metrics
        metrics = {
            "models": [
                {
                    "name": "gemini-2.0-flash",
                    "provider": "google",
                    "avg_response_time": 750,
                    "success_rate": 99.2,
                    "cost_per_request": 0.0,
                    "usage_count": 450
                },
                {
                    "name": "llama-3.3-70b",
                    "provider": "groq",
                    "avg_response_time": 650,
                    "success_rate": 98.8,
                    "cost_per_request": 0.0,
                    "usage_count": 320
                },
                {
                    "name": "gpt-4o",
                    "provider": "openai",
                    "avg_response_time": 1200,
                    "success_rate": 99.8,
                    "cost_per_request": 0.045,
                    "usage_count": 85
                }
            ],
            "overall_metrics": {
                "avg_response_time": 825,
                "overall_success_rate": 99.1,
                "total_cost_savings": 78.5,
                "free_model_usage_percentage": 85.2
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get model performance metrics: {e}")
        return {}
