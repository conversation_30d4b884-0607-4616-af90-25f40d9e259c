"""
Authentication middleware for FastAPI using Supabase Auth.
"""

import logging
from typing import Optional, Callable, List
from fastapi import Request, HTTPException, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from ..services.auth_service import get_auth_service, UserProfile, SupabaseAuthError

logger = logging.getLogger(__name__)

# Security scheme for Bearer token
security = HTTPBearer(auto_error=False)


class AuthMiddleware(BaseHTTPMiddleware):
    """Authentication middleware for protecting routes."""
    
    def __init__(self, app, protected_paths: Optional[List[str]] = None):
        """
        Initialize auth middleware.
        
        Args:
            app: FastAPI application
            protected_paths: List of path prefixes that require authentication
        """
        super().__init__(app)
        self.protected_paths = protected_paths or [
            "/api/protected",
            "/api/user",
            "/api/conversations",
            "/api/files"
        ]
        self.auth_service = get_auth_service()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and check authentication if needed."""
        
        # Check if path requires authentication
        if not self._requires_auth(request.url.path):
            return await call_next(request)
        
        # Extract token from Authorization header
        token = self._extract_token(request)
        
        if not token:
            return self._unauthorized_response("Missing authentication token")
        
        # Validate token and get user
        try:
            user = await self.auth_service.get_user(token)
            if not user:
                return self._unauthorized_response("Invalid authentication token")
            
            # Add user to request state
            request.state.user = user
            request.state.access_token = token
            
        except SupabaseAuthError as e:
            return self._unauthorized_response(str(e))
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return self._unauthorized_response("Authentication failed")
        
        return await call_next(request)
    
    def _requires_auth(self, path: str) -> bool:
        """Check if a path requires authentication."""
        return any(path.startswith(protected_path) for protected_path in self.protected_paths)
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """Extract Bearer token from Authorization header."""
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return None
        
        if not auth_header.startswith("Bearer "):
            return None
        
        return auth_header[7:]  # Remove "Bearer " prefix
    
    def _unauthorized_response(self, message: str) -> Response:
        """Return unauthorized response."""
        from fastapi.responses import JSONResponse
        return JSONResponse(
            status_code=401,
            content={"error": "Unauthorized", "detail": message}
        )


# Dependency for getting current user
async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> UserProfile:
    """
    Dependency to get the current authenticated user.
    
    Usage:
        @app.get("/api/user/profile")
        async def get_profile(user: UserProfile = Depends(get_current_user)):
            return user
    """
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Missing authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    auth_service = get_auth_service()
    
    try:
        user = await auth_service.get_user(credentials.credentials)
        if not user:
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user
    
    except SupabaseAuthError as e:
        raise HTTPException(
            status_code=401,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=401,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


# Optional dependency for getting current user (doesn't raise error if not authenticated)
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[UserProfile]:
    """
    Optional dependency to get the current authenticated user.
    Returns None if not authenticated instead of raising an error.
    
    Usage:
        @app.get("/api/public-or-private")
        async def endpoint(user: Optional[UserProfile] = Depends(get_current_user_optional)):
            if user:
                return {"message": f"Hello {user.email}"}
            else:
                return {"message": "Hello anonymous user"}
    """
    if not credentials:
        return None
    
    auth_service = get_auth_service()
    
    try:
        return await auth_service.get_user(credentials.credentials)
    except Exception as e:
        logger.warning(f"Optional authentication failed: {e}")
        return None


# Dependency for getting access token
async def get_access_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """
    Dependency to get the current access token.
    
    Usage:
        @app.get("/api/token-info")
        async def get_token_info(token: str = Depends(get_access_token)):
            return {"token": token}
    """
    return credentials.credentials


# Role-based access control decorator
def require_role(required_role: str):
    """
    Decorator for role-based access control.
    
    Usage:
        @app.get("/api/admin/users")
        @require_role("admin")
        async def list_users(user: UserProfile = Depends(get_current_user)):
            return {"users": []}
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get user from kwargs (should be injected by get_current_user dependency)
            user = None
            for arg in args:
                if isinstance(arg, UserProfile):
                    user = arg
                    break
            
            if not user:
                for value in kwargs.values():
                    if isinstance(value, UserProfile):
                        user = value
                        break
            
            if not user:
                raise HTTPException(
                    status_code=401,
                    detail="User not found in request"
                )
            
            # Check user role (assuming role is stored in metadata)
            user_role = user.metadata.get("role") if user.metadata else None
            
            if user_role != required_role:
                raise HTTPException(
                    status_code=403,
                    detail=f"Insufficient permissions. Required role: {required_role}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


# Utility function to check if user has permission
def has_permission(user: UserProfile, permission: str) -> bool:
    """
    Check if user has a specific permission.
    
    Args:
        user: User profile
        permission: Permission string to check
    
    Returns:
        True if user has permission, False otherwise
    """
    if not user.metadata:
        return False
    
    user_permissions = user.metadata.get("permissions", [])
    user_role = user.metadata.get("role")
    
    # Check direct permission
    if permission in user_permissions:
        return True
    
    # Check role-based permissions (you can customize this logic)
    role_permissions = {
        "admin": ["read", "write", "delete", "manage_users"],
        "user": ["read", "write"],
        "viewer": ["read"]
    }
    
    if user_role and user_role in role_permissions:
        return permission in role_permissions[user_role]
    
    return False
