"""
Enhanced authentication middleware for AI Brain Foundation.
"""

import time
import uuid
import logging
from typing import List, Optional, Dict, Any

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_401_UNAUTHORIZED, HTTP_403_FORBIDDEN, HTTP_429_TOO_MANY_REQUESTS

from ..core.auth import auth_service, AuthContext
from ..core.config import get_settings

logger = logging.getLogger(__name__)


class EnhancedAuthMiddleware(BaseHTTPMiddleware):
    """Enhanced authentication middleware with rate limiting and logging."""
    
    def __init__(
        self, 
        app, 
        protected_paths: List[str] = None,
        public_paths: List[str] = None,
        rate_limit_enabled: bool = True
    ):
        super().__init__(app)
        self.protected_paths = protected_paths or ["/api/"]
        self.public_paths = public_paths or [
            "/", "/health", "/docs", "/redoc", "/openapi.json", "/metrics"
        ]
        self.settings = get_settings()
        self.rate_limit_enabled = rate_limit_enabled
        
        # Simple in-memory rate limiting (use Redis in production)
        self.rate_limit_store: Dict[str, List[float]] = {}
    
    async def dispatch(self, request: Request, call_next):
        """Process request through enhanced authentication middleware."""
        start_time = time.time()
        request_id = str(uuid.uuid4())
        
        # Add request ID to headers
        request.state.request_id = request_id
        
        try:
            # Check rate limiting first
            if self.rate_limit_enabled and self._is_rate_limited(request):
                return JSONResponse(
                    status_code=HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": "Rate limit exceeded",
                        "request_id": request_id
                    }
                )
            
            # Check if path requires authentication
            if self._is_public_path(request.url.path):
                response = await call_next(request)
            elif self._requires_auth(request.url.path):
                auth_context = await self._authenticate_request(request)
                if not auth_context:
                    return JSONResponse(
                        status_code=HTTP_401_UNAUTHORIZED,
                        content={
                            "error": "Authentication required",
                            "request_id": request_id
                        }
                    )
                
                # Add auth context to request state
                request.state.auth_context = auth_context
                request.state.user_id = auth_context.user_id
                request.state.user_email = auth_context.email
                request.state.user_role = auth_context.role
                
                response = await call_next(request)
            else:
                response = await call_next(request)
            
            # Add security headers
            self._add_security_headers(response)
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            # Log request
            await self._log_request(request, response, start_time)
            
            return response
            
        except Exception as e:
            logger.error(f"Middleware error: {e}", extra={"request_id": request_id})
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "request_id": request_id
                }
            )
    
    def _is_public_path(self, path: str) -> bool:
        """Check if path is public (no auth required)."""
        return any(path.startswith(public_path) for public_path in self.public_paths)
    
    def _requires_auth(self, path: str) -> bool:
        """Check if path requires authentication."""
        if self._is_public_path(path):
            return False
        return any(path.startswith(protected_path) for protected_path in self.protected_paths)
    
    async def _authenticate_request(self, request: Request) -> Optional[AuthContext]:
        """Authenticate request and return auth context."""
        try:
            # Extract token from Authorization header
            auth_header = request.headers.get("Authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.split(" ")[1]
            
            # Use auth service to verify token
            if self.settings.supabase.is_configured():
                token_payload = auth_service.verify_supabase_token(token)
            else:
                token_payload = auth_service.verify_token(token)
            
            return AuthContext(
                user_id=token_payload.sub,
                email=token_payload.email,
                role=token_payload.role,
                permissions=token_payload.permissions
            )
            
        except Exception as e:
            logger.warning(f"Authentication failed: {e}")
            return None
    
    def _is_rate_limited(self, request: Request) -> bool:
        """Check if request should be rate limited."""
        if not self.settings.security.rate_limit_enabled:
            return False
        
        # Use IP address as rate limit key
        client_ip = self._get_client_ip(request)
        current_time = time.time()
        window = self.settings.security.rate_limit_window
        max_requests = self.settings.security.rate_limit_requests
        
        # Clean old entries
        if client_ip in self.rate_limit_store:
            self.rate_limit_store[client_ip] = [
                timestamp for timestamp in self.rate_limit_store[client_ip]
                if current_time - timestamp < window
            ]
        else:
            self.rate_limit_store[client_ip] = []
        
        # Check if rate limit exceeded
        if len(self.rate_limit_store[client_ip]) >= max_requests:
            return True
        
        # Add current request
        self.rate_limit_store[client_ip].append(current_time)
        return False
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response."""
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        if self.settings.is_production():
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    async def _log_request(self, request: Request, response: Response, start_time: float):
        """Log request details for observability."""
        duration_ms = int((time.time() - start_time) * 1000)
        
        log_data = {
            "request_id": getattr(request.state, "request_id", "unknown"),
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "duration_ms": duration_ms,
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("User-Agent", ""),
            "user_id": getattr(request.state, "user_id", None),
        }
        
        if response.status_code >= 400:
            logger.warning("Request failed", extra=log_data)
        else:
            logger.info("Request completed", extra=log_data)
