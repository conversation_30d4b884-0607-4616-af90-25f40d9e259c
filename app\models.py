"""
Pydantic models for request/response validation and AI agent configuration.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator


class MessageRole(str, Enum):
    """Enum for message roles in conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class ChatMessage(BaseModel):
    """Model for a single chat message."""
    role: MessageRole
    content: str
    timestamp: Optional[datetime] = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ChatRequest(BaseModel):
    """Request model for chat completion with multi-provider support."""
    message: str = Field(..., description="The user's message")
    conversation_id: Optional[str] = Field(None, description="Optional conversation ID for context")
    system_prompt: Optional[str] = Field(None, description="Optional system prompt override")
    max_tokens: Optional[int] = Field(1000, ge=1, le=4000, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Temperature for response generation")
    use_tools: bool = Field(True, description="Whether to enable function calling")
    provider: Optional[str] = Field(None, description="AI provider to use (openai, anthropic, google, groq, etc.)")
    model: Optional[str] = Field(None, description="Specific model name to use")
    use_fallback: Optional[bool] = Field(None, description="Whether to enable fallback providers for this request")

    @validator('message')
    def message_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Message cannot be empty')
        return v

    @validator('provider')
    def validate_provider(cls, v):
        if v is not None:
            valid_providers = ['openai', 'anthropic', 'google', 'groq', 'mistral', 'cohere', 'huggingface', 'openrouter', 'deepseek', 'together']
            if v.lower() not in valid_providers:
                raise ValueError(f'Provider must be one of: {", ".join(valid_providers)}')
            return v.lower()
        return v


class ContextPromptRequest(BaseModel):
    """Request model for context-prompt chat completion with structured input."""
    context: str = Field(..., description="Background information/context for the AI")
    prompt: str = Field(..., description="User's question/request")
    provider: Optional[str] = Field(None, description="AI provider to use (google, groq, etc.). Defaults to google with groq fallback")
    max_tokens: Optional[int] = Field(1000, ge=1, le=4000, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Temperature for response generation")
    conversation_id: Optional[str] = Field(None, description="Optional conversation ID for context")
    use_tools: bool = Field(True, description="Whether to enable function calling")

    @validator('context')
    def context_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Context cannot be empty')
        return v

    @validator('prompt')
    def prompt_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Prompt cannot be empty')
        return v

    @validator('provider')
    def validate_provider(cls, v):
        if v is not None:
            valid_providers = ['openai', 'anthropic', 'google', 'groq', 'mistral', 'cohere', 'huggingface', 'openrouter', 'deepseek', 'together']
            if v.lower() not in valid_providers:
                raise ValueError(f'Provider must be one of: {", ".join(valid_providers)}')
            return v.lower()
        return v


class ChatResponse(BaseModel):
    """Response model for chat completion."""
    response: str
    conversation_id: str
    timestamp: datetime = Field(default_factory=datetime.now)
    model_used: str
    tokens_used: Optional[int] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ToolCall(BaseModel):
    """Model for function/tool calls."""
    name: str
    arguments: Dict[str, Any]
    result: Optional[Any] = None
    error: Optional[str] = None


class AgentConfig(BaseModel):
    """Configuration for AI agent with multi-provider support."""
    model: str = Field("gemini-1.5-flash", description="AI model to use")
    provider: Optional[str] = Field(None, description="AI provider to use (openai, anthropic, google, groq, etc.)")
    use_fallback: bool = Field(True, description="Whether to use fallback providers")
    prefer_free_models: bool = Field(True, description="Whether to prefer free/low-cost models")
    system_prompt: str = Field(
        "You are a helpful AI assistant built with Pydantic AI.",
        description="System prompt for the agent"
    )
    max_tokens: int = Field(1000, ge=1, le=4000)
    temperature: float = Field(0.7, ge=0.0, le=2.0)
    enable_tools: bool = Field(True, description="Whether to enable function calling")
    tools: List[str] = Field(default_factory=list, description="List of available tools")


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = "1.0.0"
    dependencies: Dict[str, str] = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    request_id: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ConversationSummary(BaseModel):
    """Model for conversation summary."""
    conversation_id: str
    message_count: int
    created_at: datetime
    last_updated: datetime
    summary: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class WeatherInfo(BaseModel):
    """Model for weather information (example tool response)."""
    location: str
    temperature: float
    description: str
    humidity: Optional[int] = None
    wind_speed: Optional[float] = None
    timestamp: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CalculationResult(BaseModel):
    """Model for calculation results (example tool response)."""
    expression: str
    result: Union[int, float, str]
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
