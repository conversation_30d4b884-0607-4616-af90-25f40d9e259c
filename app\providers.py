"""
Provider factory and model initialization for multi-provider AI support.
"""

from typing import Optional, Dict, Any, Union, List
import logging

from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.models.groq import GroqModel
from pydantic_ai.settings import ModelSettings


class SimpleFallbackModel:
    """Simple fallback model implementation that tries multiple models in sequence."""

    def __init__(self, *models):
        """Initialize with a list of models to try in order."""
        self.models = list(models)
        if not self.models:
            raise ValueError("At least one model must be provided")

    def __getattr__(self, name):
        """Delegate attribute access to the first model."""
        return getattr(self.models[0], name)

    async def __call__(self, *args, **kwargs):
        """Try each model in sequence until one succeeds."""
        last_error = None

        for i, model in enumerate(self.models):
            try:
                return await model(*args, **kwargs)
            except Exception as e:
                last_error = e
                logger.warning(f"Model {i+1}/{len(self.models)} failed: {e}")
                continue

        # If all models failed, raise the last error
        raise last_error or Exception("All fallback models failed")

from .config import (
    ProviderType, ProviderConfig, OpenAIConfig, AnthropicConfig, 
    GoogleConfig, GroqConfig, OpenRouterConfig, DeepSeekConfig,
    get_settings
)

logger = logging.getLogger(__name__)


class ProviderFactory:
    """Factory for creating AI model instances based on provider configuration."""
    
    def __init__(self):
        self.settings = get_settings()
        self.provider_configs = self.settings.get_provider_configs()
    
    def create_model(
        self, 
        provider: ProviderType, 
        model_name: Optional[str] = None,
        model_settings: Optional[ModelSettings] = None
    ) -> Union[OpenAIModel, AnthropicModel, GeminiModel, GroqModel]:
        """
        Create a model instance for the specified provider.
        
        Args:
            provider: The AI provider to use
            model_name: Specific model name (uses default if not provided)
            model_settings: Optional model settings override
            
        Returns:
            Configured model instance
            
        Raises:
            ValueError: If provider is not configured or unsupported
        """
        if provider not in self.provider_configs:
            raise ValueError(f"Provider {provider} is not configured or API key is missing")
        
        config = self.provider_configs[provider]
        
        # Use provided model name or default from config
        if not model_name:
            if self.settings.prefer_free_models:
                # Try to find a free model for this provider
                free_models = self._get_free_models_for_provider(provider)
                if free_models:
                    model_name = free_models[0]
                else:
                    model_name = config.models[0] if config.models else self.settings.default_model
            else:
                model_name = config.models[0] if config.models else self.settings.default_model
        
        # Create model settings if not provided
        if not model_settings:
            model_settings = ModelSettings(
                max_tokens=self.settings.max_tokens,
                temperature=self.settings.temperature,
                timeout=config.timeout,
                max_retries=config.max_retries
            )
        
        logger.info(f"Creating {provider} model: {model_name}")
        
        if provider == ProviderType.OPENAI:
            return self._create_openai_model(config, model_name, model_settings)
        elif provider == ProviderType.ANTHROPIC:
            return self._create_anthropic_model(config, model_name, model_settings)
        elif provider == ProviderType.GOOGLE:
            return self._create_google_model(config, model_name, model_settings)
        elif provider == ProviderType.GROQ:
            return self._create_groq_model(config, model_name, model_settings)
        elif provider == ProviderType.OPENROUTER:
            return self._create_openrouter_model(config, model_name, model_settings)
        elif provider == ProviderType.DEEPSEEK:
            return self._create_deepseek_model(config, model_name, model_settings)
        else:
            raise ValueError(f"Unsupported provider: {provider}")
    
    def create_fallback_model(
        self,
        primary_provider: Optional[ProviderType] = None,
        fallback_providers: Optional[List[ProviderType]] = None,
        model_name: Optional[str] = None,
        model_settings: Optional[ModelSettings] = None
    ) -> Union[SimpleFallbackModel, Any]:
        """
        Create a fallback model with multiple providers.
        
        Args:
            primary_provider: Primary provider to try first
            fallback_providers: List of fallback providers
            model_name: Model name to use (will adapt per provider)
            model_settings: Model settings to apply
            
        Returns:
            FallbackModel instance with configured providers
        """
        primary = primary_provider or self.settings.primary_provider
        fallbacks = fallback_providers or self.settings.fallback_providers
        
        models = []
        
        # Add primary model
        try:
            primary_model = self.create_model(primary, model_name, model_settings)
            models.append(primary_model)
            logger.info(f"Added primary model: {primary}")
        except ValueError as e:
            logger.warning(f"Could not create primary model {primary}: {e}")
        
        # Add fallback models
        for provider in fallbacks:
            if provider == primary:
                continue  # Skip if same as primary
            try:
                fallback_model = self.create_model(provider, model_name, model_settings)
                models.append(fallback_model)
                logger.info(f"Added fallback model: {provider}")
            except ValueError as e:
                logger.warning(f"Could not create fallback model {provider}: {e}")
        
        if not models:
            raise ValueError("No valid models could be created for fallback")
        
        if len(models) == 1:
            logger.warning("Only one model available, fallback not possible")
            return models[0]
        
        return SimpleFallbackModel(*models)
    
    def _create_openai_model(
        self,
        config: OpenAIConfig,
        model_name: str,
        model_settings: ModelSettings
    ) -> OpenAIModel:
        """Create OpenAI model instance."""
        return OpenAIModel(
            model_name=model_name,
            api_key=config.api_key,
            base_url=config.base_url
        )
    
    def _create_anthropic_model(
        self,
        config: AnthropicConfig,
        model_name: str,
        model_settings: ModelSettings
    ) -> AnthropicModel:
        """Create Anthropic model instance."""
        return AnthropicModel(
            model_name=model_name,
            api_key=config.api_key
        )
    
    def _create_google_model(
        self,
        config: GoogleConfig,
        model_name: str,
        model_settings: ModelSettings
    ) -> GeminiModel:
        """Create Google/Gemini model instance."""
        return GeminiModel(
            model_name=model_name,
            api_key=config.api_key
        )
    
    def _create_groq_model(
        self,
        config: GroqConfig,
        model_name: str,
        model_settings: ModelSettings
    ) -> GroqModel:
        """Create Groq model instance."""
        return GroqModel(
            model_name=model_name,
            api_key=config.api_key
        )
    
    def _create_openrouter_model(
        self,
        config: OpenRouterConfig,
        model_name: str,
        model_settings: ModelSettings
    ) -> OpenAIModel:
        """Create OpenRouter model instance (using OpenAI-compatible API)."""
        return OpenAIModel(
            model_name=model_name,
            api_key=config.api_key,
            base_url=config.base_url
        )
    
    def _create_deepseek_model(
        self,
        config: DeepSeekConfig,
        model_name: str,
        model_settings: ModelSettings
    ) -> OpenAIModel:
        """Create DeepSeek model instance."""
        return OpenAIModel(
            model_name=model_name,
            api_key=config.api_key,
            base_url=config.base_url
        )
    
    def _get_free_models_for_provider(self, provider: ProviderType) -> List[str]:
        """Get free models available for a specific provider."""
        if provider not in self.provider_configs:
            return []
        
        config = self.provider_configs[provider]
        free_models = []
        
        for model in config.models:
            if any(free_model in model for free_model in self.settings.free_models):
                free_models.append(model)
        
        return free_models
    
    def get_available_models(self, provider: Optional[ProviderType] = None) -> Dict[ProviderType, List[str]]:
        """Get available models for all or specific provider."""
        if provider:
            if provider in self.provider_configs:
                return {provider: self.provider_configs[provider].models}
            return {}
        
        return {
            p: config.models 
            for p, config in self.provider_configs.items()
        }
    
    def get_recommended_model(self, prefer_free: bool = None) -> tuple[ProviderType, str]:
        """Get recommended provider and model based on preferences."""
        prefer_free = prefer_free if prefer_free is not None else self.settings.prefer_free_models
        
        if prefer_free:
            # Look for free models first
            for provider, config in sorted(
                self.provider_configs.items(), 
                key=lambda x: x[1].priority
            ):
                free_models = self._get_free_models_for_provider(provider)
                if free_models:
                    return provider, free_models[0]
        
        # Fall back to primary provider or first available
        primary = self.settings.primary_provider
        if primary in self.provider_configs:
            config = self.provider_configs[primary]
            return primary, config.models[0] if config.models else self.settings.default_model
        
        # Use first available provider
        if self.provider_configs:
            provider, config = next(iter(self.provider_configs.items()))
            return provider, config.models[0] if config.models else self.settings.default_model
        
        raise ValueError("No providers configured")


# Global factory instance
_factory_instance: Optional[ProviderFactory] = None


def get_provider_factory() -> ProviderFactory:
    """Get the global provider factory instance."""
    global _factory_instance
    if _factory_instance is None:
        _factory_instance = ProviderFactory()
    return _factory_instance
