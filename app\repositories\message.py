"""
Message repository implementation for AI Brain Foundation.
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime

import asyncpg

from .base import BaseRepository, VectorRepository, EntityNotFoundError, QueryError, DuplicateEntityError
from ..services.database import get_database_service, MessageData

logger = logging.getLogger(__name__)


class MessageRepository(BaseRepository[MessageData], VectorRepository):
    """Repository for message management with vector search capabilities."""
    
    def __init__(self):
        self.db_service = None
    
    async def _get_db_service(self):
        """Get database service instance."""
        if not self.db_service:
            self.db_service = await get_database_service()
        return self.db_service
    
    async def create(self, entity: MessageData) -> MessageData:
        """Create a new message."""
        try:
            db_service = await self._get_db_service()
            
            async with db_service.pool.acquire() as conn:
                row = await conn.fetchrow("""
                    INSERT INTO messages (
                        id, conversation_id, role, content, embedding,
                        provider_used, model_used, tokens_used
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING id, conversation_id, role, content, embedding,
                             provider_used, model_used, tokens_used, created_at
                """, entity.id, entity.conversation_id, entity.role, entity.content,
                    entity.embedding, entity.provider_used, entity.model_used, entity.tokens_used)
                
                return MessageData(**dict(row))
                
        except asyncpg.UniqueViolationError:
            raise DuplicateEntityError("Message", "id", entity.id)
        except asyncpg.ForeignKeyViolationError:
            raise QueryError("INSERT INTO messages", "Invalid conversation_id")
        except Exception as e:
            logger.error(f"Failed to create message: {e}")
            raise QueryError("INSERT INTO messages", str(e))
    
    async def get_by_id(self, entity_id: UUID) -> Optional[MessageData]:
        """Get message by ID."""
        try:
            db_service = await self._get_db_service()
            
            async with db_service.pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT id, conversation_id, role, content, embedding,
                           provider_used, model_used, tokens_used, created_at
                    FROM messages WHERE id = $1
                """, entity_id)
                
                return MessageData(**dict(row)) if row else None
                
        except Exception as e:
            logger.error(f"Failed to get message {entity_id}: {e}")
            raise QueryError("SELECT FROM messages", str(e))
    
    async def update(self, entity_id: UUID, updates: Dict[str, Any]) -> Optional[MessageData]:
        """Update message by ID."""
        try:
            db_service = await self._get_db_service()
            
            # Build dynamic update query
            set_clauses = []
            params = [entity_id]
            param_count = 1
            
            allowed_fields = ['content', 'embedding', 'provider_used', 'model_used', 'tokens_used']
            for field, value in updates.items():
                if field in allowed_fields:
                    param_count += 1
                    set_clauses.append(f"{field} = ${param_count}")
                    params.append(value)
            
            if not set_clauses:
                return await self.get_by_id(entity_id)
            
            query = f"""
                UPDATE messages 
                SET {', '.join(set_clauses)}
                WHERE id = $1
                RETURNING id, conversation_id, role, content, embedding,
                         provider_used, model_used, tokens_used, created_at
            """
            
            async with db_service.pool.acquire() as conn:
                row = await conn.fetchrow(query, *params)
                
                if not row:
                    raise EntityNotFoundError("Message", entity_id)
                
                return MessageData(**dict(row))
                
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to update message {entity_id}: {e}")
            raise QueryError("UPDATE messages", str(e))
    
    async def delete(self, entity_id: UUID) -> bool:
        """Delete message by ID."""
        try:
            db_service = await self._get_db_service()
            
            async with db_service.pool.acquire() as conn:
                result = await conn.execute("""
                    DELETE FROM messages WHERE id = $1
                """, entity_id)
                
                return result == "DELETE 1"
                
        except Exception as e:
            logger.error(f"Failed to delete message {entity_id}: {e}")
            raise QueryError("DELETE FROM messages", str(e))
    
    async def list(
        self, 
        limit: int = 50, 
        offset: int = 0, 
        filters: Optional[Dict[str, Any]] = None
    ) -> List[MessageData]:
        """List messages with pagination and filtering."""
        try:
            db_service = await self._get_db_service()
            filters = filters or {}
            
            # Build dynamic query with filters
            where_clauses = []
            params = []
            param_count = 0
            
            if 'conversation_id' in filters:
                param_count += 1
                where_clauses.append(f"conversation_id = ${param_count}")
                params.append(filters['conversation_id'])
            
            if 'role' in filters:
                param_count += 1
                where_clauses.append(f"role = ${param_count}")
                params.append(filters['role'])
            
            if 'provider_used' in filters:
                param_count += 1
                where_clauses.append(f"provider_used = ${param_count}")
                params.append(filters['provider_used'])
            
            where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
            
            # Add pagination parameters
            param_count += 1
            limit_param = param_count
            params.append(limit)
            
            param_count += 1
            offset_param = param_count
            params.append(offset)
            
            query = f"""
                SELECT id, conversation_id, role, content, embedding,
                       provider_used, model_used, tokens_used, created_at
                FROM messages
                {where_clause}
                ORDER BY created_at ASC
                LIMIT ${limit_param} OFFSET ${offset_param}
            """
            
            async with db_service.pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                return [MessageData(**dict(row)) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to list messages: {e}")
            raise QueryError("SELECT FROM messages", str(e))
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count messages with optional filtering."""
        try:
            db_service = await self._get_db_service()
            filters = filters or {}
            
            # Build dynamic query with filters
            where_clauses = []
            params = []
            param_count = 0
            
            if 'conversation_id' in filters:
                param_count += 1
                where_clauses.append(f"conversation_id = ${param_count}")
                params.append(filters['conversation_id'])
            
            if 'role' in filters:
                param_count += 1
                where_clauses.append(f"role = ${param_count}")
                params.append(filters['role'])
            
            where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
            
            query = f"SELECT COUNT(*) FROM messages {where_clause}"
            
            async with db_service.pool.acquire() as conn:
                return await conn.fetchval(query, *params)
                
        except Exception as e:
            logger.error(f"Failed to count messages: {e}")
            raise QueryError("SELECT COUNT FROM messages", str(e))
    
    async def get_conversation_messages(
        self, 
        conversation_id: UUID, 
        limit: int = 100, 
        offset: int = 0
    ) -> List[MessageData]:
        """Get messages for a specific conversation."""
        return await self.list(
            limit=limit, 
            offset=offset, 
            filters={'conversation_id': conversation_id}
        )
    
    # Vector search implementation
    async def search_similar(
        self,
        query_embedding: List[float],
        limit: int = 10,
        threshold: float = 0.8,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar messages using vector similarity."""
        try:
            db_service = await self._get_db_service()
            filters = filters or {}
            
            # Build query with optional conversation filter
            where_clauses = ["embedding IS NOT NULL"]
            params = [query_embedding, threshold]
            param_count = 2
            
            if 'conversation_id' in filters:
                param_count += 1
                where_clauses.append(f"conversation_id = ${param_count}")
                params.append(filters['conversation_id'])
            
            where_clause = f"WHERE {' AND '.join(where_clauses)}"
            
            # Add limit parameter
            param_count += 1
            params.append(limit)
            
            query = f"""
                SELECT id, conversation_id, content, role,
                       1 - (embedding <=> $1) as similarity,
                       created_at,
                       jsonb_build_object(
                           'provider_used', provider_used,
                           'model_used', model_used,
                           'tokens_used', tokens_used
                       ) as metadata
                FROM messages
                {where_clause}
                  AND 1 - (embedding <=> $1) > $2
                ORDER BY embedding <=> $1
                LIMIT ${param_count}
            """
            
            async with db_service.pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to search similar messages: {e}")
            raise QueryError("Vector search on messages", str(e))
    
    async def add_embedding(
        self,
        entity_id: UUID,
        embedding: List[float],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Add or update embedding for message."""
        try:
            db_service = await self._get_db_service()
            
            async with db_service.pool.acquire() as conn:
                result = await conn.execute("""
                    UPDATE messages 
                    SET embedding = $1
                    WHERE id = $2
                """, embedding, entity_id)
                
                return result == "UPDATE 1"
                
        except Exception as e:
            logger.error(f"Failed to add embedding for message {entity_id}: {e}")
            raise QueryError("UPDATE messages embedding", str(e))
    
    async def delete_embedding(self, entity_id: UUID) -> bool:
        """Delete embedding for message."""
        try:
            db_service = await self._get_db_service()
            
            async with db_service.pool.acquire() as conn:
                result = await conn.execute("""
                    UPDATE messages 
                    SET embedding = NULL
                    WHERE id = $1
                """, entity_id)
                
                return result == "UPDATE 1"
                
        except Exception as e:
            logger.error(f"Failed to delete embedding for message {entity_id}: {e}")
            raise QueryError("UPDATE messages embedding", str(e))
