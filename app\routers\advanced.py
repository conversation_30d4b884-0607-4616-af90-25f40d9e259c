"""
Advanced model features API endpoints including function calling, tool use, and streaming.
"""

import json
import logging
import time
import uuid
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from ..services.function_calling import function_calling_service, ToolCall
from ..services.streaming import streaming_service, StreamEvent
from ..models import ChatResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/advanced", tags=["Advanced Features"])


class FunctionCallRequest(BaseModel):
    """Request model for function calling."""
    tool_name: str
    arguments: Dict[str, Any]
    permissions: Optional[List[str]] = None


class MultipleFunctionCallRequest(BaseModel):
    """Request model for multiple function calls."""
    tool_calls: List[Dict[str, Any]]  # List of {tool_name, arguments}
    permissions: Optional[List[str]] = None
    parallel: bool = True


class StreamChatRequest(BaseModel):
    """Request model for streaming chat."""
    message: str
    agent_type: str = "chatbot"
    conversation_id: str = "default"
    stream_id: Optional[str] = None


class StreamFunctionCallRequest(BaseModel):
    """Request model for streaming with function calling."""
    message: str
    available_tools: List[str] = []
    agent_type: str = "chatbot"
    conversation_id: str = "default"
    stream_id: Optional[str] = None


@router.post("/function-call")
async def execute_function_call(request: FunctionCallRequest):
    """
    Execute a single function/tool call.
    
    Available tools include calculator, text processor, HTTP requests, and agent calls.
    """
    try:
        # Create tool call
        tool_call = ToolCall(
            id=str(uuid.uuid4()),
            name=request.tool_name,
            arguments=request.arguments,
            timestamp=time.time()
        )
        
        # Execute tool call
        result = await function_calling_service.execute_tool_call(
            tool_call=tool_call,
            permissions=request.permissions
        )
        
        if result.status == "failed":
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "tool_call_id": result.id,
            "tool_name": result.name,
            "status": result.status,
            "result": result.result,
            "execution_time_ms": result.execution_time_ms
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing function call: {e}")
        raise HTTPException(status_code=500, detail=f"Function call failed: {str(e)}")


@router.post("/function-calls/batch")
async def execute_multiple_function_calls(request: MultipleFunctionCallRequest):
    """
    Execute multiple function/tool calls, optionally in parallel.
    
    Useful for complex operations that require multiple tools.
    """
    try:
        # Create tool calls
        tool_calls = []
        for i, call_data in enumerate(request.tool_calls):
            tool_call = ToolCall(
                id=f"batch_{uuid.uuid4()}_{i}",
                name=call_data["tool_name"],
                arguments=call_data["arguments"],
                timestamp=time.time()
            )
            tool_calls.append(tool_call)
        
        # Execute tool calls
        results = await function_calling_service.execute_multiple_tool_calls(
            tool_calls=tool_calls,
            permissions=request.permissions,
            parallel=request.parallel
        )
        
        # Format results
        formatted_results = []
        for result in results:
            formatted_results.append({
                "tool_call_id": result.id,
                "tool_name": result.name,
                "status": result.status,
                "result": result.result,
                "error": result.error,
                "execution_time_ms": result.execution_time_ms
            })
        
        # Calculate summary
        successful_calls = [r for r in results if r.status == "completed"]
        failed_calls = [r for r in results if r.status == "failed"]
        
        return {
            "batch_status": "completed",
            "total_calls": len(results),
            "successful_calls": len(successful_calls),
            "failed_calls": len(failed_calls),
            "parallel_execution": request.parallel,
            "results": formatted_results
        }
        
    except Exception as e:
        logger.error(f"Error executing batch function calls: {e}")
        raise HTTPException(status_code=500, detail=f"Batch function calls failed: {str(e)}")


@router.get("/tools")
async def list_available_tools():
    """List all available tools for function calling."""
    try:
        tools = function_calling_service.list_tools()
        
        return {
            "total_tools": len(tools),
            "tools": tools
        }
        
    except Exception as e:
        logger.error(f"Error listing tools: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list tools: {str(e)}")


@router.get("/tools/{tool_name}")
async def get_tool_info(tool_name: str):
    """Get detailed information about a specific tool."""
    try:
        tool_info = function_calling_service.get_tool_info(tool_name)
        
        if not tool_info:
            raise HTTPException(status_code=404, detail=f"Tool not found: {tool_name}")
        
        return tool_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tool info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get tool info: {str(e)}")


@router.get("/tools/schema")
async def get_tools_schema(tool_names: Optional[str] = Query(None)):
    """
    Get OpenAI-compatible tools schema for function calling.
    
    Optionally filter by specific tool names (comma-separated).
    """
    try:
        tool_list = None
        if tool_names:
            tool_list = [name.strip() for name in tool_names.split(",")]
        
        schema = function_calling_service.get_tools_schema(tool_list)
        
        return {
            "tools_schema": schema,
            "total_tools": len(schema)
        }
        
    except Exception as e:
        logger.error(f"Error getting tools schema: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get tools schema: {str(e)}")


@router.post("/stream/chat")
async def stream_chat(request: StreamChatRequest):
    """
    Stream a chat response in real-time.
    
    Returns Server-Sent Events (SSE) for real-time interaction.
    """
    try:
        # Generate stream ID if not provided
        stream_id = request.stream_id or str(uuid.uuid4())
        
        async def generate_stream():
            try:
                async for event in streaming_service.stream_chat_response(
                    stream_id=stream_id,
                    message=request.message,
                    agent_type=request.agent_type,
                    conversation_id=request.conversation_id
                ):
                    yield streaming_service.format_sse_event(event)
                    
            except Exception as e:
                logger.error(f"Error in chat streaming: {e}")
                error_event = StreamEvent(
                    type="error",
                    data={"error": str(e)},
                    timestamp=time.time()
                )
                yield streaming_service.format_sse_event(error_event)
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Stream-ID": stream_id
            }
        )
        
    except Exception as e:
        logger.error(f"Error starting chat stream: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start chat stream: {str(e)}")


@router.post("/stream/function-calling")
async def stream_function_calling(request: StreamFunctionCallRequest):
    """
    Stream a response with function calling capabilities.
    
    Returns Server-Sent Events (SSE) showing function calls and results in real-time.
    """
    try:
        # Generate stream ID if not provided
        stream_id = request.stream_id or str(uuid.uuid4())
        
        async def generate_stream():
            try:
                async for event in streaming_service.stream_function_calling(
                    stream_id=stream_id,
                    message=request.message,
                    available_tools=request.available_tools,
                    agent_type=request.agent_type,
                    conversation_id=request.conversation_id
                ):
                    yield streaming_service.format_sse_event(event)
                    
            except Exception as e:
                logger.error(f"Error in function calling streaming: {e}")
                error_event = StreamEvent(
                    type="error",
                    data={"error": str(e)},
                    timestamp=time.time()
                )
                yield streaming_service.format_sse_event(error_event)
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Stream-ID": stream_id
            }
        )
        
    except Exception as e:
        logger.error(f"Error starting function calling stream: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start function calling stream: {str(e)}")


@router.get("/stream/{stream_id}/info")
async def get_stream_info(stream_id: str):
    """Get information about an active stream."""
    try:
        stream_info = await streaming_service.get_stream_info(stream_id)
        
        if not stream_info:
            raise HTTPException(status_code=404, detail=f"Stream not found: {stream_id}")
        
        return stream_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stream info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get stream info: {str(e)}")


@router.delete("/stream/{stream_id}")
async def close_stream(stream_id: str):
    """Close an active stream."""
    try:
        success = await streaming_service.close_stream(stream_id)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Stream not found: {stream_id}")
        
        return {"message": f"Stream {stream_id} closed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error closing stream: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to close stream: {str(e)}")


@router.get("/streams")
async def list_active_streams():
    """List all active streams."""
    try:
        active_streams = streaming_service.list_active_streams()
        
        return {
            "total_active_streams": len(active_streams),
            "stream_ids": active_streams
        }
        
    except Exception as e:
        logger.error(f"Error listing streams: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list streams: {str(e)}")


@router.get("/health")
async def advanced_features_health_check():
    """Check advanced features service health."""
    try:
        tools = function_calling_service.list_tools()
        active_streams = streaming_service.list_active_streams()
        
        return {
            "status": "healthy",
            "function_calling": "available",
            "streaming": "available",
            "total_tools": len(tools),
            "active_streams": len(active_streams),
            "features": [
                "function_calling",
                "tool_use",
                "streaming_responses",
                "real_time_interactions"
            ]
        }
        
    except Exception as e:
        logger.error(f"Advanced features health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
