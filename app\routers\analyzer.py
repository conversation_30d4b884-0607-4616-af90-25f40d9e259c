"""
Task analyzer router for complexity analysis and model routing endpoints.
"""

from typing import <PERSON><PERSON>
from decimal import Decimal

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from ..services.task_analyzer import complexity_analyzer, TaskAnalysisResult
from ..services.router_service import router_service, ModelSelectionResult


router = APIRouter(prefix="/analyze", tags=["Task Analysis"])


class AnalyzeRequest(BaseModel):
    """Request model for task analysis."""
    
    text: str = Field(..., description="Text to analyze for complexity")
    modality: str = Field("text", description="Task modality (text, image, etc.)")
    preferred_provider: Optional[str] = Field(None, description="Preferred AI provider")
    max_cost: Optional[Decimal] = Field(None, description="Maximum cost constraint in USD")


class AnalyzeResponse(BaseModel):
    """Response model for task analysis."""
    
    complexity: TaskAnalysisResult
    routing: ModelSelectionResult


class ComplexityOnlyRequest(BaseModel):
    """Request model for complexity-only analysis."""
    
    text: str = Field(..., description="Text to analyze for complexity")


@router.post("/", response_model=AnalyzeResponse)
async def analyze_task(request: AnalyzeRequest):
    """
    Analyze task complexity and recommend optimal model routing.
    
    This endpoint combines complexity analysis with intelligent model selection
    to provide cost-efficient routing recommendations.
    
    Returns:
        - Task complexity classification and score
        - Recommended model and provider
        - Cost estimation and reasoning
        - Alternative model options
    """
    try:
        # Analyze task complexity
        complexity_result = complexity_analyzer.analyze(request.text)
        
        # Get model routing recommendation
        routing_result = router_service.pick(
            text=request.text,
            modality=request.modality,
            preferred_provider=request.preferred_provider,
            max_cost=request.max_cost
        )
        
        return AnalyzeResponse(
            complexity=complexity_result,
            routing=routing_result
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing task: {str(e)}"
        )


@router.post("/complexity", response_model=TaskAnalysisResult)
async def analyze_complexity_only(request: ComplexityOnlyRequest):
    """
    Analyze task complexity without model routing.
    
    This endpoint provides only the complexity analysis portion,
    useful for understanding task difficulty without needing model recommendations.
    
    Returns JSON in the format specified in the ai_brain.md document:
    ```json
    {"class":"hard","score":0.83,"reason":"multi-step code generation with unit tests"}
    ```
    """
    try:
        result = complexity_analyzer.analyze(request.text)
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing complexity: {str(e)}"
        )


@router.post("/route", response_model=ModelSelectionResult)
async def route_model_only(request: AnalyzeRequest):
    """
    Get model routing recommendation without complexity analysis.
    
    This endpoint provides only the model selection portion,
    useful when complexity is already known or for direct routing needs.
    """
    try:
        result = router_service.pick(
            text=request.text,
            modality=request.modality,
            preferred_provider=request.preferred_provider,
            max_cost=request.max_cost
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error routing model: {str(e)}"
        )


@router.get("/models")
async def list_available_models():
    """
    List all available models with their configurations.
    
    Returns information about model costs, capabilities, and availability
    for transparency in routing decisions.
    """
    try:
        # Refresh router cache to get latest model info
        router_service._refresh_cache()
        
        models = []
        for model_name, model_option in router_service._model_cache.items():
            models.append({
                "name": model_name,
                "provider": model_option.provider,
                "tier": model_option.tier,
                "cost_per_1k_input": float(model_option.cost_per_1k_input),
                "cost_per_1k_output": float(model_option.cost_per_1k_output),
                "max_context_tokens": model_option.max_context,
                "is_available": model_option.is_available
            })
        
        # Sort by tier (free first) then by cost
        models.sort(key=lambda m: (m["tier"] != "free", m["cost_per_1k_input"]))
        
        return {
            "models": models,
            "total_count": len(models),
            "free_models": len([m for m in models if m["tier"] == "free"]),
            "available_models": len([m for m in models if m["is_available"]])
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing models: {str(e)}"
        )


@router.get("/policies")
async def list_routing_policies():
    """
    List current routing policies for different complexity levels.
    
    Returns the routing policies that determine which models are selected
    for different task complexity levels.
    """
    try:
        # Refresh router cache to get latest policies
        router_service._refresh_cache()
        
        policies = []
        for complexity, policy in router_service._policy_cache.items():
            policies.append({
                "task_class": complexity.value,
                "score_threshold": float(policy.score_threshold),
                "preferred_models": policy.preferred_models,
                "fallback_models": policy.fallback_models,
                "is_active": policy.is_active
            })
        
        return {
            "policies": policies,
            "total_count": len(policies)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing policies: {str(e)}"
        )


@router.post("/refresh")
async def refresh_cache():
    """
    Refresh the router cache with latest model and policy configurations.
    
    This endpoint allows manual cache refresh when model configurations
    or routing policies are updated in the database.
    """
    try:
        router_service._refresh_cache()
        
        return {
            "message": "Cache refreshed successfully",
            "models_loaded": len(router_service._model_cache),
            "policies_loaded": len(router_service._policy_cache)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error refreshing cache: {str(e)}"
        )
