"""
Dashboard API endpoints for analytics and management.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from ..dashboard import get_usage_analytics, get_model_performance_metrics
from ..services.router_service import router_service
from ..services.task_analyzer import complexity_analyzer
from ..core.database import get_db_session


logger = logging.getLogger(__name__)
router = APIRouter(prefix="/dashboard", tags=["Dashboard"])


class AnalyticsResponse(BaseModel):
    """Response model for analytics data."""
    
    total_requests: int
    total_cost: float
    avg_response_time: int
    most_used_model: str
    cost_savings: float
    complexity_distribution: Dict[str, int]
    provider_usage: Dict[str, int]
    daily_requests: List[int]


class ModelMetricsResponse(BaseModel):
    """Response model for model performance metrics."""
    
    class ModelMetric(BaseModel):
        name: str
        provider: str
        avg_response_time: int
        success_rate: float
        cost_per_request: float
        usage_count: int
    
    class OverallMetrics(BaseModel):
        avg_response_time: int
        overall_success_rate: float
        total_cost_savings: float
        free_model_usage_percentage: float
    
    models: List[ModelMetric]
    overall_metrics: OverallMetrics


class SystemStatusResponse(BaseModel):
    """Response model for system status."""
    
    status: str
    timestamp: datetime
    services: Dict[str, str]
    model_cache_size: int
    policy_cache_size: int
    uptime_seconds: int


@router.get("/analytics", response_model=AnalyticsResponse)
async def get_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze")
):
    """
    Get usage analytics for the specified time period.
    
    Args:
        days: Number of days to analyze (1-365)
        
    Returns:
        Analytics data including usage, costs, and performance metrics
    """
    try:
        analytics_data = await get_usage_analytics(days)
        
        if not analytics_data:
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve analytics data"
            )
        
        return AnalyticsResponse(**analytics_data)
        
    except Exception as e:
        logger.error(f"Error retrieving analytics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving analytics: {str(e)}"
        )


@router.get("/metrics", response_model=ModelMetricsResponse)
async def get_model_metrics():
    """
    Get model performance metrics.
    
    Returns:
        Performance metrics for all configured models
    """
    try:
        metrics_data = await get_model_performance_metrics()
        
        if not metrics_data:
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve model metrics"
            )
        
        return ModelMetricsResponse(**metrics_data)
        
    except Exception as e:
        logger.error(f"Error retrieving model metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving model metrics: {str(e)}"
        )


@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status():
    """
    Get current system status and health information.
    
    Returns:
        System status including service health and cache information
    """
    try:
        # Check service status
        services = {
            "router_service": "healthy",
            "complexity_analyzer": "healthy",
            "database": "healthy",
            "cache": "healthy"
        }
        
        # Test router service
        try:
            router_service._refresh_cache()
            model_cache_size = len(router_service._model_cache)
            policy_cache_size = len(router_service._policy_cache)
        except Exception as e:
            logger.warning(f"Router service check failed: {e}")
            services["router_service"] = "degraded"
            model_cache_size = 0
            policy_cache_size = 0
        
        # Test complexity analyzer
        try:
            test_result = complexity_analyzer.analyze("test task")
            if not test_result:
                services["complexity_analyzer"] = "degraded"
        except Exception as e:
            logger.warning(f"Complexity analyzer check failed: {e}")
            services["complexity_analyzer"] = "degraded"
        
        # Determine overall status
        if all(status == "healthy" for status in services.values()):
            overall_status = "healthy"
        elif any(status == "degraded" for status in services.values()):
            overall_status = "degraded"
        else:
            overall_status = "unhealthy"
        
        return SystemStatusResponse(
            status=overall_status,
            timestamp=datetime.now(),
            services=services,
            model_cache_size=model_cache_size,
            policy_cache_size=policy_cache_size,
            uptime_seconds=3600  # Mock uptime
        )
        
    except Exception as e:
        logger.error(f"Error checking system status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error checking system status: {str(e)}"
        )


@router.post("/cache/refresh")
async def refresh_cache():
    """
    Refresh the router service cache.
    
    Returns:
        Success message with cache statistics
    """
    try:
        router_service._refresh_cache()
        
        return {
            "message": "Cache refreshed successfully",
            "timestamp": datetime.now(),
            "models_loaded": len(router_service._model_cache),
            "policies_loaded": len(router_service._policy_cache)
        }
        
    except Exception as e:
        logger.error(f"Error refreshing cache: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error refreshing cache: {str(e)}"
        )


@router.get("/models/test")
async def test_model_availability():
    """
    Test the availability of all configured models.
    
    Returns:
        Test results for each model
    """
    try:
        # This would test actual model availability
        # For now, return mock test results
        test_results = {
            "test_timestamp": datetime.now(),
            "total_models": len(router_service._model_cache),
            "available_models": [],
            "unavailable_models": [],
            "test_duration_ms": 1500
        }
        
        # Mock test results
        for model_name in router_service._model_cache.keys():
            # Simulate some models being unavailable
            if "test" in model_name.lower():
                test_results["unavailable_models"].append({
                    "model": model_name,
                    "error": "Test model not accessible"
                })
            else:
                test_results["available_models"].append({
                    "model": model_name,
                    "response_time_ms": 850,
                    "status": "healthy"
                })
        
        return test_results
        
    except Exception as e:
        logger.error(f"Error testing model availability: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error testing model availability: {str(e)}"
        )


@router.get("/complexity/test")
async def test_complexity_analyzer(
    sample_text: Optional[str] = Query(
        None, 
        description="Custom text to analyze (uses default if not provided)"
    )
):
    """
    Test the complexity analyzer with sample text.
    
    Args:
        sample_text: Optional custom text to analyze
        
    Returns:
        Complexity analysis results
    """
    try:
        if not sample_text:
            sample_text = "Write a Python function to implement a binary search algorithm with error handling and unit tests"
        
        result = complexity_analyzer.analyze(sample_text)
        
        return {
            "input_text": sample_text,
            "complexity_class": result.task_class.value,
            "complexity_score": result.score,
            "reasoning": result.reason,
            "analysis_timestamp": datetime.now(),
            "features_extracted": {
                "text_length": len(sample_text),
                "estimated_tokens": len(sample_text.split()) * 1.3  # Rough estimate
            }
        }
        
    except Exception as e:
        logger.error(f"Error testing complexity analyzer: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error testing complexity analyzer: {str(e)}"
        )


@router.get("/costs/summary")
async def get_cost_summary(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze")
):
    """
    Get cost summary and savings analysis.
    
    Args:
        days: Number of days to analyze
        
    Returns:
        Cost breakdown and savings information
    """
    try:
        # Mock cost analysis
        cost_summary = {
            "period_days": days,
            "total_cost": 15.75,
            "cost_breakdown": {
                "free_models": 0.0,
                "paid_models": 15.75
            },
            "savings_analysis": {
                "potential_cost_without_routing": 67.50,
                "actual_cost": 15.75,
                "total_savings": 51.75,
                "savings_percentage": 76.7
            },
            "cost_by_provider": {
                "google": 0.0,  # Free models
                "groq": 0.0,    # Free models
                "openrouter": 2.25,
                "openai": 10.50,
                "anthropic": 3.00
            },
            "cost_by_complexity": {
                "very_easy": 0.0,
                "easy": 0.0,
                "medium": 3.25,
                "hard": 8.50,
                "very_hard": 4.00
            },
            "recommendations": [
                "Continue using free models for simple tasks",
                "Consider fine-tuning complexity thresholds",
                "Monitor usage patterns for optimization opportunities"
            ]
        }
        
        return cost_summary
        
    except Exception as e:
        logger.error(f"Error generating cost summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating cost summary: {str(e)}"
        )
