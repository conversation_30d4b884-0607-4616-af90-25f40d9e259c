"""
Multi-modal processing API endpoints for images, audio, and video.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, UploadFile, File, HTTPException, Query, Form
from pydantic import BaseModel

from ..agents.image_agent import image_agent
from ..services.multimodal_processor import multimodal_processor
from ..models import ChatResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/multimodal", tags=["Multimodal"])


class MediaProcessingResponse(BaseModel):
    """Response model for media processing."""
    status: str
    filename: str
    content_type: str
    processing_time_ms: float
    media_metadata: Dict[str, Any]
    extracted_content: Dict[str, Any]
    ai_analysis: Optional[Dict[str, Any]] = None
    thumbnails: Optional[List[str]] = None
    message: Optional[str] = None


class ImageAnalysisRequest(BaseModel):
    """Request model for image analysis with text prompt."""
    prompt: str
    conversation_id: str = "default"
    processing_mode: str = "analysis"
    max_cost: Optional[float] = None
    preferred_provider: Optional[str] = None


@router.post("/upload", response_model=MediaProcessingResponse)
async def upload_media(
    file: UploadFile = File(...),
    task_description: str = Form("Analyze this media file"),
    conversation_id: str = Form("default")
):
    """
    Upload and process media files (images, audio, video).
    
    Supports various formats:
    - Images: JPEG, PNG, GIF, BMP, WebP, TIFF
    - Audio: MP3, WAV, OGG, M4A, AAC, FLAC
    - Video: MP4, AVI, MOV, WMV, FLV, WebM, MKV
    """
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Check file size (limit to 50MB)
        file_content = await file.read()
        if len(file_content) > 50 * 1024 * 1024:  # 50MB
            raise HTTPException(status_code=413, detail="File too large (max 50MB)")
        
        # Reset file pointer
        await file.seek(0)
        
        # Process with image agent (which handles all media types)
        result = await image_agent.process_media(
            file=file,
            task_description=task_description,
            conversation_id=conversation_id
        )
        
        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["message"])
        
        return MediaProcessingResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading media: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/analyze-image", response_model=ChatResponse)
async def analyze_image_with_prompt(
    file: UploadFile = File(...),
    request: ImageAnalysisRequest = Form(...)
):
    """
    Analyze an image with a specific text prompt using vision-language models.
    
    This endpoint combines image upload with AI-powered analysis.
    """
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Check if it's an image file
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read and encode image
        image_bytes = await file.read()
        
        # Use image agent for analysis
        response = await image_agent.process_image(
            message=request.prompt,
            image_data=image_bytes,
            conversation_id=request.conversation_id,
            processing_mode=request.processing_mode,
            max_cost=request.max_cost,
            preferred_provider=request.preferred_provider
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing image: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis error: {str(e)}")


@router.post("/extract-text")
async def extract_text_from_media(
    file: UploadFile = File(...),
    extraction_type: str = Query("auto", description="Type of extraction: auto, ocr, transcript")
):
    """
    Extract text content from media files.
    
    - Images: OCR text extraction
    - Audio: Speech-to-text transcription
    - Video: Audio transcription + OCR from frames
    - Documents: Text extraction (handled by document processor)
    """
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Process the media file
        processed_media = await multimodal_processor.process_media(file)
        
        # Extract text based on media type
        extracted_text = ""
        extraction_method = "none"
        
        if processed_media.content_type.startswith('image/'):
            # For images, we would use OCR
            extracted_text = processed_media.extracted_text or "[OCR not implemented]"
            extraction_method = "ocr"
        elif processed_media.content_type.startswith('audio/'):
            # For audio, we would use speech-to-text
            extracted_text = processed_media.audio_transcript or "[Speech-to-text not implemented]"
            extraction_method = "speech_to_text"
        elif processed_media.content_type.startswith('video/'):
            # For video, we would extract audio and use speech-to-text
            extracted_text = processed_media.video_summary or "[Video transcription not implemented]"
            extraction_method = "video_transcription"
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported media type: {processed_media.content_type}")
        
        return {
            "filename": file.filename,
            "content_type": processed_media.content_type,
            "extraction_method": extraction_method,
            "extracted_text": extracted_text,
            "processing_time_ms": processed_media.processing_time_ms,
            "metadata": processed_media.metadata
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting text from media: {e}")
        raise HTTPException(status_code=500, detail=f"Extraction error: {str(e)}")


@router.get("/supported-formats")
async def get_supported_formats():
    """Get list of supported media formats."""
    try:
        formats = multimodal_processor.get_supported_formats()
        
        return {
            "supported_formats": formats,
            "total_formats": sum(len(format_list) for format_list in formats.values()),
            "categories": list(formats.keys())
        }
        
    except Exception as e:
        logger.error(f"Error getting supported formats: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")


@router.post("/batch-process")
async def batch_process_media(
    files: List[UploadFile] = File(...),
    task_description: str = Form("Analyze these media files"),
    conversation_id: str = Form("batch_default")
):
    """
    Process multiple media files in batch.
    
    Useful for processing multiple images, audio files, or mixed media.
    """
    try:
        if len(files) > 10:
            raise HTTPException(status_code=400, detail="Maximum 10 files per batch")
        
        results = []
        total_size = 0
        
        for file in files:
            if not file.filename:
                continue
            
            # Check individual file size
            file_content = await file.read()
            total_size += len(file_content)
            
            if len(file_content) > 10 * 1024 * 1024:  # 10MB per file
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "message": "File too large (max 10MB per file)"
                })
                continue
            
            # Check total batch size
            if total_size > 100 * 1024 * 1024:  # 100MB total
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "message": "Batch size limit exceeded (max 100MB total)"
                })
                break
            
            # Reset file pointer
            await file.seek(0)
            
            try:
                # Process individual file
                result = await image_agent.process_media(
                    file=file,
                    task_description=f"{task_description} (file {len(results)+1})",
                    conversation_id=f"{conversation_id}_file_{len(results)+1}"
                )
                results.append(result)
                
            except Exception as file_error:
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "message": str(file_error)
                })
        
        # Calculate batch statistics
        successful_files = [r for r in results if r.get("status") == "success"]
        failed_files = [r for r in results if r.get("status") == "error"]
        
        return {
            "batch_status": "completed",
            "total_files": len(files),
            "successful_files": len(successful_files),
            "failed_files": len(failed_files),
            "total_size_bytes": total_size,
            "results": results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in batch processing: {e}")
        raise HTTPException(status_code=500, detail=f"Batch processing error: {str(e)}")


@router.get("/health")
async def multimodal_health_check():
    """Check multimodal service health."""
    try:
        supported_formats = multimodal_processor.get_supported_formats()
        
        return {
            "status": "healthy",
            "supported_categories": list(supported_formats.keys()),
            "total_supported_formats": sum(len(formats) for formats in supported_formats.values()),
            "image_processing": "available",
            "audio_processing": "limited",  # Placeholder implementation
            "video_processing": "limited",  # Placeholder implementation
            "ai_vision_models": "available"
        }
        
    except Exception as e:
        logger.error(f"Multimodal health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
