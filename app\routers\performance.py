"""
Performance optimization API endpoints including caching, batching, and async processing.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel

from ..services.caching import caching_service
from ..services.batching import batching_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/performance", tags=["Performance Optimization"])


class CacheSetRequest(BaseModel):
    """Request model for setting cache values."""
    key: str
    value: Any
    ttl: Optional[int] = 3600


class BatchRequest(BaseModel):
    """Request model for batch processing."""
    batch_type: str
    request_data: Dict[str, Any]
    priority: int = 0
    max_wait_time: Optional[float] = None


class MultipleBatchRequest(BaseModel):
    """Request model for multiple batch requests."""
    batch_type: str
    requests: List[Dict[str, Any]]
    priority: int = 0
    max_wait_time: Optional[float] = None


@router.get("/cache/stats")
async def get_cache_stats():
    """Get cache performance statistics."""
    try:
        stats = caching_service.get_stats()
        return {
            "cache_stats": stats,
            "status": "healthy"
        }
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get cache stats: {str(e)}")


@router.get("/cache/{key}")
async def get_cached_value(key: str):
    """Get a value from cache."""
    try:
        value = await caching_service.get(key)
        
        if value is None:
            raise HTTPException(status_code=404, detail=f"Cache key not found: {key}")
        
        return {
            "key": key,
            "value": value,
            "cached": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting cached value: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get cached value: {str(e)}")


@router.post("/cache")
async def set_cached_value(request: CacheSetRequest):
    """Set a value in cache."""
    try:
        success = await caching_service.set(
            key=request.key,
            value=request.value,
            ttl=request.ttl
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to set cache value")
        
        return {
            "key": request.key,
            "cached": True,
            "ttl": request.ttl
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting cached value: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to set cached value: {str(e)}")


@router.delete("/cache/{key}")
async def delete_cached_value(key: str):
    """Delete a value from cache."""
    try:
        success = await caching_service.delete(key)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"Cache key not found: {key}")
        
        return {
            "key": key,
            "deleted": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting cached value: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete cached value: {str(e)}")


@router.delete("/cache")
async def clear_cache():
    """Clear all cache entries."""
    try:
        success = await caching_service.clear()
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to clear cache")
        
        return {
            "cleared": True,
            "message": "All cache entries cleared"
        }
        
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")


@router.post("/batch/submit")
async def submit_batch_request(request: BatchRequest):
    """Submit a request for batch processing."""
    try:
        request_id = await batching_service.add_request(
            batch_type=request.batch_type,
            request_data=request.request_data,
            priority=request.priority,
            max_wait_time=request.max_wait_time
        )
        
        return {
            "request_id": request_id,
            "batch_type": request.batch_type,
            "status": "submitted",
            "message": "Request submitted for batch processing"
        }
        
    except Exception as e:
        logger.error(f"Error submitting batch request: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to submit batch request: {str(e)}")


@router.post("/batch/submit-multiple")
async def submit_multiple_batch_requests(request: MultipleBatchRequest):
    """Submit multiple requests for batch processing."""
    try:
        request_ids = []
        
        for req_data in request.requests:
            request_id = await batching_service.add_request(
                batch_type=request.batch_type,
                request_data=req_data,
                priority=request.priority,
                max_wait_time=request.max_wait_time
            )
            request_ids.append(request_id)
        
        return {
            "request_ids": request_ids,
            "batch_type": request.batch_type,
            "total_requests": len(request_ids),
            "status": "submitted",
            "message": f"Submitted {len(request_ids)} requests for batch processing"
        }
        
    except Exception as e:
        logger.error(f"Error submitting multiple batch requests: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to submit batch requests: {str(e)}")


@router.get("/batch/result/{request_id}")
async def get_batch_result(request_id: str, timeout: float = 30.0):
    """Get the result of a batch request."""
    try:
        result = await batching_service.get_request_result(request_id, timeout=timeout)
        
        if result is None:
            raise HTTPException(status_code=404, detail=f"Request not found: {request_id}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting batch result: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get batch result: {str(e)}")


@router.get("/batch/stats")
async def get_batch_stats():
    """Get batch processing statistics."""
    try:
        stats = batching_service.get_batch_stats()
        return {
            "batch_stats": stats,
            "status": "healthy"
        }
        
    except Exception as e:
        logger.error(f"Error getting batch stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get batch stats: {str(e)}")


@router.post("/optimize/ai-response")
async def optimize_ai_response(
    agent_type: str,
    message: str,
    conversation_id: str = "default",
    use_cache: bool = True,
    use_batching: bool = False
):
    """
    Optimized AI response with caching and optional batching.
    
    This endpoint demonstrates performance optimizations for AI responses.
    """
    try:
        # Check cache first if enabled
        if use_cache:
            cached_response = await caching_service.get_cached_ai_response(
                agent_type=agent_type,
                message=message,
                conversation_id=conversation_id
            )
            
            if cached_response:
                return {
                    "response": cached_response,
                    "cached": True,
                    "agent_type": agent_type,
                    "conversation_id": conversation_id
                }
        
        # Process request
        if use_batching:
            # Submit to batch processing
            request_id = await batching_service.add_request(
                batch_type="ai_inference",
                request_data={
                    "agent_type": agent_type,
                    "message": message,
                    "conversation_id": conversation_id
                }
            )
            
            # Wait for result
            result = await batching_service.get_request_result(request_id, timeout=30.0)
            
            if result and result.get("status") == "completed":
                response_data = result["result"]
                
                # Cache the result if caching is enabled
                if use_cache:
                    await caching_service.cache_ai_response(
                        agent_type=agent_type,
                        message=message,
                        response=response_data,
                        conversation_id=conversation_id
                    )
                
                return {
                    "response": response_data,
                    "cached": False,
                    "batched": True,
                    "batch_id": result.get("batch_id"),
                    "processing_time_ms": result.get("processing_time_ms"),
                    "agent_type": agent_type,
                    "conversation_id": conversation_id
                }
            else:
                raise HTTPException(status_code=500, detail="Batch processing failed")
        
        else:
            # Direct processing (simulated)
            response_data = {
                "message": f"Optimized response from {agent_type} for: {message}",
                "optimized": True,
                "direct_processing": True
            }
            
            # Cache the result if caching is enabled
            if use_cache:
                await caching_service.cache_ai_response(
                    agent_type=agent_type,
                    message=message,
                    response=response_data,
                    conversation_id=conversation_id
                )
            
            return {
                "response": response_data,
                "cached": False,
                "batched": False,
                "agent_type": agent_type,
                "conversation_id": conversation_id
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in optimized AI response: {e}")
        raise HTTPException(status_code=500, detail=f"Optimized AI response failed: {str(e)}")


@router.post("/optimize/task-analysis")
async def optimize_task_analysis(
    task_text: str,
    use_cache: bool = True,
    use_batching: bool = False
):
    """
    Optimized task complexity analysis with caching and optional batching.
    """
    try:
        # Check cache first if enabled
        if use_cache:
            cached_analysis = await caching_service.get_cached_task_analysis(task_text)
            
            if cached_analysis:
                return {
                    "analysis": cached_analysis,
                    "cached": True,
                    "task_text": task_text
                }
        
        # Process request
        if use_batching:
            # Submit to batch processing
            request_id = await batching_service.add_request(
                batch_type="task_analysis",
                request_data={"task_text": task_text}
            )
            
            # Wait for result
            result = await batching_service.get_request_result(request_id, timeout=30.0)
            
            if result and result.get("status") == "completed":
                analysis_data = result["result"]
                
                # Cache the result if caching is enabled
                if use_cache:
                    await caching_service.cache_task_analysis(
                        task_text=task_text,
                        analysis_result=analysis_data
                    )
                
                return {
                    "analysis": analysis_data,
                    "cached": False,
                    "batched": True,
                    "batch_id": result.get("batch_id"),
                    "processing_time_ms": result.get("processing_time_ms"),
                    "task_text": task_text
                }
            else:
                raise HTTPException(status_code=500, detail="Batch processing failed")
        
        else:
            # Direct processing (simulated)
            analysis_data = {
                "complexity_score": min(len(task_text) / 100, 1.0),
                "complexity_label": "medium",
                "features_count": len(task_text.split()),
                "optimized": True,
                "direct_processing": True
            }
            
            # Cache the result if caching is enabled
            if use_cache:
                await caching_service.cache_task_analysis(
                    task_text=task_text,
                    analysis_result=analysis_data
                )
            
            return {
                "analysis": analysis_data,
                "cached": False,
                "batched": False,
                "task_text": task_text
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in optimized task analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Optimized task analysis failed: {str(e)}")


@router.get("/health")
async def performance_health_check():
    """Check performance optimization services health."""
    try:
        cache_stats = caching_service.get_stats()
        batch_stats = batching_service.get_batch_stats()
        
        return {
            "status": "healthy",
            "caching": "available",
            "batching": "available",
            "cache_stats": cache_stats,
            "batch_stats": batch_stats,
            "optimizations": [
                "response_caching",
                "request_batching",
                "async_processing",
                "memory_management"
            ]
        }
        
    except Exception as e:
        logger.error(f"Performance health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
