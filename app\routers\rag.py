"""
RAG (Retrieval-Augmented Generation) API endpoints.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, UploadFile, File, HTTPException, Query
from pydantic import BaseModel

from ..agents.rag_agent import rag_agent
from ..services.rag_service import rag_service
from ..services.document_processor import document_processor
from ..models import ChatResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/rag", tags=["RAG"])


class DocumentUploadResponse(BaseModel):
    """Response model for document upload."""
    status: str
    filename: str
    knowledge_base: str
    processing_time_ms: float
    document_stats: Dict[str, Any]
    indexing_stats: Dict[str, Any]
    message: Optional[str] = None


class SearchRequest(BaseModel):
    """Request model for document search."""
    query: str
    limit: int = 5
    score_threshold: float = 0.7
    knowledge_base: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None


class SearchResponse(BaseModel):
    """Response model for document search."""
    query: str
    results: List[Dict[str, Any]]
    total_results: int
    retrieval_time_ms: float


class RAGChatRequest(BaseModel):
    """Request model for RAG chat."""
    message: str
    conversation_id: str
    knowledge_base: Optional[str] = None
    enable_retrieval: bool = True
    max_cost: Optional[float] = None
    preferred_provider: Optional[str] = None


@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    knowledge_base: Optional[str] = Query(None, description="Knowledge base to store the document"),
    chunking_strategy: str = Query("sentences", description="Chunking strategy: 'sentences' or 'words'")
):
    """
    Upload and index a document for RAG retrieval.
    
    Supports various file formats: PDF, DOCX, TXT, MD, HTML, JSON, CSV.
    """
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Check file size (limit to 10MB)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(status_code=413, detail="File too large (max 10MB)")
        
        # Reset file pointer
        await file.seek(0)
        
        result = await rag_agent.upload_document(
            file=file,
            knowledge_base=knowledge_base,
            chunking_strategy=chunking_strategy
        )
        
        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["message"])
        
        return DocumentUploadResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """
    Search documents using semantic similarity.
    
    Returns relevant document chunks based on the query.
    """
    try:
        rag_result = await rag_service.search_documents(
            query=request.query,
            limit=request.limit,
            score_threshold=request.score_threshold,
            filters=request.filters
        )
        
        # Convert chunks to response format
        results = []
        for chunk, score in zip(rag_result.chunks, rag_result.similarity_scores):
            results.append({
                "content": chunk.content,
                "score": score,
                "metadata": chunk.metadata,
                "chunk_id": chunk.id
            })
        
        return SearchResponse(
            query=request.query,
            results=results,
            total_results=len(results),
            retrieval_time_ms=rag_result.retrieval_time_ms
        )
        
    except Exception as e:
        logger.error(f"Error searching documents: {e}")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")


@router.post("/chat", response_model=ChatResponse)
async def rag_chat(request: RAGChatRequest):
    """
    Chat with RAG-enhanced responses.
    
    Automatically retrieves relevant information and incorporates it into responses.
    """
    try:
        response = await rag_agent.chat(
            message=request.message,
            conversation_id=request.conversation_id,
            knowledge_base=request.knowledge_base,
            enable_retrieval=request.enable_retrieval,
            max_cost=request.max_cost,
            preferred_provider=request.preferred_provider
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error in RAG chat: {e}")
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")


@router.get("/stats")
async def get_rag_stats():
    """Get RAG service statistics."""
    try:
        stats = await rag_service.get_collection_stats()
        
        # Add supported formats
        stats["supported_formats"] = document_processor.get_supported_formats()
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting RAG stats: {e}")
        raise HTTPException(status_code=500, detail=f"Stats error: {str(e)}")


@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """Delete a document and all its chunks from the knowledge base."""
    try:
        result = await rag_service.delete_document(document_id)
        
        if result["status"] == "error":
            raise HTTPException(status_code=400, detail=result["message"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Delete error: {str(e)}")


@router.get("/knowledge-bases")
async def list_knowledge_bases():
    """List available knowledge bases."""
    try:
        # This would query the database for unique knowledge_base values
        # For now, return a placeholder
        return {
            "knowledge_bases": ["default", "technical", "marketing", "support"],
            "total_documents": 0,
            "message": "Knowledge base listing not fully implemented"
        }
        
    except Exception as e:
        logger.error(f"Error listing knowledge bases: {e}")
        raise HTTPException(status_code=500, detail=f"Listing error: {str(e)}")


@router.get("/health")
async def rag_health_check():
    """Check RAG service health."""
    try:
        stats = await rag_service.get_collection_stats()
        
        return {
            "status": "healthy" if stats["status"] == "available" else "degraded",
            "qdrant_status": stats["status"],
            "embedding_model": "all-MiniLM-L6-v2",
            "supported_formats": len(document_processor.get_supported_formats()),
            "collection_info": stats if stats["status"] == "available" else None
        }
        
    except Exception as e:
        logger.error(f"RAG health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
