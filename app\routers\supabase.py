"""
Supabase API endpoints for authentication, database, and storage operations.
"""

import logging
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from fastapi.responses import StreamingResponse
import io

from ..services.auth_service import (
    get_auth_service,
    UserSignUp,
    UserSignIn,
    AuthResponse,
    UserProfile,
    SupabaseAuthError
)
from ..services.database_service import (
    get_database_service,
    DatabaseQuery,
    DatabaseInsert,
    DatabaseUpdate,
    DatabaseDelete,
    SupabaseDBError
)
from ..services.storage_service import (
    get_storage_service,
    FileInfo,
    SupabaseStorageError
)
from ..middleware.auth_middleware import get_current_user, get_current_user_optional

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/supabase", tags=["supabase"])


# Authentication endpoints
@router.post("/auth/signup", response_model=AuthResponse)
async def sign_up(user_data: UserSignUp):
    """Sign up a new user."""
    try:
        auth_service = get_auth_service()
        return await auth_service.sign_up(user_data)
    except SupabaseAuthError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Sign up error: {e}")
        raise HTTPException(status_code=500, detail="Sign up failed")


@router.post("/auth/signin", response_model=AuthResponse)
async def sign_in(credentials: UserSignIn):
    """Sign in an existing user."""
    try:
        auth_service = get_auth_service()
        return await auth_service.sign_in(credentials)
    except SupabaseAuthError as e:
        raise HTTPException(status_code=401, detail=str(e))
    except Exception as e:
        logger.error(f"Sign in error: {e}")
        raise HTTPException(status_code=500, detail="Sign in failed")


@router.post("/auth/signout")
async def sign_out(user: UserProfile = Depends(get_current_user)):
    """Sign out the current user."""
    try:
        auth_service = get_auth_service()
        # Note: We would need the access token here, but for simplicity
        # we'll just return success. In a real implementation, you'd
        # extract the token from the request.
        return {"message": "Signed out successfully"}
    except Exception as e:
        logger.error(f"Sign out error: {e}")
        raise HTTPException(status_code=500, detail="Sign out failed")


@router.get("/auth/user", response_model=UserProfile)
async def get_user_profile(user: UserProfile = Depends(get_current_user)):
    """Get current user profile."""
    return user


@router.post("/auth/refresh", response_model=AuthResponse)
async def refresh_token(refresh_token: str):
    """Refresh access token."""
    try:
        auth_service = get_auth_service()
        return await auth_service.refresh_token(refresh_token)
    except SupabaseAuthError as e:
        raise HTTPException(status_code=401, detail=str(e))
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(status_code=500, detail="Token refresh failed")


# Database endpoints
@router.post("/db/query")
async def query_database(
    query: DatabaseQuery,
    user: UserProfile = Depends(get_current_user)
):
    """Execute a database query."""
    try:
        db_service = get_database_service()
        result = await db_service.query(query)
        return {"data": result, "count": len(result)}
    except SupabaseDBError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Database query error: {e}")
        raise HTTPException(status_code=500, detail="Database query failed")


@router.post("/db/insert")
async def insert_data(
    insert: DatabaseInsert,
    user: UserProfile = Depends(get_current_user)
):
    """Insert data into database."""
    try:
        db_service = get_database_service()
        result = await db_service.insert(insert)
        return {"data": result, "count": len(result)}
    except SupabaseDBError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Database insert error: {e}")
        raise HTTPException(status_code=500, detail="Database insert failed")


@router.put("/db/update")
async def update_data(
    update: DatabaseUpdate,
    user: UserProfile = Depends(get_current_user)
):
    """Update data in database."""
    try:
        db_service = get_database_service()
        result = await db_service.update(update)
        return {"data": result, "count": len(result)}
    except SupabaseDBError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Database update error: {e}")
        raise HTTPException(status_code=500, detail="Database update failed")


@router.delete("/db/delete")
async def delete_data(
    delete: DatabaseDelete,
    user: UserProfile = Depends(get_current_user)
):
    """Delete data from database."""
    try:
        db_service = get_database_service()
        result = await db_service.delete(delete)
        return {"data": result, "count": len(result)}
    except SupabaseDBError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Database delete error: {e}")
        raise HTTPException(status_code=500, detail="Database delete failed")


@router.get("/db/schema/{table_name}")
async def get_table_schema(
    table_name: str,
    user: UserProfile = Depends(get_current_user)
):
    """Get table schema information."""
    try:
        db_service = get_database_service()
        schema = await db_service.get_table_schema(table_name)
        return schema
    except SupabaseDBError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Get schema error: {e}")
        raise HTTPException(status_code=500, detail="Get schema failed")


@router.get("/db/count/{table_name}")
async def count_records(
    table_name: str,
    user: UserProfile = Depends(get_current_user)
):
    """Count records in a table."""
    try:
        db_service = get_database_service()
        count = await db_service.count_records(table_name)
        return {"table": table_name, "count": count}
    except SupabaseDBError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Count records error: {e}")
        raise HTTPException(status_code=500, detail="Count records failed")


# Storage endpoints
@router.post("/storage/upload")
async def upload_file(
    file: UploadFile = File(...),
    bucket: Optional[str] = Form(None),
    path: Optional[str] = Form(None),
    user: UserProfile = Depends(get_current_user)
):
    """Upload a file to storage."""
    try:
        storage_service = get_storage_service()
        
        # Use filename as path if not provided
        file_path = path or file.filename
        if not file_path:
            raise HTTPException(status_code=400, detail="File path is required")
        
        # Add user ID to path for isolation
        user_path = f"users/{user.id}/{file_path}"
        
        # Read file data
        file_data = await file.read()
        
        # Upload file
        result_path = await storage_service.upload_file(
            user_path,
            file_data,
            bucket,
            file.content_type
        )
        
        return {
            "message": "File uploaded successfully",
            "path": result_path,
            "size": len(file_data),
            "content_type": file.content_type
        }
    except SupabaseStorageError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"File upload error: {e}")
        raise HTTPException(status_code=500, detail="File upload failed")


@router.get("/storage/download/{file_path:path}")
async def download_file(
    file_path: str,
    bucket: Optional[str] = None,
    user: UserProfile = Depends(get_current_user)
):
    """Download a file from storage."""
    try:
        storage_service = get_storage_service()
        
        # Add user ID to path for isolation
        user_path = f"users/{user.id}/{file_path}"
        
        # Download file
        file_data = await storage_service.download_file(user_path, bucket)
        
        # Return as streaming response
        return StreamingResponse(
            io.BytesIO(file_data),
            media_type="application/octet-stream",
            headers={"Content-Disposition": f"attachment; filename={file_path.split('/')[-1]}"}
        )
    except SupabaseStorageError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"File download error: {e}")
        raise HTTPException(status_code=500, detail="File download failed")


@router.delete("/storage/delete/{file_path:path}")
async def delete_file(
    file_path: str,
    bucket: Optional[str] = None,
    user: UserProfile = Depends(get_current_user)
):
    """Delete a file from storage."""
    try:
        storage_service = get_storage_service()
        
        # Add user ID to path for isolation
        user_path = f"users/{user.id}/{file_path}"
        
        # Delete file
        success = await storage_service.delete_file(user_path, bucket)
        
        return {"message": "File deleted successfully", "path": file_path}
    except SupabaseStorageError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"File delete error: {e}")
        raise HTTPException(status_code=500, detail="File delete failed")


@router.get("/storage/list", response_model=List[FileInfo])
async def list_files(
    folder: str = "",
    bucket: Optional[str] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
    user: UserProfile = Depends(get_current_user)
):
    """List files in storage."""
    try:
        storage_service = get_storage_service()
        
        # Add user ID to folder path for isolation
        user_folder = f"users/{user.id}/{folder}".rstrip("/")
        
        # List files
        files = await storage_service.list_files(user_folder, bucket, limit, offset)
        
        return files
    except SupabaseStorageError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"List files error: {e}")
        raise HTTPException(status_code=500, detail="List files failed")


@router.get("/storage/url/{file_path:path}")
async def get_file_url(
    file_path: str,
    bucket: Optional[str] = None,
    signed: bool = False,
    expires_in: int = 3600,
    user: UserProfile = Depends(get_current_user)
):
    """Get URL for a file."""
    try:
        storage_service = get_storage_service()
        
        # Add user ID to path for isolation
        user_path = f"users/{user.id}/{file_path}"
        
        if signed:
            url = await storage_service.create_signed_url(user_path, expires_in, bucket)
        else:
            url = await storage_service.get_public_url(user_path, bucket)
        
        return {"url": url, "expires_in": expires_in if signed else None}
    except SupabaseStorageError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Get file URL error: {e}")
        raise HTTPException(status_code=500, detail="Get file URL failed")


# Health check endpoint
@router.get("/health")
async def supabase_health_check(user: Optional[UserProfile] = Depends(get_current_user_optional)):
    """Check Supabase service health."""
    try:
        from ..services.supabase_client import get_supabase_service
        
        supabase_service = get_supabase_service()
        health = supabase_service.health_check()
        
        return {
            "supabase": health,
            "authenticated": user is not None,
            "user_id": user.id if user else None
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "supabase": {"status": "error", "message": str(e)},
            "authenticated": False,
            "user_id": None
        }
