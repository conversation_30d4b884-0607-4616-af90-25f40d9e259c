"""
Workflow automation API endpoints.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Query, Form
from pydantic import BaseModel

from ..agents.automation_agent import automation_agent
from ..services.workflow_engine import workflow_engine
from ..models import ChatResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/workflow", tags=["Workflow"])


class WorkflowCreateRequest(BaseModel):
    """Request model for creating a workflow."""
    name: str
    description: str
    steps: List[Dict[str, Any]]
    conversation_id: str = "default"


class WorkflowExecuteRequest(BaseModel):
    """Request model for executing a workflow."""
    workflow_id: str
    conversation_id: str = "default"


class WorkflowDesignRequest(BaseModel):
    """Request model for designing a workflow."""
    task_description: str
    conversation_id: str = "default"
    auto_execute: bool = False
    max_cost: Optional[float] = None
    preferred_provider: Optional[str] = None


class WorkflowResponse(BaseModel):
    """Response model for workflow operations."""
    status: str
    workflow_id: Optional[str] = None
    message: Optional[str] = None
    execution_time_ms: Optional[float] = None
    data: Optional[Dict[str, Any]] = None


@router.post("/create", response_model=WorkflowResponse)
async def create_workflow(request: WorkflowCreateRequest):
    """
    Create a new workflow with specified steps.
    
    Steps can include various actions like:
    - delay: Wait for specified seconds
    - log: Log a message
    - http_request: Make HTTP requests
    - data_transform: Transform data between steps
    - conditional: Execute conditional logic
    - agent_call: Call other AI agents
    """
    try:
        result = await automation_agent.create_workflow(
            name=request.name,
            description=request.description,
            steps=request.steps,
            conversation_id=request.conversation_id
        )
        
        if result.get("status") == "error":
            raise HTTPException(status_code=400, detail=result["message"])
        
        return WorkflowResponse(
            status="success",
            workflow_id=result["workflow_id"],
            message=f"Workflow '{request.name}' created successfully",
            execution_time_ms=result.get("creation_time_ms"),
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create workflow: {str(e)}")


@router.post("/execute", response_model=WorkflowResponse)
async def execute_workflow(request: WorkflowExecuteRequest):
    """
    Execute an existing workflow by ID.
    
    Returns the execution results including step-by-step status and any outputs.
    """
    try:
        result = await automation_agent.execute_workflow(
            workflow_id=request.workflow_id,
            conversation_id=request.conversation_id
        )
        
        if result.get("status") == "error":
            raise HTTPException(status_code=400, detail=result["message"])
        
        return WorkflowResponse(
            status="success",
            workflow_id=request.workflow_id,
            message=f"Workflow executed with status: {result.get('status')}",
            execution_time_ms=result.get("execution_time_ms"),
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to execute workflow: {str(e)}")


@router.post("/design", response_model=ChatResponse)
async def design_workflow(request: WorkflowDesignRequest):
    """
    Design a workflow based on natural language task description.
    
    Uses AI to analyze the task and suggest appropriate workflow steps.
    Optionally can auto-execute the designed workflow.
    """
    try:
        if request.auto_execute:
            # Design and execute in one call
            result = await automation_agent.design_and_execute_workflow(
                task_description=request.task_description,
                conversation_id=request.conversation_id,
                auto_execute=True,
                max_cost=request.max_cost,
                preferred_provider=request.preferred_provider
            )
            
            if result.get("status") == "error":
                raise HTTPException(status_code=400, detail=result["message"])
            
            return ChatResponse(
                message=f"Workflow designed and executed successfully. Workflow ID: {result.get('workflow_id')}",
                conversation_id=request.conversation_id,
                metadata={
                    "workflow_result": result,
                    "auto_executed": True
                }
            )
        else:
            # Just design the workflow
            response = await automation_agent.design_workflow(
                task_description=request.task_description,
                conversation_id=request.conversation_id,
                max_cost=request.max_cost,
                preferred_provider=request.preferred_provider
            )
            
            return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error designing workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to design workflow: {str(e)}")


@router.get("/status/{workflow_id}")
async def get_workflow_status(workflow_id: str):
    """Get the current status of a workflow."""
    try:
        status = await automation_agent.get_workflow_status(workflow_id)
        
        if not status:
            raise HTTPException(status_code=404, detail=f"Workflow not found: {workflow_id}")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting workflow status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get workflow status: {str(e)}")


@router.get("/list")
async def list_workflows():
    """List all workflows in the system."""
    try:
        workflows = await automation_agent.list_engine_workflows()
        
        return {
            "total_workflows": len(workflows),
            "workflows": workflows
        }
        
    except Exception as e:
        logger.error(f"Error listing workflows: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list workflows: {str(e)}")


@router.get("/templates")
async def get_workflow_templates():
    """Get predefined workflow templates for common tasks."""
    templates = {
        "data_processing": {
            "name": "Data Processing Pipeline",
            "description": "Process data through multiple transformation steps",
            "steps": [
                {
                    "id": "fetch_data",
                    "name": "Fetch Data",
                    "action": "http_request",
                    "parameters": {
                        "url": "https://api.example.com/data",
                        "method": "GET",
                        "store_in_context": "raw_data"
                    }
                },
                {
                    "id": "transform_data",
                    "name": "Transform Data",
                    "action": "data_transform",
                    "parameters": {
                        "source_key": "raw_data",
                        "target_key": "processed_data",
                        "transform_type": "json_extract",
                        "json_path": "data.items"
                    },
                    "dependencies": ["fetch_data"]
                },
                {
                    "id": "log_result",
                    "name": "Log Result",
                    "action": "log",
                    "parameters": {
                        "message": "Data processing completed. Processed {processed_data} items.",
                        "level": "info"
                    },
                    "dependencies": ["transform_data"]
                }
            ]
        },
        "notification_workflow": {
            "name": "Notification Workflow",
            "description": "Send notifications through multiple channels",
            "steps": [
                {
                    "id": "prepare_message",
                    "name": "Prepare Message",
                    "action": "data_transform",
                    "parameters": {
                        "source_key": "input_message",
                        "target_key": "formatted_message",
                        "transform_type": "format_string",
                        "template": "Alert: {value} at {timestamp}"
                    }
                },
                {
                    "id": "send_email",
                    "name": "Send Email",
                    "action": "http_request",
                    "parameters": {
                        "url": "https://api.emailservice.com/send",
                        "method": "POST",
                        "json": {
                            "to": "<EMAIL>",
                            "subject": "System Alert",
                            "body": "{formatted_message}"
                        }
                    },
                    "dependencies": ["prepare_message"]
                },
                {
                    "id": "log_notification",
                    "name": "Log Notification",
                    "action": "log",
                    "parameters": {
                        "message": "Notification sent: {formatted_message}",
                        "level": "info"
                    },
                    "dependencies": ["send_email"]
                }
            ]
        },
        "ai_agent_chain": {
            "name": "AI Agent Chain",
            "description": "Chain multiple AI agents for complex processing",
            "steps": [
                {
                    "id": "analyze_input",
                    "name": "Analyze Input",
                    "action": "agent_call",
                    "parameters": {
                        "agent_type": "chatbot",
                        "method": "chat",
                        "parameters": {
                            "message": "Analyze this input: {input_text}",
                            "conversation_id": "workflow_analysis"
                        },
                        "store_in_context": "analysis_result"
                    }
                },
                {
                    "id": "generate_response",
                    "name": "Generate Response",
                    "action": "agent_call",
                    "parameters": {
                        "agent_type": "marketing",
                        "method": "chat",
                        "parameters": {
                            "message": "Create marketing content based on: {analysis_result}",
                            "conversation_id": "workflow_marketing"
                        },
                        "store_in_context": "marketing_content"
                    },
                    "dependencies": ["analyze_input"]
                },
                {
                    "id": "finalize_output",
                    "name": "Finalize Output",
                    "action": "log",
                    "parameters": {
                        "message": "AI agent chain completed. Generated: {marketing_content}",
                        "level": "info"
                    },
                    "dependencies": ["generate_response"]
                }
            ]
        }
    }
    
    return {
        "available_templates": list(templates.keys()),
        "templates": templates
    }


@router.get("/actions")
async def get_available_actions():
    """Get list of available workflow actions and their parameters."""
    actions = {
        "delay": {
            "description": "Wait for specified number of seconds",
            "parameters": {
                "seconds": "Number of seconds to wait (default: 1)"
            }
        },
        "log": {
            "description": "Log a message with specified level",
            "parameters": {
                "message": "Message to log (supports context variable formatting)",
                "level": "Log level: debug, info, warning, error (default: info)"
            }
        },
        "http_request": {
            "description": "Make HTTP requests to external APIs",
            "parameters": {
                "url": "Request URL (supports context variable formatting)",
                "method": "HTTP method: GET, POST, PUT, DELETE (default: GET)",
                "headers": "Request headers (optional)",
                "data": "Request body data (optional)",
                "json": "JSON request body (optional)",
                "timeout": "Request timeout in seconds (default: 30)",
                "store_in_context": "Key to store response in workflow context (optional)"
            }
        },
        "data_transform": {
            "description": "Transform data between workflow steps",
            "parameters": {
                "source_key": "Key in workflow context to read data from",
                "target_key": "Key in workflow context to store result",
                "transform_type": "Type of transformation: copy, json_extract, format_string",
                "json_path": "JSON path for json_extract (dot notation)",
                "template": "Template string for format_string transformation"
            }
        },
        "conditional": {
            "description": "Execute conditional logic",
            "parameters": {
                "condition_type": "Type of condition: equals, exists",
                "left": "Left operand for comparison",
                "right": "Right operand for comparison",
                "key": "Key to check existence for 'exists' condition",
                "result_key": "Key to store condition result in context"
            }
        },
        "agent_call": {
            "description": "Call other AI agents",
            "parameters": {
                "agent_type": "Type of agent: chatbot, rag, image, document, marketing, automation",
                "method": "Agent method to call (default: chat)",
                "parameters": "Parameters to pass to the agent",
                "store_in_context": "Key to store agent response in context (optional)"
            }
        }
    }
    
    return {
        "available_actions": list(actions.keys()),
        "action_details": actions
    }


@router.get("/health")
async def workflow_health_check():
    """Check workflow service health."""
    try:
        workflows = await automation_agent.list_engine_workflows()
        
        return {
            "status": "healthy",
            "total_workflows": len(workflows),
            "workflow_engine": "available",
            "automation_agent": "available"
        }
        
    except Exception as e:
        logger.error(f"Workflow health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
