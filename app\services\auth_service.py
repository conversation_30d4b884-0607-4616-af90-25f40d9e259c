"""
Authentication service using Supabase Auth.
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import jwt
from pydantic import BaseModel, EmailStr

from .supabase_client import get_supabase_service, handle_supabase_error, SupabaseAuthError
from ..config import get_settings

logger = logging.getLogger(__name__)


class UserSignUp(BaseModel):
    """User sign-up request model."""
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class UserSignIn(BaseModel):
    """User sign-in request model."""
    email: EmailStr
    password: str


class UserProfile(BaseModel):
    """User profile model."""
    id: str
    email: str
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    metadata: Optional[Dict[str, Any]] = None


class AuthResponse(BaseModel):
    """Authentication response model."""
    user: UserProfile
    access_token: str
    refresh_token: str
    expires_at: datetime


class AuthService:
    """Authentication service using Supabase Auth."""
    
    def __init__(self):
        """Initialize the authentication service."""
        self.supabase = get_supabase_service()
        self.settings = get_settings()
    
    @handle_supabase_error
    async def sign_up(self, user_data: UserSignUp) -> AuthResponse:
        """Sign up a new user."""
        if not self.supabase.is_available:
            raise SupabaseAuthError("Supabase is not available")
        
        # Prepare user metadata
        metadata = user_data.metadata or {}
        if user_data.full_name:
            metadata["full_name"] = user_data.full_name
        
        # Sign up user with Supabase
        response = self.supabase.client.auth.sign_up({
            "email": user_data.email,
            "password": user_data.password,
            "options": {
                "data": metadata
            }
        })
        
        if not response.user:
            raise SupabaseAuthError("Failed to create user")
        
        # Create user profile
        user_profile = UserProfile(
            id=response.user.id,
            email=response.user.email,
            full_name=metadata.get("full_name"),
            avatar_url=response.user.user_metadata.get("avatar_url"),
            created_at=datetime.fromisoformat(response.user.created_at.replace('Z', '+00:00')),
            updated_at=datetime.fromisoformat(response.user.updated_at.replace('Z', '+00:00')),
            metadata=response.user.user_metadata
        )
        
        return AuthResponse(
            user=user_profile,
            access_token=response.session.access_token,
            refresh_token=response.session.refresh_token,
            expires_at=datetime.fromtimestamp(response.session.expires_at)
        )
    
    @handle_supabase_error
    async def sign_in(self, credentials: UserSignIn) -> AuthResponse:
        """Sign in an existing user."""
        if not self.supabase.is_available:
            raise SupabaseAuthError("Supabase is not available")
        
        # Sign in user with Supabase
        response = self.supabase.client.auth.sign_in_with_password({
            "email": credentials.email,
            "password": credentials.password
        })
        
        if not response.user or not response.session:
            raise SupabaseAuthError("Invalid credentials")
        
        # Create user profile
        user_profile = UserProfile(
            id=response.user.id,
            email=response.user.email,
            full_name=response.user.user_metadata.get("full_name"),
            avatar_url=response.user.user_metadata.get("avatar_url"),
            created_at=datetime.fromisoformat(response.user.created_at.replace('Z', '+00:00')),
            updated_at=datetime.fromisoformat(response.user.updated_at.replace('Z', '+00:00')),
            metadata=response.user.user_metadata
        )
        
        return AuthResponse(
            user=user_profile,
            access_token=response.session.access_token,
            refresh_token=response.session.refresh_token,
            expires_at=datetime.fromtimestamp(response.session.expires_at)
        )
    
    @handle_supabase_error
    async def sign_out(self, access_token: str) -> bool:
        """Sign out a user."""
        if not self.supabase.is_available:
            raise SupabaseAuthError("Supabase is not available")
        
        # Set the session token
        self.supabase.client.auth.set_session(access_token, "")
        
        # Sign out
        response = self.supabase.client.auth.sign_out()
        return True
    
    @handle_supabase_error
    async def get_user(self, access_token: str) -> Optional[UserProfile]:
        """Get user profile from access token."""
        if not self.supabase.is_available:
            raise SupabaseAuthError("Supabase is not available")
        
        try:
            # Set the session token
            self.supabase.client.auth.set_session(access_token, "")
            
            # Get user
            response = self.supabase.client.auth.get_user()
            
            if not response.user:
                return None
            
            return UserProfile(
                id=response.user.id,
                email=response.user.email,
                full_name=response.user.user_metadata.get("full_name"),
                avatar_url=response.user.user_metadata.get("avatar_url"),
                created_at=datetime.fromisoformat(response.user.created_at.replace('Z', '+00:00')),
                updated_at=datetime.fromisoformat(response.user.updated_at.replace('Z', '+00:00')),
                metadata=response.user.user_metadata
            )
        except Exception:
            return None
    
    @handle_supabase_error
    async def refresh_token(self, refresh_token: str) -> AuthResponse:
        """Refresh an access token."""
        if not self.supabase.is_available:
            raise SupabaseAuthError("Supabase is not available")
        
        response = self.supabase.client.auth.refresh_session(refresh_token)
        
        if not response.user or not response.session:
            raise SupabaseAuthError("Failed to refresh token")
        
        user_profile = UserProfile(
            id=response.user.id,
            email=response.user.email,
            full_name=response.user.user_metadata.get("full_name"),
            avatar_url=response.user.user_metadata.get("avatar_url"),
            created_at=datetime.fromisoformat(response.user.created_at.replace('Z', '+00:00')),
            updated_at=datetime.fromisoformat(response.user.updated_at.replace('Z', '+00:00')),
            metadata=response.user.user_metadata
        )
        
        return AuthResponse(
            user=user_profile,
            access_token=response.session.access_token,
            refresh_token=response.session.refresh_token,
            expires_at=datetime.fromtimestamp(response.session.expires_at)
        )
    
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify a JWT token (for custom JWT validation if needed)."""
        try:
            if not self.settings.jwt_secret:
                logger.warning("JWT secret not configured")
                return None
            
            payload = jwt.decode(
                token,
                self.settings.jwt_secret,
                algorithms=[self.settings.jwt_algorithm]
            )
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token has expired")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT token")
            return None


# Global auth service instance
_auth_service: Optional[AuthService] = None


def get_auth_service() -> AuthService:
    """Get the global authentication service instance."""
    global _auth_service
    if _auth_service is None:
        _auth_service = AuthService()
    return _auth_service
