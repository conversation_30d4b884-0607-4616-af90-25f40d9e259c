"""
Request batching service for performance optimization.
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, Any, List, Optional, Callable, Awaitable
from dataclasses import dataclass, field
from enum import Enum

from .observability import observability_service, SpanMetadata

logger = logging.getLogger(__name__)


class BatchStatus(Enum):
    """Status of a batch request."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class BatchRequest:
    """Individual request in a batch."""
    id: str
    data: Dict[str, Any]
    created_at: float
    priority: int = 0
    result: Optional[Any] = None
    error: Optional[str] = None
    status: BatchStatus = BatchStatus.PENDING


@dataclass
class Batch:
    """Collection of requests to be processed together."""
    id: str
    batch_type: str
    requests: List[BatchRequest] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    status: BatchStatus = BatchStatus.PENDING
    max_size: int = 10
    max_wait_time: float = 5.0  # seconds
    processor: Optional[Callable] = None


class BatchingService:
    """Service for batching requests to improve throughput."""
    
    def __init__(self):
        self.batches: Dict[str, Batch] = {}
        self.pending_batches: Dict[str, Batch] = {}  # batches waiting for more requests
        self.processing_tasks: Dict[str, asyncio.Task] = {}
        self.batch_processors: Dict[str, Callable] = {}
        
        # Register default batch processors
        self._register_default_processors()
    
    def _register_default_processors(self):
        """Register default batch processors."""
        self.batch_processors.update({
            "ai_inference": self._process_ai_inference_batch,
            "task_analysis": self._process_task_analysis_batch,
            "rag_search": self._process_rag_search_batch,
            "image_processing": self._process_image_processing_batch
        })
    
    def register_batch_processor(self, batch_type: str, processor: Callable):
        """Register a custom batch processor."""
        self.batch_processors[batch_type] = processor
        logger.info(f"Registered batch processor: {batch_type}")
    
    async def add_request(
        self,
        batch_type: str,
        request_data: Dict[str, Any],
        priority: int = 0,
        max_wait_time: Optional[float] = None
    ) -> str:
        """Add a request to a batch."""
        request_id = str(uuid.uuid4())
        
        # Create batch request
        batch_request = BatchRequest(
            id=request_id,
            data=request_data,
            created_at=time.time(),
            priority=priority
        )
        
        # Find or create a pending batch
        batch = await self._get_or_create_batch(
            batch_type=batch_type,
            max_wait_time=max_wait_time
        )
        
        # Add request to batch
        batch.requests.append(batch_request)
        
        logger.debug(f"Added request {request_id} to batch {batch.id}")
        
        # Check if batch should be processed
        if len(batch.requests) >= batch.max_size:
            await self._process_batch(batch)
        
        return request_id
    
    async def _get_or_create_batch(
        self,
        batch_type: str,
        max_wait_time: Optional[float] = None
    ) -> Batch:
        """Get an existing pending batch or create a new one."""
        # Look for existing pending batch of this type
        for batch in self.pending_batches.values():
            if (batch.batch_type == batch_type and 
                batch.status == BatchStatus.PENDING and
                len(batch.requests) < batch.max_size):
                return batch
        
        # Create new batch
        batch_id = str(uuid.uuid4())
        batch = Batch(
            id=batch_id,
            batch_type=batch_type,
            max_wait_time=max_wait_time or 5.0,
            processor=self.batch_processors.get(batch_type)
        )
        
        self.batches[batch_id] = batch
        self.pending_batches[batch_id] = batch
        
        # Start timer for batch processing
        asyncio.create_task(self._batch_timer(batch))
        
        logger.info(f"Created new batch: {batch_id} for type: {batch_type}")
        
        return batch
    
    async def _batch_timer(self, batch: Batch):
        """Timer to process batch after max wait time."""
        await asyncio.sleep(batch.max_wait_time)
        
        if (batch.id in self.pending_batches and 
            batch.status == BatchStatus.PENDING and
            len(batch.requests) > 0):
            await self._process_batch(batch)
    
    async def _process_batch(self, batch: Batch):
        """Process a batch of requests."""
        if batch.status != BatchStatus.PENDING:
            return
        
        batch.status = BatchStatus.PROCESSING
        batch.started_at = time.time()
        
        # Remove from pending batches
        if batch.id in self.pending_batches:
            del self.pending_batches[batch.id]
        
        logger.info(f"Processing batch {batch.id} with {len(batch.requests)} requests")
        
        metadata_span = SpanMetadata(
            operation_type="batch_processing",
            batch_id=batch.id,
            batch_type=batch.batch_type,
            request_count=len(batch.requests)
        )
        
        async with observability_service.trace_operation("process_batch", metadata_span) as span:
            try:
                if batch.processor:
                    await batch.processor(batch)
                else:
                    # Default processing - mark all as failed
                    for request in batch.requests:
                        request.status = BatchStatus.FAILED
                        request.error = f"No processor registered for batch type: {batch.batch_type}"
                
                batch.status = BatchStatus.COMPLETED
                batch.completed_at = time.time()
                
                # Calculate statistics
                successful_requests = [r for r in batch.requests if r.status == BatchStatus.COMPLETED]
                failed_requests = [r for r in batch.requests if r.status == BatchStatus.FAILED]
                
                processing_time = batch.completed_at - batch.started_at
                
                logger.info(
                    f"Batch {batch.id} completed: "
                    f"{len(successful_requests)} successful, "
                    f"{len(failed_requests)} failed, "
                    f"processing time: {processing_time:.2f}s"
                )
                
                # Log batch processing
                observability_service.log_agent_execution(
                    agent_type="batching",
                    request_data={
                        "batch_type": batch.batch_type,
                        "request_count": len(batch.requests)
                    },
                    response_data={
                        "successful_requests": len(successful_requests),
                        "failed_requests": len(failed_requests),
                        "processing_time_ms": processing_time * 1000
                    },
                    duration_ms=processing_time * 1000,
                    model_name=f"batch_{batch.batch_type}",
                    provider="internal"
                )
                
            except Exception as e:
                batch.status = BatchStatus.FAILED
                batch.completed_at = time.time()
                
                # Mark all requests as failed
                for request in batch.requests:
                    if request.status == BatchStatus.PENDING:
                        request.status = BatchStatus.FAILED
                        request.error = f"Batch processing failed: {str(e)}"
                
                logger.error(f"Batch {batch.id} processing failed: {e}")
                
                observability_service.log_error(
                    operation_name="process_batch",
                    error=e,
                    context={
                        "batch_id": batch.id,
                        "batch_type": batch.batch_type,
                        "request_count": len(batch.requests)
                    }
                )
    
    async def get_request_result(self, request_id: str, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """Get the result of a batched request."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Find the request in all batches
            for batch in self.batches.values():
                for request in batch.requests:
                    if request.id == request_id:
                        if request.status == BatchStatus.COMPLETED:
                            return {
                                "request_id": request_id,
                                "status": "completed",
                                "result": request.result,
                                "batch_id": batch.id,
                                "processing_time_ms": (batch.completed_at - batch.started_at) * 1000 if batch.completed_at else None
                            }
                        elif request.status == BatchStatus.FAILED:
                            return {
                                "request_id": request_id,
                                "status": "failed",
                                "error": request.error,
                                "batch_id": batch.id
                            }
                        elif request.status in [BatchStatus.PENDING, BatchStatus.PROCESSING]:
                            # Still processing, wait a bit
                            await asyncio.sleep(0.1)
                            break
            else:
                # Request not found
                return None
        
        # Timeout
        return {
            "request_id": request_id,
            "status": "timeout",
            "error": f"Request timed out after {timeout} seconds"
        }
    
    # Default batch processors
    
    async def _process_ai_inference_batch(self, batch: Batch):
        """Process a batch of AI inference requests."""
        try:
            # Group requests by agent type and model
            grouped_requests = {}
            for request in batch.requests:
                agent_type = request.data.get("agent_type", "default")
                model = request.data.get("model", "default")
                key = f"{agent_type}_{model}"
                
                if key not in grouped_requests:
                    grouped_requests[key] = []
                grouped_requests[key].append(request)
            
            # Process each group
            for group_key, requests in grouped_requests.items():
                try:
                    # Simulate batch processing (in production, this would call actual AI providers)
                    for request in requests:
                        request.result = {
                            "message": f"Batch processed response for: {request.data.get('message', 'No message')}",
                            "batch_processed": True,
                            "group_key": group_key
                        }
                        request.status = BatchStatus.COMPLETED
                        
                except Exception as e:
                    for request in requests:
                        request.status = BatchStatus.FAILED
                        request.error = str(e)
                        
        except Exception as e:
            for request in batch.requests:
                request.status = BatchStatus.FAILED
                request.error = str(e)
    
    async def _process_task_analysis_batch(self, batch: Batch):
        """Process a batch of task analysis requests."""
        try:
            # Simulate batch task analysis
            for request in batch.requests:
                task_text = request.data.get("task_text", "")
                
                # Simulate complexity analysis
                request.result = {
                    "complexity_score": min(len(task_text) / 100, 1.0),
                    "complexity_label": "medium",
                    "features_count": len(task_text.split()),
                    "batch_processed": True
                }
                request.status = BatchStatus.COMPLETED
                
        except Exception as e:
            for request in batch.requests:
                request.status = BatchStatus.FAILED
                request.error = str(e)
    
    async def _process_rag_search_batch(self, batch: Batch):
        """Process a batch of RAG search requests."""
        try:
            # Group by knowledge base
            grouped_requests = {}
            for request in batch.requests:
                kb = request.data.get("knowledge_base", "default")
                if kb not in grouped_requests:
                    grouped_requests[kb] = []
                grouped_requests[kb].append(request)
            
            # Process each knowledge base group
            for kb, requests in grouped_requests.items():
                try:
                    # Simulate batch RAG search
                    for request in requests:
                        query = request.data.get("query", "")
                        
                        request.result = {
                            "documents": [
                                {
                                    "content": f"Batch search result for: {query}",
                                    "score": 0.8,
                                    "metadata": {"batch_processed": True}
                                }
                            ],
                            "total_results": 1,
                            "knowledge_base": kb
                        }
                        request.status = BatchStatus.COMPLETED
                        
                except Exception as e:
                    for request in requests:
                        request.status = BatchStatus.FAILED
                        request.error = str(e)
                        
        except Exception as e:
            for request in batch.requests:
                request.status = BatchStatus.FAILED
                request.error = str(e)
    
    async def _process_image_processing_batch(self, batch: Batch):
        """Process a batch of image processing requests."""
        try:
            # Simulate batch image processing
            for request in batch.requests:
                image_data = request.data.get("image_data")
                operation = request.data.get("operation", "analyze")
                
                request.result = {
                    "operation": operation,
                    "result": f"Batch processed image with operation: {operation}",
                    "batch_processed": True,
                    "has_image_data": image_data is not None
                }
                request.status = BatchStatus.COMPLETED
                
        except Exception as e:
            for request in batch.requests:
                request.status = BatchStatus.FAILED
                request.error = str(e)
    
    def get_batch_stats(self) -> Dict[str, Any]:
        """Get batching service statistics."""
        total_batches = len(self.batches)
        pending_batches = len(self.pending_batches)
        completed_batches = len([b for b in self.batches.values() if b.status == BatchStatus.COMPLETED])
        failed_batches = len([b for b in self.batches.values() if b.status == BatchStatus.FAILED])
        
        total_requests = sum(len(b.requests) for b in self.batches.values())
        
        return {
            "total_batches": total_batches,
            "pending_batches": pending_batches,
            "completed_batches": completed_batches,
            "failed_batches": failed_batches,
            "total_requests": total_requests,
            "registered_processors": list(self.batch_processors.keys())
        }


# Global batching service instance
batching_service = BatchingService()
