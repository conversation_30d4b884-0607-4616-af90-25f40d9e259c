"""
Caching service for performance optimization.
"""

import json
import logging
import time
import hashlib
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class CacheType(Enum):
    """Types of cache storage."""
    MEMORY = "memory"
    REDIS = "redis"
    HYBRID = "hybrid"


@dataclass
class CacheEntry:
    """Represents a cache entry."""
    key: str
    value: Any
    created_at: float
    expires_at: Optional[float] = None
    access_count: int = 0
    last_accessed: float = 0
    size_bytes: int = 0


class CachingService:
    """Service for caching AI responses and computations."""
    
    def __init__(self, cache_type: CacheType = CacheType.MEMORY):
        self.cache_type = cache_type
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.redis_client = None
        self.max_memory_size = 100 * 1024 * 1024  # 100MB
        self.current_memory_size = 0
        self.default_ttl = 3600  # 1 hour
        
        # Initialize Redis if needed
        if cache_type in [CacheType.REDIS, CacheType.HYBRID]:
            self._init_redis()
    
    def _init_redis(self):
        """Initialize Redis connection."""
        try:
            import redis.asyncio as redis
            self.redis_client = redis.Redis(
                host="localhost",
                port=6379,
                db=0,
                decode_responses=True
            )
            logger.info("Redis cache initialized")
        except ImportError:
            logger.warning("Redis not available, falling back to memory cache")
            self.cache_type = CacheType.MEMORY
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            self.cache_type = CacheType.MEMORY
    
    def _generate_cache_key(self, prefix: str, data: Dict[str, Any]) -> str:
        """Generate a cache key from data."""
        # Create a deterministic hash of the data
        data_str = json.dumps(data, sort_keys=True)
        hash_obj = hashlib.md5(data_str.encode())
        return f"{prefix}:{hash_obj.hexdigest()}"
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of a value in bytes."""
        try:
            return len(json.dumps(value, default=str).encode('utf-8'))
        except:
            return len(str(value).encode('utf-8'))
    
    def _evict_lru(self, required_space: int):
        """Evict least recently used items to free space."""
        if not self.memory_cache:
            return
        
        # Sort by last accessed time
        sorted_entries = sorted(
            self.memory_cache.items(),
            key=lambda x: x[1].last_accessed
        )
        
        freed_space = 0
        for key, entry in sorted_entries:
            if freed_space >= required_space:
                break
            
            freed_space += entry.size_bytes
            self.current_memory_size -= entry.size_bytes
            del self.memory_cache[key]
            logger.debug(f"Evicted cache entry: {key}")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get a value from cache."""
        try:
            # Try memory cache first
            if key in self.memory_cache:
                entry = self.memory_cache[key]
                
                # Check expiration
                if entry.expires_at and time.time() > entry.expires_at:
                    del self.memory_cache[key]
                    self.current_memory_size -= entry.size_bytes
                    return None
                
                # Update access stats
                entry.access_count += 1
                entry.last_accessed = time.time()
                
                return entry.value
            
            # Try Redis if available
            if self.redis_client and self.cache_type in [CacheType.REDIS, CacheType.HYBRID]:
                try:
                    cached_data = await self.redis_client.get(key)
                    if cached_data:
                        value = json.loads(cached_data)
                        
                        # Store in memory cache for faster access
                        if self.cache_type == CacheType.HYBRID:
                            await self.set(key, value, ttl=self.default_ttl, store_in_memory=True)
                        
                        return value
                except Exception as e:
                    logger.error(f"Redis get error: {e}")
            
            return None
            
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        store_in_memory: bool = True
    ) -> bool:
        """Set a value in cache."""
        try:
            ttl = ttl or self.default_ttl
            expires_at = time.time() + ttl if ttl > 0 else None
            
            # Store in memory cache
            if store_in_memory and self.cache_type in [CacheType.MEMORY, CacheType.HYBRID]:
                size_bytes = self._calculate_size(value)
                
                # Check if we need to evict items
                if self.current_memory_size + size_bytes > self.max_memory_size:
                    self._evict_lru(size_bytes)
                
                entry = CacheEntry(
                    key=key,
                    value=value,
                    created_at=time.time(),
                    expires_at=expires_at,
                    access_count=0,
                    last_accessed=time.time(),
                    size_bytes=size_bytes
                )
                
                self.memory_cache[key] = entry
                self.current_memory_size += size_bytes
            
            # Store in Redis
            if self.redis_client and self.cache_type in [CacheType.REDIS, CacheType.HYBRID]:
                try:
                    cached_data = json.dumps(value, default=str)
                    if ttl > 0:
                        await self.redis_client.setex(key, ttl, cached_data)
                    else:
                        await self.redis_client.set(key, cached_data)
                except Exception as e:
                    logger.error(f"Redis set error: {e}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete a value from cache."""
        try:
            deleted = False
            
            # Delete from memory cache
            if key in self.memory_cache:
                entry = self.memory_cache[key]
                self.current_memory_size -= entry.size_bytes
                del self.memory_cache[key]
                deleted = True
            
            # Delete from Redis
            if self.redis_client and self.cache_type in [CacheType.REDIS, CacheType.HYBRID]:
                try:
                    redis_deleted = await self.redis_client.delete(key)
                    deleted = deleted or redis_deleted > 0
                except Exception as e:
                    logger.error(f"Redis delete error: {e}")
            
            return deleted
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            # Clear memory cache
            self.memory_cache.clear()
            self.current_memory_size = 0
            
            # Clear Redis cache
            if self.redis_client and self.cache_type in [CacheType.REDIS, CacheType.HYBRID]:
                try:
                    await self.redis_client.flushdb()
                except Exception as e:
                    logger.error(f"Redis clear error: {e}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        memory_entries = len(self.memory_cache)
        total_access_count = sum(entry.access_count for entry in self.memory_cache.values())
        
        return {
            "cache_type": self.cache_type.value,
            "memory_cache": {
                "entries": memory_entries,
                "size_bytes": self.current_memory_size,
                "max_size_bytes": self.max_memory_size,
                "utilization_percent": (self.current_memory_size / self.max_memory_size) * 100,
                "total_access_count": total_access_count
            },
            "redis_available": self.redis_client is not None
        }
    
    # Specialized caching methods for AI operations
    
    async def cache_ai_response(
        self,
        agent_type: str,
        message: str,
        response: Dict[str, Any],
        conversation_id: str = "default",
        ttl: int = 3600
    ) -> str:
        """Cache an AI agent response."""
        cache_data = {
            "agent_type": agent_type,
            "message": message,
            "conversation_id": conversation_id
        }
        
        cache_key = self._generate_cache_key("ai_response", cache_data)
        
        await self.set(cache_key, response, ttl=ttl)
        
        return cache_key
    
    async def get_cached_ai_response(
        self,
        agent_type: str,
        message: str,
        conversation_id: str = "default"
    ) -> Optional[Dict[str, Any]]:
        """Get a cached AI agent response."""
        cache_data = {
            "agent_type": agent_type,
            "message": message,
            "conversation_id": conversation_id
        }
        
        cache_key = self._generate_cache_key("ai_response", cache_data)
        
        return await self.get(cache_key)
    
    async def cache_task_analysis(
        self,
        task_text: str,
        analysis_result: Dict[str, Any],
        ttl: int = 7200  # 2 hours
    ) -> str:
        """Cache a task complexity analysis result."""
        cache_data = {"task_text": task_text}
        cache_key = self._generate_cache_key("task_analysis", cache_data)
        
        await self.set(cache_key, analysis_result, ttl=ttl)
        
        return cache_key
    
    async def get_cached_task_analysis(self, task_text: str) -> Optional[Dict[str, Any]]:
        """Get a cached task complexity analysis."""
        cache_data = {"task_text": task_text}
        cache_key = self._generate_cache_key("task_analysis", cache_data)
        
        return await self.get(cache_key)
    
    async def cache_rag_search(
        self,
        query: str,
        search_results: Dict[str, Any],
        knowledge_base: str = "default",
        ttl: int = 1800  # 30 minutes
    ) -> str:
        """Cache RAG search results."""
        cache_data = {
            "query": query,
            "knowledge_base": knowledge_base
        }
        cache_key = self._generate_cache_key("rag_search", cache_data)
        
        await self.set(cache_key, search_results, ttl=ttl)
        
        return cache_key
    
    async def get_cached_rag_search(
        self,
        query: str,
        knowledge_base: str = "default"
    ) -> Optional[Dict[str, Any]]:
        """Get cached RAG search results."""
        cache_data = {
            "query": query,
            "knowledge_base": knowledge_base
        }
        cache_key = self._generate_cache_key("rag_search", cache_data)
        
        return await self.get(cache_key)


# Global caching service instance
caching_service = CachingService(CacheType.HYBRID)
