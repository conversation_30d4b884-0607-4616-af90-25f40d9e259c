"""
Database service layer for AI Brain Foundation.
Provides unified database access with connection pooling and vector capabilities.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID, uuid4
from datetime import datetime

import asyncpg
from asyncpg import Pool, Connection
from pydantic import BaseModel

from ..config import get_settings

logger = logging.getLogger(__name__)


class DatabaseConfig(BaseModel):
    """Database configuration model."""
    host: str
    port: int = 5432
    database: str
    username: str
    password: str
    min_connections: int = 5
    max_connections: int = 20
    command_timeout: int = 60


class VectorSearchResult(BaseModel):
    """Vector search result model."""
    id: UUID
    content: str
    similarity: float
    metadata: Dict[str, Any] = {}


class ConversationData(BaseModel):
    """Conversation data model."""
    id: UUID
    user_id: Optional[UUID]
    title: Optional[str]
    created_at: datetime
    updated_at: datetime
    message_count: int
    metadata: Dict[str, Any] = {}


class MessageData(BaseModel):
    """Message data model."""
    id: UUID
    conversation_id: UUID
    role: str
    content: str
    embedding: Optional[List[float]] = None
    provider_used: Optional[str] = None
    model_used: Optional[str] = None
    tokens_used: Optional[int] = None
    created_at: datetime


class DocumentData(BaseModel):
    """Document data model."""
    id: UUID
    title: str
    content: str
    embedding: Optional[List[float]] = None
    metadata: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime


class DatabaseService:
    """Unified database service with connection pooling and vector capabilities."""
    
    def __init__(self):
        self.pool: Optional[Pool] = None
        self.settings = get_settings()
        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize database connection pool."""
        try:
            if self._initialized:
                return True
            
            # Parse database URL or use Supabase configuration
            db_config = self._get_database_config()
            
            self.pool = await asyncpg.create_pool(
                host=db_config.host,
                port=db_config.port,
                database=db_config.database,
                user=db_config.username,
                password=db_config.password,
                min_size=db_config.min_connections,
                max_size=db_config.max_connections,
                command_timeout=db_config.command_timeout
            )
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            self._initialized = True
            logger.info("Database service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize database service: {e}")
            return False
    
    def _get_database_config(self) -> DatabaseConfig:
        """Get database configuration from settings."""
        if self.settings.database_url:
            # Parse DATABASE_URL
            import urllib.parse as urlparse
            url = urlparse.urlparse(self.settings.database_url)
            return DatabaseConfig(
                host=url.hostname,
                port=url.port or 5432,
                database=url.path[1:],  # Remove leading slash
                username=url.username,
                password=url.password
            )
        elif self.settings.supabase_url:
            # Use Supabase configuration
            project_id = self.settings.supabase_url.split("//")[1].split(".")[0]
            return DatabaseConfig(
                host=f"db.{project_id}.supabase.co",
                port=5432,
                database="postgres",
                username="postgres",
                password=self.settings.supabase_service_role_key or "your-password"
            )
        else:
            raise ValueError("No database configuration found")
    
    async def close(self):
        """Close database connection pool."""
        if self.pool:
            await self.pool.close()
            self._initialized = False
            logger.info("Database service closed")
    
    async def health_check(self) -> bool:
        """Check database health."""
        try:
            if not self.pool:
                return False
            
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    # Conversation management
    async def create_conversation(
        self, 
        user_id: Optional[UUID] = None, 
        title: Optional[str] = None,
        metadata: Dict[str, Any] = None
    ) -> ConversationData:
        """Create a new conversation."""
        conversation_id = uuid4()
        metadata = metadata or {}
        
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                INSERT INTO conversations (id, user_id, title, metadata)
                VALUES ($1, $2, $3, $4)
                RETURNING id, user_id, title, created_at, updated_at, message_count, metadata
            """, conversation_id, user_id, title, metadata)
            
            return ConversationData(**dict(row))
    
    async def get_conversation(self, conversation_id: UUID) -> Optional[ConversationData]:
        """Get conversation by ID."""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT id, user_id, title, created_at, updated_at, message_count, metadata
                FROM conversations WHERE id = $1
            """, conversation_id)
            
            return ConversationData(**dict(row)) if row else None
    
    async def list_conversations(
        self, 
        user_id: Optional[UUID] = None, 
        limit: int = 50, 
        offset: int = 0
    ) -> List[ConversationData]:
        """List conversations with optional user filter."""
        query = """
            SELECT id, user_id, title, created_at, updated_at, message_count, metadata
            FROM conversations
        """
        params = []
        
        if user_id:
            query += " WHERE user_id = $1"
            params.append(user_id)
        
        query += " ORDER BY updated_at DESC LIMIT $%d OFFSET $%d" % (
            len(params) + 1, len(params) + 2
        )
        params.extend([limit, offset])
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            return [ConversationData(**dict(row)) for row in rows]


# Global database service instance
database_service = DatabaseService()


async def get_database_service() -> DatabaseService:
    """Get initialized database service."""
    if not database_service._initialized:
        await database_service.initialize()
    return database_service


    # Message management
    async def create_message(
        self,
        conversation_id: UUID,
        role: str,
        content: str,
        embedding: Optional[List[float]] = None,
        provider_used: Optional[str] = None,
        model_used: Optional[str] = None,
        tokens_used: Optional[int] = None
    ) -> MessageData:
        """Create a new message."""
        message_id = uuid4()

        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                INSERT INTO messages (
                    id, conversation_id, role, content, embedding,
                    provider_used, model_used, tokens_used
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING id, conversation_id, role, content, embedding,
                         provider_used, model_used, tokens_used, created_at
            """, message_id, conversation_id, role, content, embedding,
                provider_used, model_used, tokens_used)

            return MessageData(**dict(row))

    async def get_conversation_messages(
        self,
        conversation_id: UUID,
        limit: int = 100,
        offset: int = 0
    ) -> List[MessageData]:
        """Get messages for a conversation."""
        async with self.pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT id, conversation_id, role, content, embedding,
                       provider_used, model_used, tokens_used, created_at
                FROM messages
                WHERE conversation_id = $1
                ORDER BY created_at ASC
                LIMIT $2 OFFSET $3
            """, conversation_id, limit, offset)

            return [MessageData(**dict(row)) for row in rows]

    # Vector search operations
    async def search_similar_documents(
        self,
        query_embedding: List[float],
        match_threshold: float = 0.8,
        match_count: int = 10
    ) -> List[VectorSearchResult]:
        """Search for similar documents using vector similarity."""
        async with self.pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT id, title as content,
                       1 - (embedding <=> $1) as similarity,
                       metadata
                FROM documents
                WHERE 1 - (embedding <=> $1) > $2
                ORDER BY embedding <=> $1
                LIMIT $3
            """, query_embedding, match_threshold, match_count)

            return [VectorSearchResult(**dict(row)) for row in rows]

    async def search_similar_messages(
        self,
        query_embedding: List[float],
        conversation_id: Optional[UUID] = None,
        match_threshold: float = 0.8,
        match_count: int = 10
    ) -> List[VectorSearchResult]:
        """Search for similar messages using vector similarity."""
        query = """
            SELECT id, content,
                   1 - (embedding <=> $1) as similarity,
                   jsonb_build_object(
                       'conversation_id', conversation_id,
                       'role', role,
                       'created_at', created_at
                   ) as metadata
            FROM messages
            WHERE embedding IS NOT NULL
              AND 1 - (embedding <=> $1) > $2
        """
        params = [query_embedding, match_threshold]

        if conversation_id:
            query += " AND conversation_id = $3"
            params.append(conversation_id)
            query += " ORDER BY embedding <=> $1 LIMIT $4"
            params.append(match_count)
        else:
            query += " ORDER BY embedding <=> $1 LIMIT $3"
            params.append(match_count)

        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            return [VectorSearchResult(**dict(row)) for row in rows]

    # Document management
    async def create_document(
        self,
        title: str,
        content: str,
        embedding: Optional[List[float]] = None,
        metadata: Dict[str, Any] = None
    ) -> DocumentData:
        """Create a new document."""
        document_id = uuid4()
        metadata = metadata or {}

        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                INSERT INTO documents (id, title, content, embedding, metadata)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id, title, content, embedding, metadata, created_at, updated_at
            """, document_id, title, content, embedding, metadata)

            return DocumentData(**dict(row))

    async def get_document(self, document_id: UUID) -> Optional[DocumentData]:
        """Get document by ID."""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT id, title, content, embedding, metadata, created_at, updated_at
                FROM documents WHERE id = $1
            """, document_id)

            return DocumentData(**dict(row)) if row else None

    # Provider logging
    async def log_provider_usage(
        self,
        provider: str,
        model: str,
        request_tokens: Optional[int] = None,
        response_tokens: Optional[int] = None,
        response_time_ms: Optional[int] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ):
        """Log provider usage for monitoring."""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO provider_logs (
                    provider, model, request_tokens, response_tokens,
                    response_time_ms, success, error_message
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """, provider, model, request_tokens, response_tokens,
                response_time_ms, success, error_message)

    # System metrics
    async def record_metric(
        self,
        metric_name: str,
        metric_value: float,
        metric_type: str = "gauge",
        labels: Dict[str, Any] = None
    ):
        """Record a system metric."""
        labels = labels or {}

        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO system_metrics (metric_name, metric_value, metric_type, labels)
                VALUES ($1, $2, $3, $4)
            """, metric_name, metric_value, metric_type, labels)


async def close_database_service():
    """Close database service."""
    await database_service.close()
