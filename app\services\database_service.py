"""
Database service using Supabase PostgreSQL.
"""

import logging
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from pydantic import BaseModel

from .supabase_client import get_supabase_service, handle_supabase_error, SupabaseDBError

logger = logging.getLogger(__name__)


class DatabaseQuery(BaseModel):
    """Database query model."""
    table: str
    select: Optional[str] = "*"
    filters: Optional[Dict[str, Any]] = None
    order_by: Optional[str] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


class DatabaseInsert(BaseModel):
    """Database insert model."""
    table: str
    data: Union[Dict[str, Any], List[Dict[str, Any]]]
    upsert: bool = False


class DatabaseUpdate(BaseModel):
    """Database update model."""
    table: str
    data: Dict[str, Any]
    filters: Dict[str, Any]


class DatabaseDelete(BaseModel):
    """Database delete model."""
    table: str
    filters: Dict[str, Any]


class DatabaseService:
    """Database service using Supabase PostgreSQL."""
    
    def __init__(self):
        """Initialize the database service."""
        self.supabase = get_supabase_service()
    
    @handle_supabase_error
    async def query(self, query: DatabaseQuery) -> List[Dict[str, Any]]:
        """Execute a SELECT query."""
        if not self.supabase.is_available:
            raise SupabaseDBError("Supabase is not available")
        
        # Start building the query
        db_query = self.supabase.client.table(query.table).select(query.select)
        
        # Apply filters
        if query.filters:
            for key, value in query.filters.items():
                if isinstance(value, dict):
                    # Handle complex filters like {"gte": 10}, {"like": "%test%"}
                    for op, val in value.items():
                        if op == "eq":
                            db_query = db_query.eq(key, val)
                        elif op == "neq":
                            db_query = db_query.neq(key, val)
                        elif op == "gt":
                            db_query = db_query.gt(key, val)
                        elif op == "gte":
                            db_query = db_query.gte(key, val)
                        elif op == "lt":
                            db_query = db_query.lt(key, val)
                        elif op == "lte":
                            db_query = db_query.lte(key, val)
                        elif op == "like":
                            db_query = db_query.like(key, val)
                        elif op == "ilike":
                            db_query = db_query.ilike(key, val)
                        elif op == "in":
                            db_query = db_query.in_(key, val)
                        elif op == "is":
                            if val is None:
                                db_query = db_query.is_(key, "null")
                            else:
                                db_query = db_query.is_(key, val)
                else:
                    # Simple equality filter
                    db_query = db_query.eq(key, value)
        
        # Apply ordering
        if query.order_by:
            if query.order_by.startswith("-"):
                # Descending order
                db_query = db_query.order(query.order_by[1:], desc=True)
            else:
                # Ascending order
                db_query = db_query.order(query.order_by)
        
        # Apply limit and offset
        if query.limit:
            db_query = db_query.limit(query.limit)
        
        if query.offset:
            db_query = db_query.offset(query.offset)
        
        # Execute query
        response = db_query.execute()
        return response.data
    
    @handle_supabase_error
    async def insert(self, insert: DatabaseInsert) -> List[Dict[str, Any]]:
        """Insert data into a table."""
        if not self.supabase.is_available:
            raise SupabaseDBError("Supabase is not available")
        
        db_query = self.supabase.client.table(insert.table)
        
        if insert.upsert:
            response = db_query.upsert(insert.data).execute()
        else:
            response = db_query.insert(insert.data).execute()
        
        return response.data
    
    @handle_supabase_error
    async def update(self, update: DatabaseUpdate) -> List[Dict[str, Any]]:
        """Update data in a table."""
        if not self.supabase.is_available:
            raise SupabaseDBError("Supabase is not available")
        
        db_query = self.supabase.client.table(update.table).update(update.data)
        
        # Apply filters
        for key, value in update.filters.items():
            db_query = db_query.eq(key, value)
        
        response = db_query.execute()
        return response.data
    
    @handle_supabase_error
    async def delete(self, delete: DatabaseDelete) -> List[Dict[str, Any]]:
        """Delete data from a table."""
        if not self.supabase.is_available:
            raise SupabaseDBError("Supabase is not available")
        
        db_query = self.supabase.client.table(delete.table).delete()
        
        # Apply filters
        for key, value in delete.filters.items():
            db_query = db_query.eq(key, value)
        
        response = db_query.execute()
        return response.data
    
    @handle_supabase_error
    async def execute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute raw SQL query (use with caution)."""
        if not self.supabase.is_available:
            raise SupabaseDBError("Supabase is not available")
        
        # Note: Direct SQL execution might require service role key
        # This is a placeholder for advanced SQL operations
        logger.warning("Raw SQL execution is not directly supported in this implementation")
        raise SupabaseDBError("Raw SQL execution not implemented")
    
    @handle_supabase_error
    async def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get table schema information."""
        if not self.supabase.is_available:
            raise SupabaseDBError("Supabase is not available")
        
        # Query information_schema to get table structure
        query = DatabaseQuery(
            table="information_schema.columns",
            select="column_name,data_type,is_nullable,column_default",
            filters={"table_name": table_name}
        )
        
        columns = await self.query(query)
        return {
            "table_name": table_name,
            "columns": columns
        }
    
    @handle_supabase_error
    async def count_records(self, table: str, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count records in a table."""
        if not self.supabase.is_available:
            raise SupabaseDBError("Supabase is not available")
        
        db_query = self.supabase.client.table(table).select("*", count="exact")
        
        # Apply filters
        if filters:
            for key, value in filters.items():
                db_query = db_query.eq(key, value)
        
        response = db_query.execute()
        return response.count
    
    async def create_conversation_table(self) -> bool:
        """Create conversations table for AI chat history."""
        try:
            # This would typically be done via Supabase dashboard or migrations
            # For now, we'll just log the SQL that should be executed
            sql = """
            CREATE TABLE IF NOT EXISTS conversations (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
                title TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                metadata JSONB DEFAULT '{}'::jsonb
            );
            
            CREATE TABLE IF NOT EXISTS messages (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
                role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
                content TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                metadata JSONB DEFAULT '{}'::jsonb
            );
            
            -- Enable Row Level Security
            ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
            ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
            
            -- Create policies
            CREATE POLICY "Users can view their own conversations" ON conversations
                FOR SELECT USING (auth.uid() = user_id);
            
            CREATE POLICY "Users can insert their own conversations" ON conversations
                FOR INSERT WITH CHECK (auth.uid() = user_id);
            
            CREATE POLICY "Users can update their own conversations" ON conversations
                FOR UPDATE USING (auth.uid() = user_id);
            
            CREATE POLICY "Users can delete their own conversations" ON conversations
                FOR DELETE USING (auth.uid() = user_id);
            
            CREATE POLICY "Users can view messages in their conversations" ON messages
                FOR SELECT USING (
                    conversation_id IN (
                        SELECT id FROM conversations WHERE user_id = auth.uid()
                    )
                );
            
            CREATE POLICY "Users can insert messages in their conversations" ON messages
                FOR INSERT WITH CHECK (
                    conversation_id IN (
                        SELECT id FROM conversations WHERE user_id = auth.uid()
                    )
                );
            """
            
            logger.info("To create the required tables, execute this SQL in your Supabase dashboard:")
            logger.info(sql)
            return True
            
        except Exception as e:
            logger.error(f"Error creating conversation tables: {e}")
            return False


# Global database service instance
_database_service: Optional[DatabaseService] = None


def get_database_service() -> DatabaseService:
    """Get the global database service instance."""
    global _database_service
    if _database_service is None:
        _database_service = DatabaseService()
    return _database_service
