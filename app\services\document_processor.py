"""
Document processing service for various file formats (PDF, DOCX, TXT, etc.).
"""

import logging
import time
import mimetypes
from typing import Dict, Any, Optional, List, BinaryIO
from pathlib import Path
from dataclasses import dataclass

import aiofiles
from fastapi import UploadFile

from .observability import observability_service, SpanMetadata

logger = logging.getLogger(__name__)


@dataclass
class ProcessedDocument:
    """Processed document with extracted content and metadata."""
    content: str
    metadata: Dict[str, Any]
    file_type: str
    processing_time_ms: float
    word_count: int
    char_count: int


class DocumentProcessor:
    """Service for processing various document formats."""
    
    def __init__(self):
        self.supported_formats = {
            'text/plain': self._process_text,
            'application/pdf': self._process_pdf,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._process_docx,
            'application/msword': self._process_doc,
            'text/markdown': self._process_markdown,
            'text/html': self._process_html,
            'application/json': self._process_json,
            'text/csv': self._process_csv
        }
    
    async def process_file(self, file: UploadFile) -> ProcessedDocument:
        """Process an uploaded file and extract text content."""
        start_time = time.time()
        
        metadata_span = SpanMetadata(
            operation_type="document_processing",
            file_name=file.filename
        )
        
        async with observability_service.trace_operation("process_document", metadata_span) as span:
            try:
                # Read file content
                content_bytes = await file.read()
                
                # Determine file type
                file_type = file.content_type or mimetypes.guess_type(file.filename)[0] or 'application/octet-stream'
                
                # Get processor for file type
                processor = self.supported_formats.get(file_type)
                if not processor:
                    # Try to process as text if no specific processor
                    processor = self._process_text
                    logger.warning(f"No specific processor for {file_type}, trying text processing")
                
                # Process the document
                content = await processor(content_bytes, file.filename)
                
                # Calculate metrics
                word_count = len(content.split()) if content else 0
                char_count = len(content) if content else 0
                processing_time_ms = (time.time() - start_time) * 1000
                
                # Create metadata
                metadata = {
                    'filename': file.filename,
                    'file_type': file_type,
                    'file_size': len(content_bytes),
                    'word_count': word_count,
                    'char_count': char_count,
                    'processing_time_ms': processing_time_ms,
                    'processed_at': time.time()
                }
                
                result = ProcessedDocument(
                    content=content,
                    metadata=metadata,
                    file_type=file_type,
                    processing_time_ms=processing_time_ms,
                    word_count=word_count,
                    char_count=char_count
                )
                
                # Log processing operation
                observability_service.log_task_analysis(
                    task_text=f"Document processing: {file.filename}",
                    complexity_result={
                        "file_type": file_type,
                        "file_size": len(content_bytes),
                        "word_count": word_count,
                        "success": True
                    },
                    duration_ms=processing_time_ms,
                    features_count=word_count
                )
                
                return result
                
            except Exception as e:
                processing_time_ms = (time.time() - start_time) * 1000
                logger.error(f"Error processing file {file.filename}: {e}")
                
                observability_service.log_error(
                    operation_name="process_document",
                    error=e,
                    context={
                        "filename": file.filename,
                        "file_type": file.content_type,
                        "duration_ms": processing_time_ms
                    }
                )
                
                # Return empty document on error
                return ProcessedDocument(
                    content="",
                    metadata={
                        'filename': file.filename,
                        'error': str(e),
                        'processing_time_ms': processing_time_ms
                    },
                    file_type=file.content_type or 'unknown',
                    processing_time_ms=processing_time_ms,
                    word_count=0,
                    char_count=0
                )
    
    async def _process_text(self, content_bytes: bytes, filename: str) -> str:
        """Process plain text files."""
        try:
            # Try different encodings
            for encoding in ['utf-8', 'utf-16', 'latin-1', 'cp1252']:
                try:
                    return content_bytes.decode(encoding)
                except UnicodeDecodeError:
                    continue
            
            # If all encodings fail, use utf-8 with error handling
            return content_bytes.decode('utf-8', errors='replace')
            
        except Exception as e:
            logger.error(f"Error processing text file {filename}: {e}")
            return ""
    
    async def _process_pdf(self, content_bytes: bytes, filename: str) -> str:
        """Process PDF files."""
        try:
            # Try to import PyPDF2 or pypdf
            try:
                from pypdf import PdfReader
                import io
                
                pdf_file = io.BytesIO(content_bytes)
                reader = PdfReader(pdf_file)
                
                text_content = []
                for page in reader.pages:
                    text_content.append(page.extract_text())
                
                return '\n'.join(text_content)
                
            except ImportError:
                logger.warning("pypdf not available, cannot process PDF files")
                return f"PDF processing not available for {filename}"
                
        except Exception as e:
            logger.error(f"Error processing PDF file {filename}: {e}")
            return f"Error processing PDF: {str(e)}"
    
    async def _process_docx(self, content_bytes: bytes, filename: str) -> str:
        """Process DOCX files."""
        try:
            try:
                from docx import Document
                import io
                
                docx_file = io.BytesIO(content_bytes)
                doc = Document(docx_file)
                
                text_content = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_content.append(paragraph.text)
                
                return '\n'.join(text_content)
                
            except ImportError:
                logger.warning("python-docx not available, cannot process DOCX files")
                return f"DOCX processing not available for {filename}"
                
        except Exception as e:
            logger.error(f"Error processing DOCX file {filename}: {e}")
            return f"Error processing DOCX: {str(e)}"
    
    async def _process_doc(self, content_bytes: bytes, filename: str) -> str:
        """Process legacy DOC files."""
        # Legacy DOC files require more complex processing
        # For now, return a placeholder
        logger.warning(f"Legacy DOC format not fully supported for {filename}")
        return f"Legacy DOC format processing not implemented for {filename}"
    
    async def _process_markdown(self, content_bytes: bytes, filename: str) -> str:
        """Process Markdown files."""
        try:
            content = await self._process_text(content_bytes, filename)
            
            # Basic markdown processing - remove common markdown syntax
            import re
            
            # Remove headers
            content = re.sub(r'^#+\s*', '', content, flags=re.MULTILINE)
            
            # Remove bold/italic
            content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)
            content = re.sub(r'\*(.*?)\*', r'\1', content)
            
            # Remove links
            content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)
            
            # Remove code blocks
            content = re.sub(r'```[\s\S]*?```', '', content)
            content = re.sub(r'`([^`]+)`', r'\1', content)
            
            return content
            
        except Exception as e:
            logger.error(f"Error processing Markdown file {filename}: {e}")
            return await self._process_text(content_bytes, filename)
    
    async def _process_html(self, content_bytes: bytes, filename: str) -> str:
        """Process HTML files."""
        try:
            content = await self._process_text(content_bytes, filename)
            
            # Basic HTML tag removal
            import re
            
            # Remove HTML tags
            content = re.sub(r'<[^>]+>', '', content)
            
            # Decode HTML entities
            import html
            content = html.unescape(content)
            
            # Clean up whitespace
            content = re.sub(r'\s+', ' ', content).strip()
            
            return content
            
        except Exception as e:
            logger.error(f"Error processing HTML file {filename}: {e}")
            return await self._process_text(content_bytes, filename)
    
    async def _process_json(self, content_bytes: bytes, filename: str) -> str:
        """Process JSON files."""
        try:
            import json
            
            content = await self._process_text(content_bytes, filename)
            data = json.loads(content)
            
            # Extract text content from JSON
            def extract_text(obj, texts=None):
                if texts is None:
                    texts = []
                
                if isinstance(obj, dict):
                    for value in obj.values():
                        extract_text(value, texts)
                elif isinstance(obj, list):
                    for item in obj:
                        extract_text(item, texts)
                elif isinstance(obj, str):
                    texts.append(obj)
                
                return texts
            
            text_values = extract_text(data)
            return '\n'.join(text_values)
            
        except Exception as e:
            logger.error(f"Error processing JSON file {filename}: {e}")
            return await self._process_text(content_bytes, filename)
    
    async def _process_csv(self, content_bytes: bytes, filename: str) -> str:
        """Process CSV files."""
        try:
            import csv
            import io
            
            content = await self._process_text(content_bytes, filename)
            csv_file = io.StringIO(content)
            reader = csv.reader(csv_file)
            
            text_content = []
            for row in reader:
                text_content.append(' '.join(str(cell) for cell in row))
            
            return '\n'.join(text_content)
            
        except Exception as e:
            logger.error(f"Error processing CSV file {filename}: {e}")
            return await self._process_text(content_bytes, filename)
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats."""
        return list(self.supported_formats.keys())


# Global document processor instance
document_processor = DocumentProcessor()
