"""
Function calling and tool use capabilities for AI agents.
"""

import json
import logging
import time
import asyncio
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum

from .observability import observability_service, SpanMetadata

logger = logging.getLogger(__name__)


class ToolType(Enum):
    """Types of tools available for function calling."""
    BUILTIN = "builtin"
    EXTERNAL_API = "external_api"
    AGENT_CALL = "agent_call"
    DATABASE = "database"
    FILE_SYSTEM = "file_system"
    CALCULATION = "calculation"


@dataclass
class ToolDefinition:
    """Definition of a tool that can be called by AI models."""
    name: str
    description: str
    tool_type: ToolType
    parameters: Dict[str, Any]  # JSON Schema for parameters
    handler: Optional[Callable] = None
    required_permissions: List[str] = None
    timeout_seconds: int = 30
    cost_estimate: float = 0.0  # Estimated cost in credits/tokens


@dataclass
class ToolCall:
    """Represents a function/tool call from an AI model."""
    id: str
    name: str
    arguments: Dict[str, Any]
    timestamp: float
    status: str = "pending"  # pending, running, completed, failed
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time_ms: float = 0


class FunctionCallingService:
    """Service for managing function calling and tool use capabilities."""
    
    def __init__(self):
        self.tools: Dict[str, ToolDefinition] = {}
        self.tool_calls: Dict[str, ToolCall] = {}
        
        # Register built-in tools
        self._register_builtin_tools()
    
    def _register_builtin_tools(self):
        """Register built-in tools."""
        
        # Calculator tool
        self.register_tool(ToolDefinition(
            name="calculator",
            description="Perform mathematical calculations",
            tool_type=ToolType.CALCULATION,
            parameters={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression to evaluate (e.g., '2 + 2', 'sqrt(16)', 'sin(pi/2)')"
                    }
                },
                "required": ["expression"]
            },
            handler=self._handle_calculator
        ))
        
        # Current time tool
        self.register_tool(ToolDefinition(
            name="get_current_time",
            description="Get the current date and time",
            tool_type=ToolType.BUILTIN,
            parameters={
                "type": "object",
                "properties": {
                    "format": {
                        "type": "string",
                        "description": "Time format: 'iso', 'timestamp', 'human' (default: 'iso')",
                        "default": "iso"
                    },
                    "timezone": {
                        "type": "string",
                        "description": "Timezone (default: 'UTC')",
                        "default": "UTC"
                    }
                }
            },
            handler=self._handle_get_time
        ))
        
        # Text processing tool
        self.register_tool(ToolDefinition(
            name="text_processor",
            description="Process text with various operations",
            tool_type=ToolType.BUILTIN,
            parameters={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to process"
                    },
                    "operation": {
                        "type": "string",
                        "enum": ["word_count", "char_count", "uppercase", "lowercase", "reverse", "extract_emails"],
                        "description": "Operation to perform on the text"
                    }
                },
                "required": ["text", "operation"]
            },
            handler=self._handle_text_processor
        ))
        
        # HTTP request tool
        self.register_tool(ToolDefinition(
            name="http_request",
            description="Make HTTP requests to external APIs",
            tool_type=ToolType.EXTERNAL_API,
            parameters={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to make the request to"
                    },
                    "method": {
                        "type": "string",
                        "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"],
                        "default": "GET",
                        "description": "HTTP method"
                    },
                    "headers": {
                        "type": "object",
                        "description": "Request headers"
                    },
                    "data": {
                        "type": "object",
                        "description": "Request body data"
                    },
                    "timeout": {
                        "type": "number",
                        "default": 30,
                        "description": "Request timeout in seconds"
                    }
                },
                "required": ["url"]
            },
            handler=self._handle_http_request,
            required_permissions=["external_api"],
            timeout_seconds=60
        ))
        
        # Agent call tool
        self.register_tool(ToolDefinition(
            name="call_agent",
            description="Call another AI agent for specialized tasks",
            tool_type=ToolType.AGENT_CALL,
            parameters={
                "type": "object",
                "properties": {
                    "agent_type": {
                        "type": "string",
                        "enum": ["chatbot", "rag", "image", "document", "marketing", "automation"],
                        "description": "Type of agent to call"
                    },
                    "method": {
                        "type": "string",
                        "default": "chat",
                        "description": "Agent method to call"
                    },
                    "parameters": {
                        "type": "object",
                        "description": "Parameters to pass to the agent"
                    }
                },
                "required": ["agent_type", "parameters"]
            },
            handler=self._handle_agent_call,
            cost_estimate=0.1
        ))
    
    def register_tool(self, tool: ToolDefinition):
        """Register a new tool."""
        self.tools[tool.name] = tool
        logger.info(f"Registered tool: {tool.name} ({tool.tool_type.value})")
    
    def get_tools_schema(self, tool_names: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get OpenAI-compatible tools schema for function calling."""
        tools_to_include = tool_names or list(self.tools.keys())
        
        schema = []
        for tool_name in tools_to_include:
            if tool_name in self.tools:
                tool = self.tools[tool_name]
                schema.append({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
        
        return schema
    
    async def execute_tool_call(
        self,
        tool_call: ToolCall,
        permissions: Optional[List[str]] = None
    ) -> ToolCall:
        """Execute a tool call."""
        start_time = time.time()
        
        metadata_span = SpanMetadata(
            operation_type="tool_execution",
            tool_name=tool_call.name,
            tool_call_id=tool_call.id
        )
        
        async with observability_service.trace_operation("execute_tool_call", metadata_span) as span:
            try:
                tool_call.status = "running"
                
                # Check if tool exists
                if tool_call.name not in self.tools:
                    raise ValueError(f"Tool not found: {tool_call.name}")
                
                tool = self.tools[tool_call.name]
                
                # Check permissions
                if tool.required_permissions:
                    user_permissions = permissions or []
                    missing_permissions = set(tool.required_permissions) - set(user_permissions)
                    if missing_permissions:
                        raise PermissionError(f"Missing permissions: {missing_permissions}")
                
                # Execute tool with timeout
                if tool.handler:
                    result = await asyncio.wait_for(
                        tool.handler(tool_call.arguments),
                        timeout=tool.timeout_seconds
                    )
                else:
                    raise ValueError(f"No handler registered for tool: {tool_call.name}")
                
                tool_call.result = result
                tool_call.status = "completed"
                tool_call.execution_time_ms = (time.time() - start_time) * 1000
                
                logger.info(f"Tool call completed: {tool_call.name} ({tool_call.id})")
                
                # Log tool execution
                observability_service.log_agent_execution(
                    agent_type="function_calling",
                    request_data={
                        "tool_name": tool_call.name,
                        "arguments": tool_call.arguments
                    },
                    response_data={"result": result},
                    duration_ms=tool_call.execution_time_ms,
                    model_name=f"tool_{tool_call.name}",
                    provider="internal"
                )
                
                return tool_call
                
            except asyncio.TimeoutError:
                tool_call.status = "failed"
                tool_call.error = f"Tool call timed out after {tool.timeout_seconds} seconds"
                tool_call.execution_time_ms = (time.time() - start_time) * 1000
                
                logger.error(f"Tool call timed out: {tool_call.name} ({tool_call.id})")
                
                observability_service.log_error(
                    operation_name="execute_tool_call",
                    error=TimeoutError(tool_call.error),
                    context={
                        "tool_name": tool_call.name,
                        "tool_call_id": tool_call.id,
                        "timeout_seconds": tool.timeout_seconds
                    }
                )
                
                return tool_call
                
            except Exception as e:
                tool_call.status = "failed"
                tool_call.error = str(e)
                tool_call.execution_time_ms = (time.time() - start_time) * 1000
                
                logger.error(f"Tool call failed: {tool_call.name} ({tool_call.id}) - {e}")
                
                observability_service.log_error(
                    operation_name="execute_tool_call",
                    error=e,
                    context={
                        "tool_name": tool_call.name,
                        "tool_call_id": tool_call.id,
                        "arguments": tool_call.arguments
                    }
                )
                
                return tool_call
    
    async def execute_multiple_tool_calls(
        self,
        tool_calls: List[ToolCall],
        permissions: Optional[List[str]] = None,
        parallel: bool = True
    ) -> List[ToolCall]:
        """Execute multiple tool calls, optionally in parallel."""
        if parallel:
            # Execute all tool calls in parallel
            tasks = [
                self.execute_tool_call(tool_call, permissions)
                for tool_call in tool_calls
            ]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle any exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    tool_calls[i].status = "failed"
                    tool_calls[i].error = str(result)
                else:
                    tool_calls[i] = result
            
            return tool_calls
        else:
            # Execute tool calls sequentially
            results = []
            for tool_call in tool_calls:
                result = await self.execute_tool_call(tool_call, permissions)
                results.append(result)
            
            return results

    # Built-in tool handlers

    async def _handle_calculator(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle calculator tool calls."""
        try:
            import math
            import operator

            expression = arguments.get("expression", "")
            if not expression:
                raise ValueError("Expression is required")

            # Safe evaluation with limited functions
            allowed_names = {
                "abs": abs, "round": round, "min": min, "max": max,
                "sum": sum, "pow": pow, "sqrt": math.sqrt,
                "sin": math.sin, "cos": math.cos, "tan": math.tan,
                "log": math.log, "log10": math.log10, "exp": math.exp,
                "pi": math.pi, "e": math.e,
                "ceil": math.ceil, "floor": math.floor
            }

            # Simple expression evaluation (be careful with eval in production)
            # This is a simplified version - in production, use a proper math parser
            try:
                # Replace common math functions
                safe_expression = expression.replace("^", "**")

                # Evaluate with restricted globals
                result = eval(safe_expression, {"__builtins__": {}}, allowed_names)

                return {
                    "expression": expression,
                    "result": result,
                    "type": type(result).__name__
                }

            except Exception as e:
                raise ValueError(f"Invalid mathematical expression: {e}")

        except Exception as e:
            raise ValueError(f"Calculator error: {e}")

    async def _handle_get_time(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle get current time tool calls."""
        try:
            from datetime import datetime, timezone
            import pytz

            format_type = arguments.get("format", "iso")
            tz_name = arguments.get("timezone", "UTC")

            # Get timezone
            if tz_name == "UTC":
                tz = timezone.utc
            else:
                try:
                    tz = pytz.timezone(tz_name)
                except:
                    tz = timezone.utc
                    tz_name = "UTC"

            now = datetime.now(tz)

            if format_type == "timestamp":
                formatted_time = now.timestamp()
            elif format_type == "human":
                formatted_time = now.strftime("%Y-%m-%d %H:%M:%S %Z")
            else:  # iso
                formatted_time = now.isoformat()

            return {
                "current_time": formatted_time,
                "format": format_type,
                "timezone": tz_name,
                "timestamp": now.timestamp()
            }

        except Exception as e:
            raise ValueError(f"Time retrieval error: {e}")

    async def _handle_text_processor(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle text processing tool calls."""
        try:
            import re

            text = arguments.get("text", "")
            operation = arguments.get("operation", "")

            if not text:
                raise ValueError("Text is required")

            if not operation:
                raise ValueError("Operation is required")

            result = None

            if operation == "word_count":
                result = len(text.split())
            elif operation == "char_count":
                result = len(text)
            elif operation == "uppercase":
                result = text.upper()
            elif operation == "lowercase":
                result = text.lower()
            elif operation == "reverse":
                result = text[::-1]
            elif operation == "extract_emails":
                email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                result = re.findall(email_pattern, text)
            else:
                raise ValueError(f"Unknown operation: {operation}")

            return {
                "original_text": text,
                "operation": operation,
                "result": result,
                "result_type": type(result).__name__
            }

        except Exception as e:
            raise ValueError(f"Text processing error: {e}")

    async def _handle_http_request(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle HTTP request tool calls."""
        try:
            import httpx

            url = arguments.get("url")
            method = arguments.get("method", "GET").upper()
            headers = arguments.get("headers", {})
            data = arguments.get("data")
            timeout = arguments.get("timeout", 30)

            if not url:
                raise ValueError("URL is required")

            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data if data else None,
                    timeout=timeout
                )

                result = {
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "text": response.text,
                    "url": str(response.url),
                    "method": method
                }

                # Try to parse JSON response
                try:
                    result["json"] = response.json()
                except:
                    pass

                return result

        except ImportError:
            raise ValueError("httpx library not available for HTTP requests")
        except Exception as e:
            raise ValueError(f"HTTP request error: {e}")

    async def _handle_agent_call(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle agent call tool calls."""
        try:
            agent_type = arguments.get("agent_type")
            method = arguments.get("method", "chat")
            parameters = arguments.get("parameters", {})

            if not agent_type:
                raise ValueError("agent_type is required")

            # This would integrate with the actual agent system
            # For now, return a placeholder
            result = {
                "agent_type": agent_type,
                "method": method,
                "parameters": parameters,
                "result": f"[Agent call to {agent_type}.{method} executed]",
                "status": "completed"
            }

            return result

        except Exception as e:
            raise ValueError(f"Agent call error: {e}")

    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific tool."""
        if tool_name not in self.tools:
            return None

        tool = self.tools[tool_name]
        return {
            "name": tool.name,
            "description": tool.description,
            "type": tool.tool_type.value,
            "parameters": tool.parameters,
            "required_permissions": tool.required_permissions or [],
            "timeout_seconds": tool.timeout_seconds,
            "cost_estimate": tool.cost_estimate
        }

    def list_tools(self) -> List[Dict[str, Any]]:
        """List all available tools."""
        return [
            self.get_tool_info(tool_name)
            for tool_name in self.tools.keys()
        ]


# Global function calling service instance
function_calling_service = FunctionCallingService()
