"""
Multi-modal processing service for images, audio, and video content.
"""

import logging
import time
import base64
import io
from typing import Dict, Any, Optional, List, Union, BinaryIO
from pathlib import Path
from dataclasses import dataclass

import aiofiles
from fastapi import UploadFile
from PIL import Image
import numpy as np

from .observability import observability_service, SpanMetadata

logger = logging.getLogger(__name__)


@dataclass
class ProcessedMedia:
    """Processed media content with extracted information."""
    content_type: str
    metadata: Dict[str, Any]
    processing_time_ms: float
    extracted_text: Optional[str] = None
    image_description: Optional[str] = None
    audio_transcript: Optional[str] = None
    video_summary: Optional[str] = None
    thumbnails: Optional[List[str]] = None  # Base64 encoded thumbnails


class MultiModalProcessor:
    """Service for processing various media formats."""
    
    def __init__(self):
        self.supported_image_formats = {
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
            'image/bmp', 'image/webp', 'image/tiff'
        }
        self.supported_audio_formats = {
            'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 
            'audio/m4a', 'audio/aac', 'audio/flac'
        }
        self.supported_video_formats = {
            'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 
            'video/flv', 'video/webm', 'video/mkv'
        }
    
    async def process_media(self, file: UploadFile) -> ProcessedMedia:
        """Process uploaded media file and extract information."""
        start_time = time.time()
        
        metadata_span = SpanMetadata(
            operation_type="multimodal_processing",
            file_name=file.filename,
            content_type=file.content_type
        )
        
        async with observability_service.trace_operation("process_media", metadata_span) as span:
            try:
                # Read file content
                content_bytes = await file.read()
                content_type = file.content_type or 'application/octet-stream'
                
                # Route to appropriate processor
                if content_type in self.supported_image_formats:
                    result = await self._process_image(content_bytes, file.filename, content_type)
                elif content_type in self.supported_audio_formats:
                    result = await self._process_audio(content_bytes, file.filename, content_type)
                elif content_type in self.supported_video_formats:
                    result = await self._process_video(content_bytes, file.filename, content_type)
                else:
                    raise ValueError(f"Unsupported media type: {content_type}")
                
                processing_time_ms = (time.time() - start_time) * 1000
                result.processing_time_ms = processing_time_ms
                
                # Log processing operation
                observability_service.log_task_analysis(
                    task_text=f"Media processing: {file.filename}",
                    complexity_result={
                        "content_type": content_type,
                        "file_size": len(content_bytes),
                        "success": True
                    },
                    duration_ms=processing_time_ms,
                    features_count=1
                )
                
                return result
                
            except Exception as e:
                processing_time_ms = (time.time() - start_time) * 1000
                logger.error(f"Error processing media file {file.filename}: {e}")
                
                observability_service.log_error(
                    operation_name="process_media",
                    error=e,
                    context={
                        "filename": file.filename,
                        "content_type": file.content_type,
                        "duration_ms": processing_time_ms
                    }
                )
                
                # Return error result
                return ProcessedMedia(
                    content_type=file.content_type or 'unknown',
                    metadata={
                        'filename': file.filename,
                        'error': str(e),
                        'processing_time_ms': processing_time_ms
                    },
                    processing_time_ms=processing_time_ms
                )
    
    async def _process_image(self, content_bytes: bytes, filename: str, content_type: str) -> ProcessedMedia:
        """Process image files and extract metadata."""
        try:
            # Open image with PIL
            image = Image.open(io.BytesIO(content_bytes))
            
            # Extract basic metadata
            metadata = {
                'filename': filename,
                'content_type': content_type,
                'width': image.width,
                'height': image.height,
                'mode': image.mode,
                'format': image.format,
                'file_size': len(content_bytes),
                'aspect_ratio': round(image.width / image.height, 2) if image.height > 0 else 0
            }
            
            # Extract EXIF data if available
            if hasattr(image, '_getexif') and image._getexif():
                exif_data = image._getexif()
                if exif_data:
                    metadata['exif'] = {k: str(v) for k, v in exif_data.items() if isinstance(v, (str, int, float))}
            
            # Generate thumbnail
            thumbnail = image.copy()
            thumbnail.thumbnail((200, 200), Image.Resampling.LANCZOS)
            
            # Convert thumbnail to base64
            thumb_buffer = io.BytesIO()
            thumbnail.save(thumb_buffer, format='JPEG')
            thumb_base64 = base64.b64encode(thumb_buffer.getvalue()).decode('utf-8')
            
            # Basic image analysis
            image_description = await self._analyze_image_content(image, metadata)
            
            return ProcessedMedia(
                content_type=content_type,
                metadata=metadata,
                processing_time_ms=0,  # Will be set by caller
                image_description=image_description,
                thumbnails=[thumb_base64]
            )
            
        except Exception as e:
            logger.error(f"Error processing image {filename}: {e}")
            return ProcessedMedia(
                content_type=content_type,
                metadata={'filename': filename, 'error': str(e)},
                processing_time_ms=0
            )
    
    async def _analyze_image_content(self, image: Image.Image, metadata: Dict[str, Any]) -> str:
        """Analyze image content and generate description."""
        try:
            # Basic image analysis based on properties
            width, height = image.width, image.height
            mode = image.mode
            
            # Determine image characteristics
            characteristics = []
            
            if width > height:
                characteristics.append("landscape orientation")
            elif height > width:
                characteristics.append("portrait orientation")
            else:
                characteristics.append("square format")
            
            if width * height > 2000000:  # > 2MP
                characteristics.append("high resolution")
            elif width * height < 100000:  # < 0.1MP
                characteristics.append("low resolution")
            
            if mode == 'RGBA':
                characteristics.append("with transparency")
            elif mode == 'L':
                characteristics.append("grayscale")
            elif mode == 'RGB':
                characteristics.append("color")
            
            # Basic color analysis
            try:
                # Convert to RGB if needed
                if image.mode != 'RGB':
                    rgb_image = image.convert('RGB')
                else:
                    rgb_image = image
                
                # Sample colors from center region
                center_x, center_y = width // 2, height // 2
                sample_size = min(100, width // 4, height // 4)
                
                crop_box = (
                    center_x - sample_size // 2,
                    center_y - sample_size // 2,
                    center_x + sample_size // 2,
                    center_y + sample_size // 2
                )
                
                center_crop = rgb_image.crop(crop_box)
                colors = center_crop.getcolors(maxcolors=256)
                
                if colors:
                    # Find dominant color
                    dominant_color = max(colors, key=lambda x: x[0])
                    r, g, b = dominant_color[1]
                    
                    # Classify dominant color
                    if r > 200 and g > 200 and b > 200:
                        characteristics.append("bright/light tones")
                    elif r < 50 and g < 50 and b < 50:
                        characteristics.append("dark tones")
                    elif r > g and r > b:
                        characteristics.append("reddish tones")
                    elif g > r and g > b:
                        characteristics.append("greenish tones")
                    elif b > r and b > g:
                        characteristics.append("bluish tones")
                
            except Exception as color_error:
                logger.debug(f"Color analysis failed: {color_error}")
            
            description = f"Image ({width}x{height} pixels) with {', '.join(characteristics)}"
            
            return description
            
        except Exception as e:
            logger.error(f"Error analyzing image content: {e}")
            return f"Image ({metadata.get('width', 'unknown')}x{metadata.get('height', 'unknown')} pixels)"
    
    async def _process_audio(self, content_bytes: bytes, filename: str, content_type: str) -> ProcessedMedia:
        """Process audio files and extract metadata."""
        try:
            metadata = {
                'filename': filename,
                'content_type': content_type,
                'file_size': len(content_bytes),
                'duration_estimate': 'unknown'  # Would need audio library to determine
            }
            
            # Placeholder for audio transcription
            # In production, this would use speech-to-text services
            audio_transcript = f"[Audio transcription not implemented for {filename}]"
            
            return ProcessedMedia(
                content_type=content_type,
                metadata=metadata,
                processing_time_ms=0,
                audio_transcript=audio_transcript
            )
            
        except Exception as e:
            logger.error(f"Error processing audio {filename}: {e}")
            return ProcessedMedia(
                content_type=content_type,
                metadata={'filename': filename, 'error': str(e)},
                processing_time_ms=0
            )
    
    async def _process_video(self, content_bytes: bytes, filename: str, content_type: str) -> ProcessedMedia:
        """Process video files and extract metadata."""
        try:
            metadata = {
                'filename': filename,
                'content_type': content_type,
                'file_size': len(content_bytes),
                'duration_estimate': 'unknown',  # Would need video library to determine
                'resolution_estimate': 'unknown'
            }
            
            # Placeholder for video processing
            # In production, this would use OpenCV or similar
            video_summary = f"[Video processing not fully implemented for {filename}]"
            
            return ProcessedMedia(
                content_type=content_type,
                metadata=metadata,
                processing_time_ms=0,
                video_summary=video_summary
            )
            
        except Exception as e:
            logger.error(f"Error processing video {filename}: {e}")
            return ProcessedMedia(
                content_type=content_type,
                metadata={'filename': filename, 'error': str(e)},
                processing_time_ms=0
            )
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """Get list of supported media formats by category."""
        return {
            "images": list(self.supported_image_formats),
            "audio": list(self.supported_audio_formats),
            "video": list(self.supported_video_formats)
        }


# Global multimodal processor instance
multimodal_processor = MultiModalProcessor()
