"""
Enhanced observability service using Pydantic Logfire SDK.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from contextlib import asynccontextmanager
from functools import wraps

import logfire
from pydantic import BaseModel

from ..config import get_settings


logger = logging.getLogger(__name__)


class SpanMetadata(BaseModel):
    """Metadata for observability spans."""
    
    operation_type: str
    model_name: Optional[str] = None
    provider: Optional[str] = None
    task_complexity: Optional[str] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    cost: Optional[float] = None
    user_id: Optional[str] = None
    conversation_id: Optional[str] = None


class ObservabilityService:
    """
    Service for enhanced observability using Pydantic Logfire.
    
    This service provides structured logging, tracing, and monitoring
    for all AI operations in the backend.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self._initialize_logfire()
        self._operation_counts: Dict[str, int] = {}
        self._total_costs: Dict[str, float] = {}
        
    def _initialize_logfire(self):
        """Initialize Pydantic Logfire SDK."""
        try:
            # Configure Logfire
            logfire.configure(
                service_name="ai-backend",
                service_version="1.0.0",
                environment=getattr(self.settings, 'environment', 'development'),
                # Add your Logfire token here or set via environment variable
                # token=self.settings.logfire_token,
            )
            
            logger.info("Pydantic Logfire initialized successfully")
            
        except Exception as e:
            logger.warning(f"Failed to initialize Logfire: {e}")
            # Continue without Logfire if initialization fails
    
    @asynccontextmanager
    async def trace_operation(
        self,
        operation_name: str,
        metadata: SpanMetadata,
        **extra_attributes
    ):
        """
        Create a traced operation span.
        
        Args:
            operation_name: Name of the operation being traced
            metadata: Structured metadata for the operation
            **extra_attributes: Additional attributes to include in the span
        """
        start_time = time.time()
        
        # Prepare span attributes
        attributes = {
            "operation.name": operation_name,
            "operation.type": metadata.operation_type,
            "timestamp": datetime.now().isoformat(),
        }
        
        # Add metadata attributes
        if metadata.model_name:
            attributes["ai.model.name"] = metadata.model_name
        if metadata.provider:
            attributes["ai.provider"] = metadata.provider
        if metadata.task_complexity:
            attributes["ai.task.complexity"] = metadata.task_complexity
        if metadata.input_tokens:
            attributes["ai.tokens.input"] = metadata.input_tokens
        if metadata.output_tokens:
            attributes["ai.tokens.output"] = metadata.output_tokens
        if metadata.cost:
            attributes["ai.cost.total"] = metadata.cost
        if metadata.user_id:
            attributes["user.id"] = metadata.user_id
        if metadata.conversation_id:
            attributes["conversation.id"] = metadata.conversation_id
        
        # Add extra attributes
        attributes.update(extra_attributes)
        
        try:
            with logfire.span(operation_name, **attributes) as span:
                yield span
                
                # Calculate duration
                duration_ms = (time.time() - start_time) * 1000
                span.set_attribute("operation.duration_ms", duration_ms)
                
                # Update internal metrics
                self._operation_counts[operation_name] = self._operation_counts.get(operation_name, 0) + 1
                if metadata.cost:
                    self._total_costs[operation_name] = self._total_costs.get(operation_name, 0.0) + metadata.cost
                
                logger.info(f"Operation {operation_name} completed in {duration_ms:.2f}ms")
                
        except Exception as e:
            logger.error(f"Error in traced operation {operation_name}: {e}")
            # Re-raise the exception to maintain normal error handling
            raise
    
    def log_task_analysis(
        self,
        task_text: str,
        complexity_result: Dict[str, Any],
        duration_ms: float,
        **extra_context
    ):
        """
        Log task complexity analysis operation.
        
        Args:
            task_text: The input task text
            complexity_result: Analysis result from ComplexityNet
            duration_ms: Operation duration in milliseconds
            **extra_context: Additional context to log
        """
        try:
            logfire.info(
                "Task complexity analysis completed",
                task_length=len(task_text),
                complexity_class=complexity_result.get("task_class"),
                complexity_score=complexity_result.get("score"),
                reasoning=complexity_result.get("reason"),
                duration_ms=duration_ms,
                **extra_context
            )
            
        except Exception as e:
            logger.warning(f"Failed to log task analysis: {e}")
    
    def log_model_routing(
        self,
        routing_request: Dict[str, Any],
        routing_result: Dict[str, Any],
        duration_ms: float,
        **extra_context
    ):
        """
        Log model routing operation.
        
        Args:
            routing_request: The routing request details
            routing_result: The routing decision result
            duration_ms: Operation duration in milliseconds
            **extra_context: Additional context to log
        """
        try:
            logfire.info(
                "Model routing completed",
                selected_model=routing_result.get("selected_model"),
                provider=routing_result.get("provider"),
                estimated_cost=routing_result.get("estimated_cost"),
                routing_reason=routing_result.get("reason"),
                fallback_used=routing_result.get("fallback_used", False),
                alternatives_count=len(routing_result.get("alternatives", [])),
                duration_ms=duration_ms,
                **routing_request,
                **extra_context
            )
            
        except Exception as e:
            logger.warning(f"Failed to log model routing: {e}")
    
    def log_agent_execution(
        self,
        agent_type: str,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        duration_ms: float,
        **extra_context
    ):
        """
        Log agent execution operation.
        
        Args:
            agent_type: Type of agent (chatbot, rag, image, etc.)
            request_data: The agent request details
            response_data: The agent response details
            duration_ms: Operation duration in milliseconds
            **extra_context: Additional context to log
        """
        try:
            logfire.info(
                "Agent execution completed",
                agent_type=agent_type,
                model_used=response_data.get("model_used"),
                tokens_used=response_data.get("tokens_used"),
                estimated_cost=response_data.get("metadata", {}).get("estimated_cost"),
                conversation_id=response_data.get("conversation_id"),
                response_length=len(response_data.get("response", "")),
                duration_ms=duration_ms,
                **extra_context
            )
            
        except Exception as e:
            logger.warning(f"Failed to log agent execution: {e}")
    
    def log_error(
        self,
        operation_name: str,
        error: Exception,
        context: Dict[str, Any] = None,
        **extra_attributes
    ):
        """
        Log an error with structured context.
        
        Args:
            operation_name: Name of the operation that failed
            error: The exception that occurred
            context: Additional context about the error
            **extra_attributes: Additional attributes to include
        """
        try:
            error_attributes = {
                "operation.name": operation_name,
                "error.type": type(error).__name__,
                "error.message": str(error),
                "timestamp": datetime.now().isoformat(),
            }
            
            if context:
                error_attributes.update(context)
            
            error_attributes.update(extra_attributes)
            
            logfire.error(
                f"Operation {operation_name} failed",
                **error_attributes
            )
            
        except Exception as e:
            logger.warning(f"Failed to log error: {e}")
    
    def log_performance_metrics(
        self,
        operation_name: str,
        metrics: Dict[str, Any],
        **extra_context
    ):
        """
        Log performance metrics for an operation.
        
        Args:
            operation_name: Name of the operation
            metrics: Performance metrics to log
            **extra_context: Additional context
        """
        try:
            logfire.info(
                f"Performance metrics for {operation_name}",
                operation_name=operation_name,
                **metrics,
                **extra_context
            )
            
        except Exception as e:
            logger.warning(f"Failed to log performance metrics: {e}")
    
    def get_operation_summary(self) -> Dict[str, Any]:
        """
        Get summary of operations tracked by this service.
        
        Returns:
            Dictionary with operation counts and costs
        """
        return {
            "operation_counts": self._operation_counts.copy(),
            "total_costs": self._total_costs.copy(),
            "summary_timestamp": datetime.now().isoformat()
        }
    
    def create_span_decorator(self, operation_name: str, operation_type: str):
        """
        Create a decorator for automatically tracing function calls.
        
        Args:
            operation_name: Name of the operation
            operation_type: Type of operation
            
        Returns:
            Decorator function
        """
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                metadata = SpanMetadata(
                    operation_type=operation_type,
                    # Extract metadata from function arguments if available
                )
                
                async with self.trace_operation(operation_name, metadata):
                    return await func(*args, **kwargs)
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                metadata = SpanMetadata(
                    operation_type=operation_type,
                )
                
                # For sync functions, we'll use a simpler approach
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    duration_ms = (time.time() - start_time) * 1000
                    
                    logfire.info(
                        f"{operation_name} completed",
                        operation_name=operation_name,
                        operation_type=operation_type,
                        duration_ms=duration_ms
                    )
                    
                    return result
                    
                except Exception as e:
                    duration_ms = (time.time() - start_time) * 1000
                    self.log_error(operation_name, e, {"duration_ms": duration_ms})
                    raise
            
            # Return appropriate wrapper based on function type
            if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator


# Global observability service instance
observability_service = ObservabilityService()
