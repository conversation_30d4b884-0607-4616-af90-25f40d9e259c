"""
Enhanced provider management system for AI Brain Foundation.
Implements circuit breaker pattern, intelligent selection, health monitoring, and cost optimization.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta

from pydantic import BaseModel

from ..core.config import get_settings, ProviderConfig

logger = logging.getLogger(__name__)


class ProviderStatus(str, Enum):
    """Provider status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CIRCUIT_OPEN = "circuit_open"


class CircuitBreakerState(str, Enum):
    """Circuit breaker state enumeration."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class ProviderMetrics:
    """Provider performance metrics."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    total_tokens_used: int = 0
    total_cost: float = 0.0
    last_request_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    last_failure_time: Optional[datetime] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def average_response_time(self) -> float:
        """Calculate average response time."""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests
    
    @property
    def average_cost_per_request(self) -> float:
        """Calculate average cost per request."""
        if self.total_requests == 0:
            return 0.0
        return self.total_cost / self.total_requests


@dataclass
class CircuitBreaker:
    """Circuit breaker implementation for provider fault tolerance."""
    failure_threshold: int = 5
    recovery_timeout: int = 60  # seconds
    half_open_max_calls: int = 3
    
    state: CircuitBreakerState = field(default=CircuitBreakerState.CLOSED)
    failure_count: int = field(default=0)
    last_failure_time: Optional[datetime] = field(default=None)
    half_open_calls: int = field(default=0)
    
    def can_execute(self) -> bool:
        """Check if request can be executed."""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
                self.half_open_calls = 0
                return True
            return False
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            return self.half_open_calls < self.half_open_max_calls
        
        return False
    
    def record_success(self):
        """Record successful request."""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.half_open_calls += 1
            if self.half_open_calls >= self.half_open_max_calls:
                self.state = CircuitBreakerState.CLOSED
                self.failure_count = 0
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count = 0
    
    def record_failure(self):
        """Record failed request."""
        self.failure_count += 1
        self.last_failure_time = datetime.utcnow()
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.OPEN
        elif self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        if not self.last_failure_time:
            return True
        
        time_since_failure = datetime.utcnow() - self.last_failure_time
        return time_since_failure.total_seconds() >= self.recovery_timeout


class ProviderHealth:
    """Provider health monitoring."""
    
    def __init__(self, provider_name: str):
        self.provider_name = provider_name
        self.metrics = ProviderMetrics()
        self.circuit_breaker = CircuitBreaker()
        self.status = ProviderStatus.HEALTHY
    
    def record_request(
        self, 
        success: bool, 
        response_time: float, 
        tokens_used: int = 0, 
        cost: float = 0.0
    ):
        """Record request metrics."""
        self.metrics.total_requests += 1
        self.metrics.last_request_time = datetime.utcnow()
        
        if success:
            self.metrics.successful_requests += 1
            self.metrics.total_response_time += response_time
            self.metrics.last_success_time = datetime.utcnow()
            self.circuit_breaker.record_success()
        else:
            self.metrics.failed_requests += 1
            self.metrics.last_failure_time = datetime.utcnow()
            self.circuit_breaker.record_failure()
        
        self.metrics.total_tokens_used += tokens_used
        self.metrics.total_cost += cost
        
        # Update status based on metrics
        self._update_status()
    
    def _update_status(self):
        """Update provider status based on metrics."""
        if self.circuit_breaker.state == CircuitBreakerState.OPEN:
            self.status = ProviderStatus.CIRCUIT_OPEN
        elif self.metrics.success_rate < 0.5:
            self.status = ProviderStatus.UNHEALTHY
        elif self.metrics.success_rate < 0.8:
            self.status = ProviderStatus.DEGRADED
        else:
            self.status = ProviderStatus.HEALTHY
    
    def can_handle_request(self) -> bool:
        """Check if provider can handle request."""
        return (
            self.status != ProviderStatus.CIRCUIT_OPEN and
            self.circuit_breaker.can_execute()
        )
    
    def get_health_score(self) -> float:
        """Calculate health score (0.0 to 1.0)."""
        if self.status == ProviderStatus.CIRCUIT_OPEN:
            return 0.0
        
        success_weight = 0.6
        response_time_weight = 0.3
        availability_weight = 0.1
        
        success_score = self.metrics.success_rate
        
        # Response time score (lower is better, normalize to 0-1)
        avg_response_time = self.metrics.average_response_time
        response_time_score = max(0.0, 1.0 - (avg_response_time / 10.0))  # 10s = 0 score
        
        # Availability score based on recent activity
        availability_score = 1.0
        if self.metrics.last_success_time:
            time_since_success = datetime.utcnow() - self.metrics.last_success_time
            if time_since_success.total_seconds() > 300:  # 5 minutes
                availability_score = 0.5
        
        return (
            success_score * success_weight +
            response_time_score * response_time_weight +
            availability_score * availability_weight
        )


class ProviderSelector:
    """Intelligent provider selection based on health, cost, and load balancing."""
    
    def __init__(self):
        self.provider_health: Dict[str, ProviderHealth] = {}
        self.provider_configs: Dict[str, ProviderConfig] = {}
        self.settings = get_settings()
    
    def register_provider(self, name: str, config: ProviderConfig):
        """Register a provider with its configuration."""
        self.provider_configs[name] = config
        if name not in self.provider_health:
            self.provider_health[name] = ProviderHealth(name)
    
    def select_provider(
        self, 
        model_required: Optional[str] = None,
        exclude_providers: Optional[List[str]] = None
    ) -> Optional[str]:
        """Select the best provider based on health, cost, and availability."""
        exclude_providers = exclude_providers or []
        
        # Filter available providers
        available_providers = []
        for name, config in self.provider_configs.items():
            if not config.enabled or name in exclude_providers:
                continue
            
            # Check if provider supports required model
            if model_required and config.models and model_required not in config.models:
                continue
            
            # Check if provider can handle request
            health = self.provider_health.get(name)
            if not health or not health.can_handle_request():
                continue
            
            available_providers.append(name)
        
        if not available_providers:
            return None
        
        # Sort by priority and health score
        def provider_score(provider_name: str) -> Tuple[int, float]:
            config = self.provider_configs[provider_name]
            health = self.provider_health[provider_name]
            
            # Higher priority (lower number) and higher health score is better
            return (-config.priority, health.get_health_score())
        
        available_providers.sort(key=provider_score, reverse=True)
        return available_providers[0]
    
    def record_provider_usage(
        self,
        provider_name: str,
        success: bool,
        response_time: float,
        tokens_used: int = 0,
        cost: float = 0.0
    ):
        """Record provider usage metrics."""
        if provider_name in self.provider_health:
            self.provider_health[provider_name].record_request(
                success, response_time, tokens_used, cost
            )
    
    def get_provider_status(self, provider_name: str) -> Optional[Dict[str, Any]]:
        """Get provider status and metrics."""
        if provider_name not in self.provider_health:
            return None
        
        health = self.provider_health[provider_name]
        config = self.provider_configs.get(provider_name, {})
        
        return {
            "name": provider_name,
            "status": health.status.value,
            "health_score": health.get_health_score(),
            "circuit_breaker_state": health.circuit_breaker.state.value,
            "metrics": {
                "total_requests": health.metrics.total_requests,
                "success_rate": health.metrics.success_rate,
                "average_response_time": health.metrics.average_response_time,
                "total_tokens_used": health.metrics.total_tokens_used,
                "total_cost": health.metrics.total_cost,
                "average_cost_per_request": health.metrics.average_cost_per_request,
            },
            "config": {
                "enabled": config.enabled if hasattr(config, 'enabled') else True,
                "priority": config.priority if hasattr(config, 'priority') else 1,
                "models": config.models if hasattr(config, 'models') else [],
            }
        }
    
    def get_all_provider_status(self) -> List[Dict[str, Any]]:
        """Get status for all registered providers."""
        return [
            self.get_provider_status(name) 
            for name in self.provider_configs.keys()
        ]
    
    def reset_provider_circuit_breaker(self, provider_name: str) -> bool:
        """Manually reset provider circuit breaker."""
        if provider_name in self.provider_health:
            health = self.provider_health[provider_name]
            health.circuit_breaker.state = CircuitBreakerState.CLOSED
            health.circuit_breaker.failure_count = 0
            health.status = ProviderStatus.HEALTHY
            return True
        return False


# Global provider selector instance
provider_selector = ProviderSelector()


def get_provider_selector() -> ProviderSelector:
    """Get the global provider selector instance."""
    return provider_selector
