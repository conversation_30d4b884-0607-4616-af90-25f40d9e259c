"""
Enhanced RAG Service with Qdrant vector search, document chunking, and semantic search.
"""

import logging
import time
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
from sentence_transformers import SentenceTransformer
import numpy as np

from .observability import observability_service, SpanMetadata
from ..config import get_settings

logger = logging.getLogger(__name__)


@dataclass
class DocumentChunk:
    """Document chunk with metadata."""
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None


@dataclass
class RAGResult:
    """RAG retrieval result."""
    chunks: List[DocumentChunk]
    query: str
    similarity_scores: List[float]
    total_chunks: int
    retrieval_time_ms: float


class DocumentChunker:
    """Advanced document chunking strategies."""
    
    def __init__(self, chunk_size: int = 512, chunk_overlap: int = 50):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def chunk_text(self, text: str, metadata: Dict[str, Any] = None) -> List[DocumentChunk]:
        """Chunk text using sliding window approach."""
        if not text.strip():
            return []
        
        chunks = []
        words = text.split()
        
        for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + self.chunk_size]
            chunk_text = ' '.join(chunk_words)
            
            # Generate unique ID for chunk
            chunk_id = hashlib.md5(f"{chunk_text}_{i}".encode()).hexdigest()
            
            chunk_metadata = {
                "chunk_index": i // (self.chunk_size - self.chunk_overlap),
                "word_count": len(chunk_words),
                "start_word": i,
                "end_word": min(i + self.chunk_size, len(words)),
                **(metadata or {})
            }
            
            chunks.append(DocumentChunk(
                id=chunk_id,
                content=chunk_text,
                metadata=chunk_metadata
            ))
        
        return chunks
    
    def chunk_by_sentences(self, text: str, metadata: Dict[str, Any] = None) -> List[DocumentChunk]:
        """Chunk text by sentences for better semantic coherence."""
        import re
        
        # Simple sentence splitting (can be enhanced with spaCy/NLTK)
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        chunks = []
        current_chunk = []
        current_length = 0
        
        for i, sentence in enumerate(sentences):
            sentence_length = len(sentence.split())
            
            if current_length + sentence_length > self.chunk_size and current_chunk:
                # Create chunk from current sentences
                chunk_text = '. '.join(current_chunk) + '.'
                chunk_id = hashlib.md5(f"{chunk_text}_{len(chunks)}".encode()).hexdigest()
                
                chunk_metadata = {
                    "chunk_index": len(chunks),
                    "sentence_count": len(current_chunk),
                    "word_count": current_length,
                    "chunking_method": "sentence_based",
                    **(metadata or {})
                }
                
                chunks.append(DocumentChunk(
                    id=chunk_id,
                    content=chunk_text,
                    metadata=chunk_metadata
                ))
                
                # Start new chunk with overlap
                overlap_sentences = current_chunk[-self.chunk_overlap//10:] if self.chunk_overlap > 0 else []
                current_chunk = overlap_sentences + [sentence]
                current_length = sum(len(s.split()) for s in current_chunk)
            else:
                current_chunk.append(sentence)
                current_length += sentence_length
        
        # Add final chunk
        if current_chunk:
            chunk_text = '. '.join(current_chunk) + '.'
            chunk_id = hashlib.md5(f"{chunk_text}_{len(chunks)}".encode()).hexdigest()
            
            chunk_metadata = {
                "chunk_index": len(chunks),
                "sentence_count": len(current_chunk),
                "word_count": current_length,
                "chunking_method": "sentence_based",
                **(metadata or {})
            }
            
            chunks.append(DocumentChunk(
                id=chunk_id,
                content=chunk_text,
                metadata=chunk_metadata
            ))
        
        return chunks


class EnhancedRAGService:
    """Enhanced RAG service with Qdrant vector search and advanced chunking."""
    
    def __init__(self):
        self.settings = get_settings()
        self.client = None
        self.embedding_model = None
        self.chunker = DocumentChunker()
        self.collection_name = "documents"
        self.embedding_dimension = 384  # all-MiniLM-L6-v2 dimension
        
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialize Qdrant client and embedding model."""
        try:
            # Initialize Qdrant client
            self.client = QdrantClient(
                host=getattr(self.settings, 'qdrant_host', 'localhost'),
                port=getattr(self.settings, 'qdrant_port', 6333)
            )
            
            # Initialize embedding model
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            self.embedding_dimension = self.embedding_model.get_sentence_embedding_dimension()
            
            # Create collection if it doesn't exist
            self._ensure_collection_exists()
            
            logger.info("Enhanced RAG service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG service: {e}")
            # Use fallback mode without vector search
            self.client = None
            self.embedding_model = None
    
    def _ensure_collection_exists(self):
        """Ensure the document collection exists in Qdrant."""
        if not self.client:
            return
        
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.embedding_dimension,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created Qdrant collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"Error ensuring collection exists: {e}")
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts."""
        if not self.embedding_model:
            logger.warning("Embedding model not available, returning empty embeddings")
            return [[0.0] * self.embedding_dimension for _ in texts]
        
        try:
            embeddings = self.embedding_model.encode(texts, convert_to_numpy=True)
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            return [[0.0] * self.embedding_dimension for _ in texts]
    
    async def index_document(
        self,
        content: str,
        metadata: Dict[str, Any],
        chunking_strategy: str = "sentences"
    ) -> Dict[str, Any]:
        """Index a document with advanced chunking and vector storage."""
        start_time = time.time()
        
        metadata_span = SpanMetadata(
            operation_type="document_indexing",
            document_id=metadata.get('document_id', 'unknown')
        )
        
        async with observability_service.trace_operation("rag_index_document", metadata_span) as span:
            try:
                # Choose chunking strategy
                if chunking_strategy == "sentences":
                    chunks = self.chunker.chunk_by_sentences(content, metadata)
                else:
                    chunks = self.chunker.chunk_text(content, metadata)
                
                if not chunks:
                    return {"status": "error", "message": "No chunks generated from content"}
                
                # Generate embeddings for chunks
                chunk_texts = [chunk.content for chunk in chunks]
                embeddings = self.generate_embeddings(chunk_texts)
                
                # Add embeddings to chunks
                for chunk, embedding in zip(chunks, embeddings):
                    chunk.embedding = embedding
                
                # Store in Qdrant if available
                indexed_count = 0
                if self.client:
                    try:
                        points = [
                            PointStruct(
                                id=chunk.id,
                                vector=chunk.embedding,
                                payload={
                                    "content": chunk.content,
                                    "metadata": chunk.metadata
                                }
                            )
                            for chunk in chunks
                        ]
                        
                        self.client.upsert(
                            collection_name=self.collection_name,
                            points=points
                        )
                        indexed_count = len(points)
                        
                    except Exception as e:
                        logger.error(f"Error storing chunks in Qdrant: {e}")
                
                duration_ms = (time.time() - start_time) * 1000
                
                result = {
                    "status": "success",
                    "chunks_created": len(chunks),
                    "chunks_indexed": indexed_count,
                    "chunking_strategy": chunking_strategy,
                    "processing_time_ms": duration_ms,
                    "document_metadata": metadata
                }
                
                # Log indexing operation
                observability_service.log_task_analysis(
                    task_text=f"Document indexing: {len(content)} chars",
                    complexity_result={
                        "chunks_created": len(chunks),
                        "chunks_indexed": indexed_count,
                        "strategy": chunking_strategy
                    },
                    duration_ms=duration_ms,
                    features_count=len(chunks)
                )
                
                return result
                
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                logger.error(f"Error indexing document: {e}")
                
                observability_service.log_error(
                    operation_name="rag_index_document",
                    error=e,
                    context={
                        "content_length": len(content),
                        "chunking_strategy": chunking_strategy,
                        "duration_ms": duration_ms
                    }
                )
                
                return {"status": "error", "message": str(e)}


    async def search_documents(
        self,
        query: str,
        limit: int = 5,
        score_threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> RAGResult:
        """Search documents using semantic similarity."""
        start_time = time.time()

        metadata_span = SpanMetadata(
            operation_type="document_search",
            query=query[:100]  # Truncate for logging
        )

        async with observability_service.trace_operation("rag_search_documents", metadata_span) as span:
            try:
                if not self.client or not self.embedding_model:
                    logger.warning("RAG service not fully initialized, returning empty results")
                    return RAGResult(
                        chunks=[],
                        query=query,
                        similarity_scores=[],
                        total_chunks=0,
                        retrieval_time_ms=(time.time() - start_time) * 1000
                    )

                # Generate query embedding
                query_embedding = self.generate_embeddings([query])[0]

                # Build Qdrant filter if provided
                qdrant_filter = None
                if filters:
                    conditions = []
                    for key, value in filters.items():
                        conditions.append(
                            FieldCondition(
                                key=f"metadata.{key}",
                                match=MatchValue(value=value)
                            )
                        )
                    if conditions:
                        qdrant_filter = Filter(must=conditions)

                # Search in Qdrant
                search_results = self.client.search(
                    collection_name=self.collection_name,
                    query_vector=query_embedding,
                    limit=limit,
                    score_threshold=score_threshold,
                    query_filter=qdrant_filter
                )

                # Convert results to DocumentChunks
                chunks = []
                scores = []

                for result in search_results:
                    chunk = DocumentChunk(
                        id=result.id,
                        content=result.payload["content"],
                        metadata=result.payload["metadata"],
                        embedding=None  # Don't return embeddings to save memory
                    )
                    chunks.append(chunk)
                    scores.append(result.score)

                duration_ms = (time.time() - start_time) * 1000

                rag_result = RAGResult(
                    chunks=chunks,
                    query=query,
                    similarity_scores=scores,
                    total_chunks=len(chunks),
                    retrieval_time_ms=duration_ms
                )

                # Log search operation
                observability_service.log_task_analysis(
                    task_text=f"RAG search: {query}",
                    complexity_result={
                        "results_found": len(chunks),
                        "avg_score": np.mean(scores) if scores else 0.0,
                        "filters_applied": bool(filters)
                    },
                    duration_ms=duration_ms,
                    features_count=len(chunks)
                )

                return rag_result

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                logger.error(f"Error searching documents: {e}")

                observability_service.log_error(
                    operation_name="rag_search_documents",
                    error=e,
                    context={
                        "query": query,
                        "limit": limit,
                        "duration_ms": duration_ms
                    }
                )

                return RAGResult(
                    chunks=[],
                    query=query,
                    similarity_scores=[],
                    total_chunks=0,
                    retrieval_time_ms=duration_ms
                )

    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the document collection."""
        try:
            if not self.client:
                return {"status": "unavailable", "message": "Qdrant client not initialized"}

            collection_info = self.client.get_collection(self.collection_name)

            return {
                "status": "available",
                "total_documents": collection_info.points_count,
                "vector_dimension": collection_info.config.params.vectors.size,
                "distance_metric": collection_info.config.params.vectors.distance.value,
                "collection_name": self.collection_name
            }

        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {"status": "error", "message": str(e)}

    async def delete_document(self, document_id: str) -> Dict[str, Any]:
        """Delete all chunks for a specific document."""
        try:
            if not self.client:
                return {"status": "error", "message": "Qdrant client not initialized"}

            # Delete points with matching document_id in metadata
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key="metadata.document_id",
                        match=MatchValue(value=document_id)
                    )
                ]
            )

            result = self.client.delete(
                collection_name=self.collection_name,
                points_selector=filter_condition
            )

            return {
                "status": "success",
                "document_id": document_id,
                "operation_id": result.operation_id if hasattr(result, 'operation_id') else None
            }

        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            return {"status": "error", "message": str(e)}


# Global RAG service instance
rag_service = EnhancedRAGService()
