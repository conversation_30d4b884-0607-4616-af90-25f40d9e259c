"""
Router Service for intelligent model selection based on task complexity and cost optimization.
"""

import logging
import time
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from dataclasses import dataclass

from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..core.database import get_db, ModelConfig, RoutingPolicy
from ..config import ProviderType, get_settings
from .observability import observability_service, SpanMetadata
from .task_analyzer import complexity_analyzer, TaskComplexity, TaskAnalysisResult


logger = logging.getLogger(__name__)


@dataclass
class ModelOption:
    """Model option with cost and capability information."""
    name: str
    provider: str
    cost_per_1k_input: Decimal
    cost_per_1k_output: Decimal
    max_context: int
    tier: str
    is_available: bool


class ModelSelectionResult(BaseModel):
    """Result of model selection."""
    selected_model: str
    provider: str
    reason: str
    estimated_cost: Decimal
    fallback_used: bool
    alternatives: List[str]


class RouterService:
    """
    Intelligent router that selects the most cost-efficient model
    based on task complexity and routing policies.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self._model_cache: Dict[str, ModelOption] = {}
        self._policy_cache: Dict[TaskComplexity, RoutingPolicy] = {}
        self._refresh_cache()
    
    def _refresh_cache(self):
        """Refresh model and policy caches from database."""
        try:
            db = next(get_db())
            
            # Cache model configurations
            models = db.query(ModelConfig).filter(ModelConfig.is_enabled == True).all()
            self._model_cache = {
                model.name: ModelOption(
                    name=model.name,
                    provider=model.provider,
                    cost_per_1k_input=model.price_input_per_1k,
                    cost_per_1k_output=model.price_output_per_1k,
                    max_context=model.max_context_tokens,
                    tier=model.tier,
                    is_available=self._is_provider_available(model.provider)
                )
                for model in models
            }
            
            # Cache routing policies
            policies = db.query(RoutingPolicy).filter(RoutingPolicy.is_active == True).all()
            self._policy_cache = {
                TaskComplexity(policy.task_class): policy
                for policy in policies
            }
            
            db.close()
            logger.info(f"Refreshed cache: {len(self._model_cache)} models, {len(self._policy_cache)} policies")
            
        except Exception as e:
            logger.error(f"Failed to refresh cache: {e}")
            # Use fallback configuration
            self._use_fallback_cache()
    
    def _use_fallback_cache(self):
        """Use fallback model configuration when database is unavailable."""
        logger.warning("Using fallback model configuration")
        
        # Default free models
        self._model_cache = {
            'gemini-2.0-flash': ModelOption(
                name='gemini-2.0-flash',
                provider='google',
                cost_per_1k_input=Decimal('0.0'),
                cost_per_1k_output=Decimal('0.0'),
                max_context=1000000,
                tier='free',
                is_available=self._is_provider_available('google')
            ),
            'llama-3.3-70b-versatile': ModelOption(
                name='llama-3.3-70b-versatile',
                provider='groq',
                cost_per_1k_input=Decimal('0.0'),
                cost_per_1k_output=Decimal('0.0'),
                max_context=32768,
                tier='free',
                is_available=self._is_provider_available('groq')
            ),
            'gpt-4o-mini': ModelOption(
                name='gpt-4o-mini',
                provider='openai',
                cost_per_1k_input=Decimal('0.15'),
                cost_per_1k_output=Decimal('0.60'),
                max_context=128000,
                tier='paid',
                is_available=self._is_provider_available('openai')
            )
        }
    
    def _is_provider_available(self, provider: str) -> bool:
        """Check if provider is available based on API keys."""
        provider_configs = self.settings.get_provider_configs()
        return any(config.provider_type.value == provider for config in provider_configs.values())
    
    def pick(
        self,
        text: str,
        modality: str = "text",
        preferred_provider: Optional[str] = None,
        max_cost: Optional[Decimal] = None
    ) -> ModelSelectionResult:
        """
        Pick the best model for the given task.

        Args:
            text: Input text to analyze
            modality: Type of task (text, image, etc.)
            preferred_provider: Preferred provider if any
            max_cost: Maximum cost constraint

        Returns:
            ModelSelectionResult with selected model and reasoning
        """
        start_time = time.time()

        try:
            # Analyze task complexity
            analysis = complexity_analyzer.analyze(text)

            # Get routing policy for this complexity level
            policy = self._policy_cache.get(analysis.task_class)

            if not policy:
                logger.warning(f"No routing policy found for {analysis.task_class}, using fallback")
                result = self._fallback_selection(analysis, preferred_provider)
            else:
                # Get candidate models based on policy
                candidates = self._get_candidate_models(policy, modality, preferred_provider)

                if not candidates:
                    logger.warning("No candidate models found, using fallback")
                    result = self._fallback_selection(analysis, preferred_provider)
                else:
                    # Select best model based on cost and availability
                    selected = self._select_best_model(candidates, text, max_cost)

                    if not selected:
                        logger.warning("No suitable model found, using fallback")
                        result = self._fallback_selection(analysis, preferred_provider)
                    else:
                        # Estimate cost
                        estimated_cost = self._estimate_cost(selected, text)

                        result = ModelSelectionResult(
                            selected_model=selected.name,
                            provider=selected.provider,
                            reason=f"Selected for {analysis.task_class} task (score: {analysis.score:.2f})",
                            estimated_cost=estimated_cost,
                            fallback_used=False,
                            alternatives=[c.name for c in candidates if c != selected][:3]
                        )

            # Log the routing decision with observability
            duration_ms = (time.time() - start_time) * 1000
            observability_service.log_model_routing(
                routing_request={
                    "text_length": len(text),
                    "modality": modality,
                    "preferred_provider": preferred_provider,
                    "max_cost": float(max_cost) if max_cost else None,
                    "task_complexity": analysis.task_class.value,
                    "complexity_score": analysis.score
                },
                routing_result={
                    "selected_model": result.selected_model,
                    "provider": result.provider,
                    "estimated_cost": float(result.estimated_cost),
                    "reason": result.reason,
                    "fallback_used": result.fallback_used,
                    "alternatives": result.alternatives
                },
                duration_ms=duration_ms,
                candidates_count=len(self._model_cache)
            )

            return result

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(f"Error in model routing: {e}")

            # Log the error
            observability_service.log_error(
                operation_name="model_routing",
                error=e,
                context={
                    "text_length": len(text),
                    "modality": modality,
                    "duration_ms": duration_ms
                }
            )

            # Return emergency fallback
            return ModelSelectionResult(
                selected_model=self.settings.default_model,
                provider=self.settings.primary_provider.value,
                reason=f"Emergency fallback due to error: {str(e)}",
                estimated_cost=Decimal('0.01'),
                fallback_used=True,
                alternatives=[]
            )
    
    def _get_candidate_models(
        self, 
        policy: RoutingPolicy, 
        modality: str,
        preferred_provider: Optional[str]
    ) -> List[ModelOption]:
        """Get candidate models based on routing policy."""
        candidates = []
        
        # Start with preferred models from policy
        model_names = policy.preferred_models or []
        
        # Add fallback models if needed
        if policy.fallback_models:
            model_names.extend(policy.fallback_models)
        
        for model_name in model_names:
            if model_name in self._model_cache:
                model = self._model_cache[model_name]
                
                # Filter by availability
                if not model.is_available:
                    continue
                
                # Filter by preferred provider
                if preferred_provider and model.provider != preferred_provider:
                    continue
                
                # Filter by modality (for future vision support)
                if modality == "image" and "vision" not in model.name.lower():
                    continue
                
                candidates.append(model)
        
        # Sort by cost (free models first, then by cost)
        candidates.sort(key=lambda m: (m.tier != 'free', m.cost_per_1k_input + m.cost_per_1k_output))
        
        return candidates
    
    def _select_best_model(
        self, 
        candidates: List[ModelOption], 
        text: str,
        max_cost: Optional[Decimal]
    ) -> Optional[ModelOption]:
        """Select the best model from candidates."""
        if not candidates:
            return None
        
        # Estimate token count (rough approximation)
        estimated_tokens = len(text.split()) * 1.3  # ~1.3 tokens per word
        
        for model in candidates:
            # Check context length
            if estimated_tokens > model.max_context:
                continue
            
            # Check cost constraint
            if max_cost:
                estimated_cost = self._estimate_cost(model, text)
                if estimated_cost > max_cost:
                    continue
            
            return model
        
        # If no model meets constraints, return the cheapest available
        return candidates[0] if candidates else None
    
    def _estimate_cost(self, model: ModelOption, text: str) -> Decimal:
        """Estimate cost for processing the text."""
        # Rough token estimation
        input_tokens = len(text.split()) * 1.3
        output_tokens = input_tokens * 0.5  # Assume 50% output ratio
        
        input_cost = (Decimal(str(input_tokens)) / 1000) * model.cost_per_1k_input
        output_cost = (Decimal(str(output_tokens)) / 1000) * model.cost_per_1k_output
        
        return input_cost + output_cost
    
    def _fallback_selection(
        self, 
        analysis: TaskAnalysisResult,
        preferred_provider: Optional[str]
    ) -> ModelSelectionResult:
        """Fallback model selection when routing fails."""
        # Try to use free models first
        free_models = [m for m in self._model_cache.values() if m.tier == 'free' and m.is_available]
        
        if preferred_provider:
            free_models = [m for m in free_models if m.provider == preferred_provider]
        
        if free_models:
            selected = free_models[0]
            return ModelSelectionResult(
                selected_model=selected.name,
                provider=selected.provider,
                reason=f"Fallback selection for {analysis.task_class} task",
                estimated_cost=Decimal('0.0'),
                fallback_used=True,
                alternatives=[m.name for m in free_models[1:]][:3]
            )
        
        # If no free models, use cheapest available
        available_models = [m for m in self._model_cache.values() if m.is_available]
        if available_models:
            selected = min(available_models, key=lambda m: m.cost_per_1k_input + m.cost_per_1k_output)
            return ModelSelectionResult(
                selected_model=selected.name,
                provider=selected.provider,
                reason=f"Emergency fallback for {analysis.task_class} task",
                estimated_cost=Decimal('0.01'),  # Minimal cost estimate
                fallback_used=True,
                alternatives=[]
            )
        
        # Last resort - use primary provider default
        return ModelSelectionResult(
            selected_model=self.settings.default_model,
            provider=self.settings.primary_provider.value,
            reason="System default fallback",
            estimated_cost=Decimal('0.01'),
            fallback_used=True,
            alternatives=[]
        )


# Global router instance
router_service = RouterService()
