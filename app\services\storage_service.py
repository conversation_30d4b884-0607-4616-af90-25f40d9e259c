"""
Storage service using Supabase Storage.
"""

import logging
import os
from typing import Optional, Dict, Any, List, BinaryIO, Union
from datetime import datetime
from pathlib import Path
from pydantic import BaseModel

from .supabase_client import get_supabase_service, handle_supabase_error, SupabaseStorageError

logger = logging.getLogger(__name__)


class FileUpload(BaseModel):
    """File upload model."""
    bucket: str
    path: str
    file_data: bytes
    content_type: Optional[str] = None
    cache_control: Optional[str] = None
    upsert: bool = False


class FileInfo(BaseModel):
    """File information model."""
    name: str
    id: str
    updated_at: datetime
    created_at: datetime
    last_accessed_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    size: Optional[int] = None
    mimetype: Optional[str] = None
    etag: Optional[str] = None


class StorageService:
    """Storage service using Supabase Storage."""
    
    def __init__(self):
        """Initialize the storage service."""
        self.supabase = get_supabase_service()
        self.default_bucket = "files"  # Default bucket name
    
    @handle_supabase_error
    async def create_bucket(self, bucket_name: str, public: bool = False) -> bool:
        """Create a new storage bucket."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        try:
            response = self.supabase.client.storage.create_bucket(bucket_name, {"public": public})
            logger.info(f"Created bucket: {bucket_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to create bucket {bucket_name}: {e}")
            return False
    
    @handle_supabase_error
    async def list_buckets(self) -> List[Dict[str, Any]]:
        """List all storage buckets."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        response = self.supabase.client.storage.list_buckets()
        return response
    
    @handle_supabase_error
    async def upload_file(
        self,
        file_path: str,
        file_data: Union[bytes, BinaryIO],
        bucket: Optional[str] = None,
        content_type: Optional[str] = None,
        upsert: bool = False
    ) -> str:
        """Upload a file to storage."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        bucket_name = bucket or self.default_bucket
        
        # Ensure bucket exists
        await self._ensure_bucket_exists(bucket_name)
        
        # Prepare upload options
        options = {}
        if content_type:
            options["content_type"] = content_type
        if upsert:
            options["upsert"] = True
        
        # Upload file
        response = self.supabase.client.storage.from_(bucket_name).upload(
            file_path, file_data, options
        )
        
        if hasattr(response, 'error') and response.error:
            raise SupabaseStorageError(f"Upload failed: {response.error}")
        
        logger.info(f"Uploaded file: {file_path} to bucket: {bucket_name}")
        return file_path
    
    @handle_supabase_error
    async def download_file(self, file_path: str, bucket: Optional[str] = None) -> bytes:
        """Download a file from storage."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        bucket_name = bucket or self.default_bucket
        
        response = self.supabase.client.storage.from_(bucket_name).download(file_path)
        
        if hasattr(response, 'error') and response.error:
            raise SupabaseStorageError(f"Download failed: {response.error}")
        
        return response
    
    @handle_supabase_error
    async def delete_file(self, file_path: str, bucket: Optional[str] = None) -> bool:
        """Delete a file from storage."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        bucket_name = bucket or self.default_bucket
        
        response = self.supabase.client.storage.from_(bucket_name).remove([file_path])
        
        if hasattr(response, 'error') and response.error:
            raise SupabaseStorageError(f"Delete failed: {response.error}")
        
        logger.info(f"Deleted file: {file_path} from bucket: {bucket_name}")
        return True
    
    @handle_supabase_error
    async def list_files(
        self,
        folder_path: str = "",
        bucket: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[FileInfo]:
        """List files in a bucket/folder."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        bucket_name = bucket or self.default_bucket
        
        options = {}
        if limit:
            options["limit"] = limit
        if offset:
            options["offset"] = offset
        
        response = self.supabase.client.storage.from_(bucket_name).list(folder_path, options)
        
        if hasattr(response, 'error') and response.error:
            raise SupabaseStorageError(f"List files failed: {response.error}")
        
        # Convert response to FileInfo objects
        files = []
        for file_data in response:
            files.append(FileInfo(
                name=file_data.get("name", ""),
                id=file_data.get("id", ""),
                updated_at=datetime.fromisoformat(file_data.get("updated_at", "").replace('Z', '+00:00')),
                created_at=datetime.fromisoformat(file_data.get("created_at", "").replace('Z', '+00:00')),
                last_accessed_at=datetime.fromisoformat(file_data.get("last_accessed_at", "").replace('Z', '+00:00')) if file_data.get("last_accessed_at") else None,
                metadata=file_data.get("metadata"),
                size=file_data.get("size"),
                mimetype=file_data.get("mimetype"),
                etag=file_data.get("etag")
            ))
        
        return files
    
    @handle_supabase_error
    async def get_public_url(self, file_path: str, bucket: Optional[str] = None) -> str:
        """Get public URL for a file."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        bucket_name = bucket or self.default_bucket
        
        response = self.supabase.client.storage.from_(bucket_name).get_public_url(file_path)
        return response
    
    @handle_supabase_error
    async def create_signed_url(
        self,
        file_path: str,
        expires_in: int = 3600,
        bucket: Optional[str] = None
    ) -> str:
        """Create a signed URL for private file access."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        bucket_name = bucket or self.default_bucket
        
        response = self.supabase.client.storage.from_(bucket_name).create_signed_url(
            file_path, expires_in
        )
        
        if hasattr(response, 'error') and response.error:
            raise SupabaseStorageError(f"Create signed URL failed: {response.error}")
        
        return response.get("signedURL", "")
    
    @handle_supabase_error
    async def move_file(
        self,
        from_path: str,
        to_path: str,
        bucket: Optional[str] = None
    ) -> bool:
        """Move/rename a file."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        bucket_name = bucket or self.default_bucket
        
        response = self.supabase.client.storage.from_(bucket_name).move(from_path, to_path)
        
        if hasattr(response, 'error') and response.error:
            raise SupabaseStorageError(f"Move file failed: {response.error}")
        
        logger.info(f"Moved file from {from_path} to {to_path} in bucket: {bucket_name}")
        return True
    
    @handle_supabase_error
    async def copy_file(
        self,
        from_path: str,
        to_path: str,
        bucket: Optional[str] = None
    ) -> bool:
        """Copy a file."""
        if not self.supabase.is_available:
            raise SupabaseStorageError("Supabase is not available")
        
        bucket_name = bucket or self.default_bucket
        
        response = self.supabase.client.storage.from_(bucket_name).copy(from_path, to_path)
        
        if hasattr(response, 'error') and response.error:
            raise SupabaseStorageError(f"Copy file failed: {response.error}")
        
        logger.info(f"Copied file from {from_path} to {to_path} in bucket: {bucket_name}")
        return True
    
    async def _ensure_bucket_exists(self, bucket_name: str) -> None:
        """Ensure a bucket exists, create if it doesn't."""
        try:
            buckets = await self.list_buckets()
            bucket_names = [bucket.get("name") for bucket in buckets]
            
            if bucket_name not in bucket_names:
                await self.create_bucket(bucket_name, public=False)
        except Exception as e:
            logger.warning(f"Could not verify/create bucket {bucket_name}: {e}")
    
    async def upload_file_from_path(
        self,
        local_file_path: str,
        storage_path: Optional[str] = None,
        bucket: Optional[str] = None
    ) -> str:
        """Upload a file from local file system."""
        local_path = Path(local_file_path)
        
        if not local_path.exists():
            raise SupabaseStorageError(f"Local file not found: {local_file_path}")
        
        # Use filename as storage path if not provided
        if storage_path is None:
            storage_path = local_path.name
        
        # Read file data
        with open(local_path, "rb") as f:
            file_data = f.read()
        
        # Determine content type
        content_type = None
        if local_path.suffix:
            import mimetypes
            content_type, _ = mimetypes.guess_type(str(local_path))
        
        return await self.upload_file(storage_path, file_data, bucket, content_type)


# Global storage service instance
_storage_service: Optional[StorageService] = None


def get_storage_service() -> StorageService:
    """Get the global storage service instance."""
    global _storage_service
    if _storage_service is None:
        _storage_service = StorageService()
    return _storage_service
