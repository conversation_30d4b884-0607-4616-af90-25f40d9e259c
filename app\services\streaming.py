"""
Streaming service for real-time AI interactions.
"""

import json
import logging
import asyncio
import time
from typing import Dict, Any, AsyncGenerator, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

from .observability import observability_service, SpanMetadata

logger = logging.getLogger(__name__)


class StreamEventType(Enum):
    """Types of streaming events."""
    START = "start"
    CHUNK = "chunk"
    FUNCTION_CALL = "function_call"
    FUNCTION_RESULT = "function_result"
    ERROR = "error"
    END = "end"
    METADATA = "metadata"


@dataclass
class StreamEvent:
    """Represents a streaming event."""
    type: StreamEventType
    data: Any
    timestamp: float
    event_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class StreamingService:
    """Service for managing streaming AI interactions."""
    
    def __init__(self):
        self.active_streams: Dict[str, Dict[str, Any]] = {}
    
    async def create_stream(
        self,
        stream_id: str,
        agent_type: str,
        conversation_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Create a new streaming session."""
        self.active_streams[stream_id] = {
            "agent_type": agent_type,
            "conversation_id": conversation_id,
            "created_at": time.time(),
            "metadata": metadata or {},
            "events": []
        }
        
        logger.info(f"Created stream: {stream_id} for agent: {agent_type}")
    
    async def stream_chat_response(
        self,
        stream_id: str,
        message: str,
        agent_type: str,
        conversation_id: str = "default",
        **kwargs
    ) -> AsyncGenerator[StreamEvent, None]:
        """Stream a chat response from an AI agent."""
        start_time = time.time()
        
        metadata_span = SpanMetadata(
            operation_type="streaming_chat",
            agent_type=agent_type,
            conversation_id=conversation_id,
            stream_id=stream_id
        )
        
        async with observability_service.trace_operation("stream_chat_response", metadata_span) as span:
            try:
                # Create stream if it doesn't exist
                if stream_id not in self.active_streams:
                    await self.create_stream(stream_id, agent_type, conversation_id)
                
                # Send start event
                start_event = StreamEvent(
                    type=StreamEventType.START,
                    data={
                        "message": "Starting chat response",
                        "agent_type": agent_type,
                        "conversation_id": conversation_id
                    },
                    timestamp=time.time(),
                    event_id=f"{stream_id}_start"
                )
                yield start_event
                
                # Simulate streaming response (in production, this would integrate with actual AI providers)
                response_text = f"This is a streaming response from {agent_type} agent for message: {message}"
                
                # Send metadata event
                metadata_event = StreamEvent(
                    type=StreamEventType.METADATA,
                    data={
                        "model": "simulated-streaming-model",
                        "provider": "internal",
                        "estimated_tokens": len(response_text.split()),
                        "conversation_id": conversation_id
                    },
                    timestamp=time.time(),
                    event_id=f"{stream_id}_metadata"
                )
                yield metadata_event
                
                # Stream response in chunks
                words = response_text.split()
                for i, word in enumerate(words):
                    # Add small delay to simulate real streaming
                    await asyncio.sleep(0.1)
                    
                    chunk_event = StreamEvent(
                        type=StreamEventType.CHUNK,
                        data={
                            "content": word + " ",
                            "chunk_index": i,
                            "is_final": i == len(words) - 1
                        },
                        timestamp=time.time(),
                        event_id=f"{stream_id}_chunk_{i}"
                    )
                    yield chunk_event
                
                # Send end event
                end_event = StreamEvent(
                    type=StreamEventType.END,
                    data={
                        "message": "Chat response completed",
                        "total_chunks": len(words),
                        "duration_ms": (time.time() - start_time) * 1000
                    },
                    timestamp=time.time(),
                    event_id=f"{stream_id}_end"
                )
                yield end_event
                
                # Log streaming completion
                observability_service.log_agent_execution(
                    agent_type=agent_type,
                    request_data={"message": message, "streaming": True},
                    response_data={"response": response_text, "chunks": len(words)},
                    duration_ms=(time.time() - start_time) * 1000,
                    model_name="simulated-streaming-model",
                    provider="internal"
                )
                
            except Exception as e:
                logger.error(f"Error in streaming chat response: {e}")
                
                error_event = StreamEvent(
                    type=StreamEventType.ERROR,
                    data={
                        "error": str(e),
                        "error_type": type(e).__name__
                    },
                    timestamp=time.time(),
                    event_id=f"{stream_id}_error"
                )
                yield error_event
                
                observability_service.log_error(
                    operation_name="stream_chat_response",
                    error=e,
                    context={
                        "stream_id": stream_id,
                        "agent_type": agent_type,
                        "message": message
                    }
                )
    
    async def stream_function_calling(
        self,
        stream_id: str,
        message: str,
        available_tools: List[str],
        agent_type: str,
        conversation_id: str = "default",
        **kwargs
    ) -> AsyncGenerator[StreamEvent, None]:
        """Stream a response with function calling capabilities."""
        start_time = time.time()
        
        metadata_span = SpanMetadata(
            operation_type="streaming_function_calling",
            agent_type=agent_type,
            conversation_id=conversation_id,
            stream_id=stream_id
        )
        
        async with observability_service.trace_operation("stream_function_calling", metadata_span) as span:
            try:
                # Create stream if it doesn't exist
                if stream_id not in self.active_streams:
                    await self.create_stream(stream_id, agent_type, conversation_id)
                
                # Send start event
                start_event = StreamEvent(
                    type=StreamEventType.START,
                    data={
                        "message": "Starting function calling response",
                        "agent_type": agent_type,
                        "available_tools": available_tools
                    },
                    timestamp=time.time(),
                    event_id=f"{stream_id}_start"
                )
                yield start_event
                
                # Simulate thinking/analysis phase
                thinking_chunks = [
                    "Analyzing your request...",
                    "Determining which tools to use...",
                    "Preparing function calls..."
                ]
                
                for i, chunk in enumerate(thinking_chunks):
                    await asyncio.sleep(0.2)
                    
                    chunk_event = StreamEvent(
                        type=StreamEventType.CHUNK,
                        data={
                            "content": chunk + "\n",
                            "chunk_index": i,
                            "phase": "thinking"
                        },
                        timestamp=time.time(),
                        event_id=f"{stream_id}_thinking_{i}"
                    )
                    yield chunk_event
                
                # Simulate function call (if tools are available)
                if available_tools and "calculator" in available_tools:
                    function_call_event = StreamEvent(
                        type=StreamEventType.FUNCTION_CALL,
                        data={
                            "function_name": "calculator",
                            "arguments": {"expression": "2 + 2"},
                            "call_id": f"call_{stream_id}_1"
                        },
                        timestamp=time.time(),
                        event_id=f"{stream_id}_function_call"
                    )
                    yield function_call_event
                    
                    # Simulate function execution delay
                    await asyncio.sleep(0.5)
                    
                    function_result_event = StreamEvent(
                        type=StreamEventType.FUNCTION_RESULT,
                        data={
                            "call_id": f"call_{stream_id}_1",
                            "result": {"expression": "2 + 2", "result": 4, "type": "int"},
                            "status": "completed"
                        },
                        timestamp=time.time(),
                        event_id=f"{stream_id}_function_result"
                    )
                    yield function_result_event
                
                # Stream final response
                final_response = "Based on the analysis and function calls, here's my response to your request."
                words = final_response.split()
                
                for i, word in enumerate(words):
                    await asyncio.sleep(0.1)
                    
                    chunk_event = StreamEvent(
                        type=StreamEventType.CHUNK,
                        data={
                            "content": word + " ",
                            "chunk_index": i + len(thinking_chunks),
                            "phase": "response",
                            "is_final": i == len(words) - 1
                        },
                        timestamp=time.time(),
                        event_id=f"{stream_id}_response_{i}"
                    )
                    yield chunk_event
                
                # Send end event
                end_event = StreamEvent(
                    type=StreamEventType.END,
                    data={
                        "message": "Function calling response completed",
                        "total_chunks": len(thinking_chunks) + len(words),
                        "function_calls_made": 1 if available_tools else 0,
                        "duration_ms": (time.time() - start_time) * 1000
                    },
                    timestamp=time.time(),
                    event_id=f"{stream_id}_end"
                )
                yield end_event
                
            except Exception as e:
                logger.error(f"Error in streaming function calling: {e}")
                
                error_event = StreamEvent(
                    type=StreamEventType.ERROR,
                    data={
                        "error": str(e),
                        "error_type": type(e).__name__
                    },
                    timestamp=time.time(),
                    event_id=f"{stream_id}_error"
                )
                yield error_event
    
    def format_sse_event(self, event: StreamEvent) -> str:
        """Format a stream event as Server-Sent Event."""
        event_data = {
            "type": event.type.value,
            "data": event.data,
            "timestamp": event.timestamp,
            "event_id": event.event_id,
            "metadata": event.metadata
        }
        
        return f"data: {json.dumps(event_data)}\n\n"
    
    async def get_stream_info(self, stream_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a stream."""
        if stream_id not in self.active_streams:
            return None
        
        stream_info = self.active_streams[stream_id].copy()
        stream_info["duration_seconds"] = time.time() - stream_info["created_at"]
        
        return stream_info
    
    async def close_stream(self, stream_id: str) -> bool:
        """Close a streaming session."""
        if stream_id in self.active_streams:
            del self.active_streams[stream_id]
            logger.info(f"Closed stream: {stream_id}")
            return True
        
        return False
    
    def list_active_streams(self) -> List[str]:
        """List all active stream IDs."""
        return list(self.active_streams.keys())


# Global streaming service instance
streaming_service = StreamingService()
