"""
Task Analyzer service for complexity scoring and model routing.
"""

import re
import logging
import time
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from decimal import Decimal

from pydantic import BaseModel, Field

from .observability import observability_service, SpanMetadata


logger = logging.getLogger(__name__)


class TaskComplexity(str, Enum):
    """Task complexity levels."""
    VERY_EASY = "very_easy"
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"
    VERY_HARD = "very_hard"


@dataclass
class ComplexityFeatures:
    """Features used for complexity analysis."""
    text_length: int
    code_blocks: int
    math_expressions: int
    question_marks: int
    technical_terms: int
    multi_step_indicators: int
    reasoning_keywords: int
    domain_complexity: float


class TaskAnalysisResult(BaseModel):
    """Result of task complexity analysis."""
    
    task_class: TaskComplexity
    score: float = Field(ge=0.0, le=1.0)
    reason: str
    features: Dict[str, float]
    confidence: float = Field(ge=0.0, le=1.0)


class ComplexityNet:
    """
    Simplified complexity analyzer that mimics a fine-tuned model.
    In production, this would be replaced with an actual fine-tuned Llama-3-8B-Instruct model.
    """
    
    def __init__(self):
        self.technical_terms = {
            'algorithm', 'optimization', 'machine learning', 'neural network', 'database',
            'api', 'microservice', 'kubernetes', 'docker', 'sql', 'nosql', 'blockchain',
            'cryptography', 'security', 'authentication', 'authorization', 'scalability',
            'performance', 'latency', 'throughput', 'concurrency', 'parallelism',
            'distributed', 'architecture', 'design pattern', 'refactor', 'debugging',
            'testing', 'unit test', 'integration', 'deployment', 'ci/cd', 'devops'
        }
        
        self.reasoning_keywords = {
            'analyze', 'compare', 'evaluate', 'synthesize', 'optimize', 'design',
            'implement', 'architect', 'troubleshoot', 'debug', 'refactor',
            'explain why', 'how does', 'what if', 'pros and cons', 'trade-offs',
            'best practices', 'recommendations', 'strategy', 'approach'
        }
        
        self.multi_step_indicators = {
            'first', 'second', 'third', 'then', 'next', 'after', 'finally',
            'step by step', 'phase', 'stage', 'process', 'workflow',
            'and then', 'followed by', 'subsequently', 'meanwhile'
        }
    
    def extract_features(self, text: str) -> ComplexityFeatures:
        """Extract features from text for complexity analysis."""
        text_lower = text.lower()
        
        # Basic text metrics
        text_length = len(text)
        
        # Code blocks (markdown or obvious code patterns)
        code_blocks = len(re.findall(r'```[\s\S]*?```|`[^`]+`', text))
        code_blocks += len(re.findall(r'\b(def |class |import |from |if __name__|function\s*\()', text))
        
        # Math expressions
        math_expressions = len(re.findall(r'[+\-*/=<>]+|\b(sum|average|calculate|formula|equation)\b', text_lower))
        
        # Question complexity
        question_marks = text.count('?')
        
        # Technical terms
        technical_terms = sum(1 for term in self.technical_terms if term in text_lower)
        
        # Multi-step indicators
        multi_step_indicators = sum(1 for indicator in self.multi_step_indicators if indicator in text_lower)
        
        # Reasoning keywords
        reasoning_keywords = sum(1 for keyword in self.reasoning_keywords if keyword in text_lower)
        
        # Domain complexity (heuristic based on content)
        domain_complexity = self._calculate_domain_complexity(text_lower)
        
        return ComplexityFeatures(
            text_length=text_length,
            code_blocks=code_blocks,
            math_expressions=math_expressions,
            question_marks=question_marks,
            technical_terms=technical_terms,
            multi_step_indicators=multi_step_indicators,
            reasoning_keywords=reasoning_keywords,
            domain_complexity=domain_complexity
        )
    
    def _calculate_domain_complexity(self, text: str) -> float:
        """Calculate domain complexity based on content analysis."""
        complexity_score = 0.0
        
        # Programming/technical content
        if any(term in text for term in ['code', 'programming', 'software', 'development']):
            complexity_score += 0.3
        
        # Data science/ML content
        if any(term in text for term in ['machine learning', 'data science', 'model', 'training']):
            complexity_score += 0.4
        
        # System design/architecture
        if any(term in text for term in ['architecture', 'system design', 'scalability', 'distributed']):
            complexity_score += 0.5
        
        # Research/academic content
        if any(term in text for term in ['research', 'paper', 'study', 'analysis', 'hypothesis']):
            complexity_score += 0.3
        
        return min(complexity_score, 1.0)
    
    def analyze(self, text: str) -> TaskAnalysisResult:
        """Analyze task complexity and return classification."""
        start_time = time.time()

        try:
            features = self.extract_features(text)

            # Calculate complexity score using weighted features
            score = self._calculate_complexity_score(features)

            # Classify based on score thresholds
            task_class = self._classify_complexity(score)

            # Generate explanation
            reason = self._generate_reason(features, task_class)

            # Calculate confidence based on feature clarity
            confidence = self._calculate_confidence(features, score)

            result = TaskAnalysisResult(
                task_class=task_class,
                score=score,
                reason=reason,
                features={
                    'text_length': features.text_length,
                    'code_blocks': features.code_blocks,
                    'math_expressions': features.math_expressions,
                    'technical_terms': features.technical_terms,
                    'multi_step_indicators': features.multi_step_indicators,
                    'reasoning_keywords': features.reasoning_keywords,
                    'domain_complexity': features.domain_complexity
                },
                confidence=confidence
            )

            # Log the analysis with observability
            duration_ms = (time.time() - start_time) * 1000
            observability_service.log_task_analysis(
                task_text=text,
                complexity_result={
                    "task_class": task_class.value,
                    "score": score,
                    "reason": reason,
                    "confidence": confidence
                },
                duration_ms=duration_ms,
                features_count=len(result.features)
            )

            return result

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(f"Error in task analysis: {e}")

            # Log the error
            observability_service.log_error(
                operation_name="task_analysis",
                error=e,
                context={
                    "text_length": len(text),
                    "duration_ms": duration_ms
                }
            )

            # Return default classification on error
            return TaskAnalysisResult(
                task_class=TaskComplexity.MEDIUM,
                score=0.5,
                reason=f"Analysis failed: {str(e)}",
                features={},
                confidence=0.0
            )
    
    def _calculate_complexity_score(self, features: ComplexityFeatures) -> float:
        """Calculate complexity score from features."""
        # Normalize features
        length_score = min(features.text_length / 1000, 1.0) * 0.15
        code_score = min(features.code_blocks / 3, 1.0) * 0.25
        math_score = min(features.math_expressions / 5, 1.0) * 0.15
        technical_score = min(features.technical_terms / 10, 1.0) * 0.20
        multi_step_score = min(features.multi_step_indicators / 5, 1.0) * 0.15
        reasoning_score = min(features.reasoning_keywords / 5, 1.0) * 0.10
        
        # Combine scores
        base_score = (
            length_score + code_score + math_score + 
            technical_score + multi_step_score + reasoning_score
        )
        
        # Add domain complexity bonus
        final_score = min(base_score + features.domain_complexity * 0.3, 1.0)
        
        return final_score
    
    def _classify_complexity(self, score: float) -> TaskComplexity:
        """Classify complexity based on score."""
        if score < 0.2:
            return TaskComplexity.VERY_EASY
        elif score < 0.4:
            return TaskComplexity.EASY
        elif score < 0.6:
            return TaskComplexity.MEDIUM
        elif score < 0.8:
            return TaskComplexity.HARD
        else:
            return TaskComplexity.VERY_HARD
    
    def _generate_reason(self, features: ComplexityFeatures, task_class: TaskComplexity) -> str:
        """Generate human-readable reason for classification."""
        reasons = []
        
        if features.code_blocks > 0:
            reasons.append(f"contains {features.code_blocks} code block(s)")
        
        if features.technical_terms > 3:
            reasons.append(f"uses {features.technical_terms} technical terms")
        
        if features.multi_step_indicators > 2:
            reasons.append("requires multi-step processing")
        
        if features.reasoning_keywords > 2:
            reasons.append("involves complex reasoning")
        
        if features.domain_complexity > 0.5:
            reasons.append("high domain complexity")
        
        if features.text_length > 500:
            reasons.append("lengthy input text")
        
        if not reasons:
            reasons.append("simple query with basic requirements")
        
        return f"{task_class.value} task: {', '.join(reasons)}"
    
    def _calculate_confidence(self, features: ComplexityFeatures, score: float) -> float:
        """Calculate confidence in the classification."""
        # Higher confidence for clear indicators
        confidence = 0.5  # Base confidence
        
        if features.code_blocks > 0:
            confidence += 0.2
        
        if features.technical_terms > 2:
            confidence += 0.15
        
        if features.multi_step_indicators > 1:
            confidence += 0.1
        
        if features.domain_complexity > 0.3:
            confidence += 0.05
        
        return min(confidence, 1.0)


# Global instance
complexity_analyzer = ComplexityNet()
