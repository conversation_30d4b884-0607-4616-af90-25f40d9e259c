"""
Workflow automation engine for complex multi-step task execution.
"""

import logging
import time
import asyncio
import uuid
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from .observability import observability_service, SpanMetadata

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class StepStatus(Enum):
    """Individual step execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"


@dataclass
class WorkflowStep:
    """Individual step in a workflow."""
    id: str
    name: str
    action: str  # Function name or action type
    parameters: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)  # Step IDs this step depends on
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 300
    status: StepStatus = StepStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time_ms: float = 0


@dataclass
class Workflow:
    """Workflow definition and execution state."""
    id: str
    name: str
    description: str
    steps: List[WorkflowStep] = field(default_factory=list)
    status: WorkflowStatus = WorkflowStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_execution_time_ms: float = 0
    context: Dict[str, Any] = field(default_factory=dict)  # Shared data between steps
    metadata: Dict[str, Any] = field(default_factory=dict)


class WorkflowEngine:
    """Engine for executing complex multi-step workflows."""
    
    def __init__(self):
        self.workflows: Dict[str, Workflow] = {}
        self.step_handlers: Dict[str, Callable] = {}
        self.running_workflows: Dict[str, asyncio.Task] = {}
        
        # Register built-in step handlers
        self._register_builtin_handlers()
    
    def _register_builtin_handlers(self):
        """Register built-in step handlers."""
        self.step_handlers.update({
            "delay": self._handle_delay,
            "log": self._handle_log,
            "http_request": self._handle_http_request,
            "data_transform": self._handle_data_transform,
            "conditional": self._handle_conditional,
            "parallel": self._handle_parallel,
            "agent_call": self._handle_agent_call
        })
    
    def register_step_handler(self, action_name: str, handler: Callable):
        """Register a custom step handler."""
        self.step_handlers[action_name] = handler
        logger.info(f"Registered step handler: {action_name}")
    
    def create_workflow(
        self,
        name: str,
        description: str,
        steps: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new workflow."""
        workflow_id = str(uuid.uuid4())
        
        # Convert step dictionaries to WorkflowStep objects
        workflow_steps = []
        for i, step_data in enumerate(steps):
            step = WorkflowStep(
                id=step_data.get("id", f"step_{i+1}"),
                name=step_data.get("name", f"Step {i+1}"),
                action=step_data["action"],
                parameters=step_data.get("parameters", {}),
                dependencies=step_data.get("dependencies", []),
                max_retries=step_data.get("max_retries", 3),
                timeout_seconds=step_data.get("timeout_seconds", 300)
            )
            workflow_steps.append(step)
        
        workflow = Workflow(
            id=workflow_id,
            name=name,
            description=description,
            steps=workflow_steps,
            metadata=metadata or {}
        )
        
        self.workflows[workflow_id] = workflow
        logger.info(f"Created workflow: {name} ({workflow_id})")
        
        return workflow_id
    
    async def execute_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Execute a workflow asynchronously."""
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow not found: {workflow_id}")
        
        workflow = self.workflows[workflow_id]
        
        if workflow.status == WorkflowStatus.RUNNING:
            raise ValueError(f"Workflow already running: {workflow_id}")
        
        # Start workflow execution
        task = asyncio.create_task(self._execute_workflow_internal(workflow))
        self.running_workflows[workflow_id] = task
        
        try:
            result = await task
            return result
        finally:
            if workflow_id in self.running_workflows:
                del self.running_workflows[workflow_id]
    
    async def _execute_workflow_internal(self, workflow: Workflow) -> Dict[str, Any]:
        """Internal workflow execution logic."""
        start_time = time.time()
        
        metadata_span = SpanMetadata(
            operation_type="workflow_execution",
            workflow_id=workflow.id,
            workflow_name=workflow.name
        )
        
        async with observability_service.trace_operation("execute_workflow", metadata_span) as span:
            try:
                workflow.status = WorkflowStatus.RUNNING
                workflow.started_at = datetime.now()
                
                logger.info(f"Starting workflow execution: {workflow.name} ({workflow.id})")
                
                # Execute steps in dependency order
                completed_steps = set()
                
                while len(completed_steps) < len(workflow.steps):
                    # Find steps that can be executed (dependencies satisfied)
                    ready_steps = [
                        step for step in workflow.steps
                        if (step.status == StepStatus.PENDING and
                            all(dep in completed_steps for dep in step.dependencies))
                    ]
                    
                    if not ready_steps:
                        # Check if we're stuck (circular dependencies or all remaining steps failed)
                        pending_steps = [s for s in workflow.steps if s.status == StepStatus.PENDING]
                        if pending_steps:
                            error_msg = f"Workflow stuck: no ready steps found. Pending: {[s.id for s in pending_steps]}"
                            logger.error(error_msg)
                            workflow.status = WorkflowStatus.FAILED
                            break
                        else:
                            break
                    
                    # Execute ready steps (can be parallel if no dependencies between them)
                    step_tasks = []
                    for step in ready_steps:
                        task = asyncio.create_task(self._execute_step(workflow, step))
                        step_tasks.append(task)
                    
                    # Wait for all ready steps to complete
                    step_results = await asyncio.gather(*step_tasks, return_exceptions=True)
                    
                    # Process results
                    for step, result in zip(ready_steps, step_results):
                        if isinstance(result, Exception):
                            logger.error(f"Step {step.id} failed with exception: {result}")
                            step.status = StepStatus.FAILED
                            step.error = str(result)
                        elif step.status == StepStatus.COMPLETED:
                            completed_steps.add(step.id)
                        elif step.status == StepStatus.FAILED:
                            # Check if workflow should continue or fail
                            if step.parameters.get("continue_on_failure", False):
                                completed_steps.add(step.id)  # Treat as completed for dependency purposes
                            else:
                                workflow.status = WorkflowStatus.FAILED
                                break
                
                # Determine final workflow status
                if workflow.status == WorkflowStatus.RUNNING:
                    failed_steps = [s for s in workflow.steps if s.status == StepStatus.FAILED]
                    if failed_steps:
                        workflow.status = WorkflowStatus.FAILED
                    else:
                        workflow.status = WorkflowStatus.COMPLETED
                
                workflow.completed_at = datetime.now()
                workflow.total_execution_time_ms = (time.time() - start_time) * 1000
                
                # Prepare result
                result = {
                    "workflow_id": workflow.id,
                    "status": workflow.status.value,
                    "execution_time_ms": workflow.total_execution_time_ms,
                    "steps_completed": len([s for s in workflow.steps if s.status == StepStatus.COMPLETED]),
                    "steps_failed": len([s for s in workflow.steps if s.status == StepStatus.FAILED]),
                    "total_steps": len(workflow.steps),
                    "context": workflow.context,
                    "step_results": {
                        step.id: {
                            "status": step.status.value,
                            "result": step.result,
                            "error": step.error,
                            "execution_time_ms": step.execution_time_ms
                        }
                        for step in workflow.steps
                    }
                }
                
                # Log workflow completion
                observability_service.log_task_analysis(
                    task_text=f"Workflow execution: {workflow.name}",
                    complexity_result={
                        "status": workflow.status.value,
                        "total_steps": len(workflow.steps),
                        "completed_steps": result["steps_completed"],
                        "failed_steps": result["steps_failed"]
                    },
                    duration_ms=workflow.total_execution_time_ms,
                    features_count=len(workflow.steps)
                )
                
                logger.info(f"Workflow completed: {workflow.name} - Status: {workflow.status.value}")
                
                return result
                
            except Exception as e:
                workflow.status = WorkflowStatus.FAILED
                workflow.completed_at = datetime.now()
                workflow.total_execution_time_ms = (time.time() - start_time) * 1000
                
                logger.error(f"Workflow execution failed: {workflow.name} - {e}")
                
                observability_service.log_error(
                    operation_name="execute_workflow",
                    error=e,
                    context={
                        "workflow_id": workflow.id,
                        "workflow_name": workflow.name,
                        "duration_ms": workflow.total_execution_time_ms
                    }
                )
                
                raise
    
    async def _execute_step(self, workflow: Workflow, step: WorkflowStep) -> Any:
        """Execute an individual workflow step."""
        step_start_time = time.time()
        step.status = StepStatus.RUNNING
        step.start_time = datetime.now()
        
        logger.info(f"Executing step: {step.name} ({step.id}) in workflow {workflow.id}")
        
        try:
            # Get step handler
            handler = self.step_handlers.get(step.action)
            if not handler:
                raise ValueError(f"No handler found for action: {step.action}")
            
            # Execute step with timeout
            result = await asyncio.wait_for(
                handler(workflow, step),
                timeout=step.timeout_seconds
            )
            
            step.result = result
            step.status = StepStatus.COMPLETED
            step.end_time = datetime.now()
            step.execution_time_ms = (time.time() - step_start_time) * 1000
            
            logger.info(f"Step completed: {step.name} ({step.id})")
            
            return result
            
        except asyncio.TimeoutError:
            step.status = StepStatus.FAILED
            step.error = f"Step timed out after {step.timeout_seconds} seconds"
            step.end_time = datetime.now()
            step.execution_time_ms = (time.time() - step_start_time) * 1000
            
            logger.error(f"Step timed out: {step.name} ({step.id})")
            raise
            
        except Exception as e:
            step.status = StepStatus.FAILED
            step.error = str(e)
            step.end_time = datetime.now()
            step.execution_time_ms = (time.time() - step_start_time) * 1000
            
            logger.error(f"Step failed: {step.name} ({step.id}) - {e}")
            
            # Retry logic
            if step.retry_count < step.max_retries:
                step.retry_count += 1
                step.status = StepStatus.RETRYING
                logger.info(f"Retrying step: {step.name} ({step.id}) - Attempt {step.retry_count}")
                
                # Wait before retry
                await asyncio.sleep(min(2 ** step.retry_count, 30))  # Exponential backoff, max 30s
                
                return await self._execute_step(workflow, step)
            
            raise

    # Built-in step handlers

    async def _handle_delay(self, workflow: Workflow, step: WorkflowStep) -> str:
        """Handle delay step."""
        delay_seconds = step.parameters.get("seconds", 1)
        await asyncio.sleep(delay_seconds)
        return f"Delayed for {delay_seconds} seconds"

    async def _handle_log(self, workflow: Workflow, step: WorkflowStep) -> str:
        """Handle log step."""
        message = step.parameters.get("message", "Log step executed")
        level = step.parameters.get("level", "info").lower()

        # Format message with context variables
        formatted_message = message.format(**workflow.context)

        if level == "debug":
            logger.debug(formatted_message)
        elif level == "warning":
            logger.warning(formatted_message)
        elif level == "error":
            logger.error(formatted_message)
        else:
            logger.info(formatted_message)

        return formatted_message

    async def _handle_http_request(self, workflow: Workflow, step: WorkflowStep) -> Dict[str, Any]:
        """Handle HTTP request step."""
        try:
            import httpx

            url = step.parameters.get("url")
            method = step.parameters.get("method", "GET").upper()
            headers = step.parameters.get("headers", {})
            data = step.parameters.get("data")
            json_data = step.parameters.get("json")
            timeout = step.parameters.get("timeout", 30)

            if not url:
                raise ValueError("URL is required for HTTP request")

            # Format URL with context variables
            formatted_url = url.format(**workflow.context)

            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=method,
                    url=formatted_url,
                    headers=headers,
                    data=data,
                    json=json_data,
                    timeout=timeout
                )

                result = {
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "text": response.text,
                    "url": str(response.url)
                }

                # Try to parse JSON response
                try:
                    result["json"] = response.json()
                except:
                    pass

                # Store response in workflow context if specified
                context_key = step.parameters.get("store_in_context")
                if context_key:
                    workflow.context[context_key] = result

                return result

        except ImportError:
            raise ValueError("httpx library not available for HTTP requests")

    async def _handle_data_transform(self, workflow: Workflow, step: WorkflowStep) -> Any:
        """Handle data transformation step."""
        source_key = step.parameters.get("source_key")
        target_key = step.parameters.get("target_key")
        transform_type = step.parameters.get("transform_type", "copy")

        if not source_key or source_key not in workflow.context:
            raise ValueError(f"Source key '{source_key}' not found in workflow context")

        source_data = workflow.context[source_key]

        # Apply transformation
        if transform_type == "copy":
            result = source_data
        elif transform_type == "json_extract":
            json_path = step.parameters.get("json_path")
            if not json_path:
                raise ValueError("json_path required for json_extract transform")

            # Simple JSON path extraction (supports dot notation)
            result = source_data
            for key in json_path.split("."):
                if isinstance(result, dict) and key in result:
                    result = result[key]
                else:
                    raise ValueError(f"JSON path '{json_path}' not found in data")

        elif transform_type == "format_string":
            template = step.parameters.get("template", "{value}")
            result = template.format(value=source_data, **workflow.context)

        else:
            raise ValueError(f"Unknown transform type: {transform_type}")

        # Store result in context
        if target_key:
            workflow.context[target_key] = result

        return result

    async def _handle_conditional(self, workflow: Workflow, step: WorkflowStep) -> str:
        """Handle conditional step."""
        condition = step.parameters.get("condition")
        condition_type = step.parameters.get("condition_type", "equals")

        if not condition:
            raise ValueError("Condition is required for conditional step")

        # Evaluate condition
        if condition_type == "equals":
            left = step.parameters.get("left")
            right = step.parameters.get("right")

            # Format values with context
            if isinstance(left, str):
                left = left.format(**workflow.context)
            if isinstance(right, str):
                right = right.format(**workflow.context)

            condition_result = left == right

        elif condition_type == "exists":
            key = step.parameters.get("key")
            condition_result = key in workflow.context

        else:
            raise ValueError(f"Unknown condition type: {condition_type}")

        # Store condition result in context
        result_key = step.parameters.get("result_key", f"{step.id}_result")
        workflow.context[result_key] = condition_result

        return f"Condition evaluated to: {condition_result}"

    async def _handle_parallel(self, workflow: Workflow, step: WorkflowStep) -> Dict[str, Any]:
        """Handle parallel execution step."""
        parallel_steps = step.parameters.get("steps", [])
        max_workers = step.parameters.get("max_workers", 5)

        if not parallel_steps:
            return {"status": "completed", "results": []}

        # Create WorkflowStep objects for parallel execution
        steps_to_execute = []
        for i, step_config in enumerate(parallel_steps):
            parallel_step = WorkflowStep(
                id=f"{step.id}_parallel_{i}",
                name=step_config.get("name", f"Parallel Step {i}"),
                action=step_config.get("action", "log"),
                parameters=step_config.get("parameters", {}),
                dependencies=[]
            )
            steps_to_execute.append(parallel_step)

        # Execute steps in parallel
        import asyncio
        from concurrent.futures import ThreadPoolExecutor

        async def execute_step(parallel_step):
            try:
                return await self._execute_step(workflow, parallel_step)
            except Exception as e:
                logger.error(f"Error in parallel step {parallel_step.id}: {e}")
                return {"error": str(e), "step_id": parallel_step.id}

        # Run all steps concurrently
        results = await asyncio.gather(
            *[execute_step(step) for step in steps_to_execute],
            return_exceptions=True
        )

        # Store results in workflow context
        result_key = step.parameters.get("result_key", f"{step.id}_results")
        workflow.context[result_key] = results

        return {
            "status": "completed",
            "results": results,
            "parallel_steps_count": len(steps_to_execute)
        }

    async def _handle_agent_call(self, workflow: Workflow, step: WorkflowStep) -> Dict[str, Any]:
        """Handle agent call step."""
        agent_type = step.parameters.get("agent_type")
        method = step.parameters.get("method", "chat")
        parameters = step.parameters.get("parameters", {})

        if not agent_type:
            raise ValueError("agent_type is required for agent_call step")

        # Format parameters with context variables
        formatted_params = {}
        for key, value in parameters.items():
            if isinstance(value, str):
                formatted_params[key] = value.format(**workflow.context)
            else:
                formatted_params[key] = value

        # This would integrate with the actual agent system
        # For now, return a placeholder
        result = {
            "agent_type": agent_type,
            "method": method,
            "parameters": formatted_params,
            "result": f"[Agent call to {agent_type}.{method} not implemented]"
        }

        # Store result in context if specified
        context_key = step.parameters.get("store_in_context")
        if context_key:
            workflow.context[context_key] = result

        return result


# Global workflow engine instance
workflow_engine = WorkflowEngine()
