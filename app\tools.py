"""
Custom tools for the Pydantic AI agent.
"""

import json
import math
import random
from datetime import datetime
from typing import Any, Dict

from pydantic import BaseModel, Field

from .models import CalculationResult, WeatherInfo


class CalculatorTool:
    """Calculator tool for mathematical operations."""
    
    @staticmethod
    def calculate(expression: str) -> CalculationResult:
        """
        Perform mathematical calculations.
        
        Args:
            expression: Mathematical expression to evaluate (e.g., "2 + 2", "sqrt(16)")
        
        Returns:
            CalculationResult with the result or error
        """
        try:
            # Replace common math functions
            safe_expression = expression.replace("sqrt", "math.sqrt")
            safe_expression = safe_expression.replace("sin", "math.sin")
            safe_expression = safe_expression.replace("cos", "math.cos")
            safe_expression = safe_expression.replace("tan", "math.tan")
            safe_expression = safe_expression.replace("log", "math.log")
            safe_expression = safe_expression.replace("pi", "math.pi")
            safe_expression = safe_expression.replace("e", "math.e")
            
            # Evaluate the expression safely
            allowed_names = {
                "math": math,
                "__builtins__": {},
            }
            
            result = eval(safe_expression, allowed_names)
            
            return CalculationResult(
                expression=expression,
                result=result
            )
        except Exception as e:
            return CalculationResult(
                expression=expression,
                result="Error",
                error=str(e)
            )


class WeatherTool:
    """Mock weather tool for demonstration."""
    
    @staticmethod
    def get_weather(location: str) -> WeatherInfo:
        """
        Get weather information for a location.
        
        Args:
            location: Location to get weather for
        
        Returns:
            WeatherInfo with mock weather data
        """
        # Mock weather data for demonstration
        weather_conditions = [
            "Sunny", "Cloudy", "Partly Cloudy", "Rainy", "Stormy", "Snowy"
        ]
        
        return WeatherInfo(
            location=location,
            temperature=round(random.uniform(-10, 35), 1),
            description=random.choice(weather_conditions),
            humidity=random.randint(30, 90),
            wind_speed=round(random.uniform(0, 25), 1)
        )


class UtilityTool:
    """Utility functions tool."""
    
    @staticmethod
    def generate_uuid() -> str:
        """Generate a random UUID."""
        import uuid
        return str(uuid.uuid4())
    
    @staticmethod
    def get_current_time() -> str:
        """Get current timestamp."""
        return datetime.now().isoformat()
    
    @staticmethod
    def encode_base64(text: str) -> str:
        """Encode text to base64."""
        import base64
        return base64.b64encode(text.encode()).decode()
    
    @staticmethod
    def decode_base64(encoded: str) -> str:
        """Decode base64 text."""
        import base64
        try:
            return base64.b64decode(encoded.encode()).decode()
        except Exception as e:
            return f"Error decoding: {str(e)}"
    
    @staticmethod
    def generate_password(length: int = 12) -> str:
        """Generate a random password."""
        import string
        import secrets
        
        if length < 4:
            length = 4
        if length > 50:
            length = 50
            
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        return password


class TextTool:
    """Text processing tool."""
    
    @staticmethod
    def count_words(text: str) -> Dict[str, Any]:
        """Count words, characters, and lines in text."""
        words = len(text.split())
        characters = len(text)
        characters_no_spaces = len(text.replace(" ", ""))
        lines = len(text.split("\n"))
        
        return {
            "words": words,
            "characters": characters,
            "characters_no_spaces": characters_no_spaces,
            "lines": lines,
            "text_length": len(text)
        }
    
    @staticmethod
    def reverse_text(text: str) -> str:
        """Reverse the given text."""
        return text[::-1]
    
    @staticmethod
    def to_uppercase(text: str) -> str:
        """Convert text to uppercase."""
        return text.upper()
    
    @staticmethod
    def to_lowercase(text: str) -> str:
        """Convert text to lowercase."""
        return text.lower()
    
    @staticmethod
    def extract_emails(text: str) -> list:
        """Extract email addresses from text."""
        import re
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return re.findall(email_pattern, text)
