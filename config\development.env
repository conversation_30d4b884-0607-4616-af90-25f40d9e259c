# Development Environment Configuration

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# Application
APP_NAME=AI Brain Foundation (Development)
HOST=0.0.0.0
PORT=8000

# Database (use local PostgreSQL or Supabase)
# DATABASE_URL=postgresql://postgres:password@localhost:5432/ai_brain_dev
# Or use Supabase
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Redis (optional for development)
REDIS_URL=redis://localhost:6379/0

# Security (use development keys)
JWT_SECRET_KEY=dev-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# AI Providers (add your API keys)
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
GOOGLE_API_KEY=your-google-key
GROQ_API_KEY=your-groq-key
OPENROUTER_API_KEY=your-openrouter-key
DEEPSEEK_API_KEY=your-deepseek-key

# AI Settings
DEFAULT_MODEL=gpt-4o-mini
MAX_TOKENS=1000
TEMPERATURE=0.7

# Feature Flags
FEATURE_VECTOR_SEARCH=true
FEATURE_RAG=true
FEATURE_MULTIMODAL=true
FEATURE_WORKFLOWS=true
FEATURE_ANALYTICS=true
FEATURE_CACHING=true
FEATURE_RATE_LIMITING=false

# Monitoring
MONITORING_ENABLED=true
STRUCTURED_LOGGING=true
TRACE_SAMPLING_RATE=1.0

# Rate Limiting (disabled for development)
RATE_LIMIT_ENABLED=false
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60

# CORS (permissive for development)
CORS_ORIGINS=*
CORS_METHODS=*
CORS_HEADERS=*
