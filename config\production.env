# Production Environment Configuration

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Application
APP_NAME=AI Brain Foundation
HOST=0.0.0.0
PORT=8000

# Database (use production database)
DATABASE_URL=${DATABASE_URL}
# Or Supabase production
SUPABASE_URL=${SUPABASE_URL}
SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}

# Redis (production cache)
REDIS_URL=${REDIS_URL}

# Security (use strong production keys)
JWT_SECRET_KEY=${JWT_SECRET_KEY}
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# AI Providers
OPENAI_API_KEY=${OPENAI_API_KEY}
ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
GOOGLE_API_KEY=${GOOGLE_API_KEY}
GROQ_API_KEY=${GROQ_API_KEY}
OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}

# AI Settings
DEFAULT_MODEL=gpt-4o-mini
MAX_TOKENS=1000
TEMPERATURE=0.7

# Feature Flags
FEATURE_VECTOR_SEARCH=true
FEATURE_RAG=true
FEATURE_MULTIMODAL=true
FEATURE_WORKFLOWS=true
FEATURE_ANALYTICS=true
FEATURE_CACHING=true
FEATURE_RATE_LIMITING=true

# Monitoring
MONITORING_ENABLED=true
STRUCTURED_LOGGING=true
TRACE_SAMPLING_RATE=0.1
SENTRY_DSN=${SENTRY_DSN}

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# CORS (restrictive for production)
CORS_ORIGINS=${CORS_ORIGINS}
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-API-Key

# Database Connection Pool
DB_MIN_CONNECTIONS=5
DB_MAX_CONNECTIONS=20
DB_COMMAND_TIMEOUT=60

# Redis Connection Pool
REDIS_MAX_CONNECTIONS=20
