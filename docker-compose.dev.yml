version: '3.8'

services:
  # PostgreSQL Database for metadata, auth, audit
  postgres:
    image: postgres:15-alpine
    container_name: ai_backend_postgres
    environment:
      POSTGRES_DB: ai_backend
      POSTGRES_USER: ai_user
      POSTGRES_PASSWORD: ai_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_user -d ai_backend"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for queuing and caching
  redis:
    image: redis:7-alpine
    container_name: ai_backend_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qdrant Vector Database for similarity search
  qdrant:
    image: qdrant/qdrant:latest
    container_name: ai_backend_qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Backend Application (optional for full stack development)
  ai_backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ai_backend_app
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************************/ai_backend
      - REDIS_URL=redis://redis:6379
      - QDRANT_URL=http://qdrant:6333
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/__pycache__
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    profiles:
      - full-stack

volumes:
  postgres_data:
  redis_data:
  qdrant_data:

networks:
  default:
    name: ai_backend_network
