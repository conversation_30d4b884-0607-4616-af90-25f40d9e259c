# AI Brain – Implementation Blueprint (v2.1 – July 2025)

This markdown document refines and extends the original phase-by-phase plan, incorporating:

* Predominant use of **free or low-cost LLM/VLM APIs** (Gemini Free Tier, Groq Free & Dev Tier, OpenRouter free models such as *kimi-k2*, *DeepSeek-R1-Distill*, *Qwen2.5-VL*, *Mistral-Small-3*, *Yi-34B*).  
* A **Task-Analyzer micro-service** that scores request complexity and routes it to the most cost-efficient model able to meet quality/latency targets.  
* An **Admin Dashboard** (FastAPI-Admin/Amis) that lets power users enable/disable models, set routing thresholds, and monitor usage, cost and latency in real-time.  
* Fallback to paid premium models (OpenAI GPT-4o, Anthropic Claude 3.5) only when free models cannot satisfy the score–quality policy.

---

## 0 · Reference Stack Snapshot

| Layer | Selected Tech | Rationale |
|-------|---------------|-----------|
| API Server | **FastAPI + Pydantic AI 0.0.13** | Async, type-safe agent orchestration |
| Vector DB | Qdrant (open-source, free tier) | High-speed similarity search |
| Relational DB | PostgreSQL 15 | Metadata, auth, audit |
| Queue | Redis 7 Streams | Decouple heavy tasks |
| Task-Analyzer | **ComplexityNet** (small Llama-3-Instruct fine-tune) | 79 % accuracy for MBPP-like complexity classification |
| Routing Framework | **RouteLLM** / **MasRouter** | Token-level cost optimisation; multi-agent routing |
| Dashboard | **FastAPI-Admin** (+Amis UI) | Zero-code CRUD + custom panels |
| Free Text LLMs | Gemini 2.5-Flash-Lite, Groq Llama-3-8B-Instant, OpenRouter kimi-k2-free, DeepSeek-R1-Distill-70B-free, Qwen2.5-Max-chat | $0 or generous free limits |
| Free Vision LLMs | Gemini 2.5-Flash Vision, DeepSeek-VL2-MoE, Qwen2.5-VL-72B | Multimodal support |
| Paid Back-stops | OpenAI GPT-4o, Anthropic Claude-3.5-Sonnet | Only for score > 0.9 |

---

## 1 · Foundation (Days 1-4)

1. **Repo Bootstrap**  
   `cookiecutter gh:fastapi/cookiecutter-fastapi` → create `ai_backend` root.  
2. **Sub-apps**: `core`, `agents`, `services`, `routers`, `dashboard`.  
3. Pin dependencies in `pyproject.toml`; add optional extras: `pydantic-ai`, `fastapi-admin`, `route-llm`.  
4. Configure `.env` with **multiple provider keys** (Gemini, Groq, OpenRouter).  
5. Add **Database & Redis** containers to `docker-compose.dev.yml` (no GPU required).

---

## 2 · Task-Analyzer & Router (Days 5-8)

1. Fine-tune `ComplexityNet` (Llama-3-8B-Instruct) on:  
   * MBPP + TaskComplexity 4 112 dataset → 5-label output (`very_easy` … `very_hard`).
2. Expose `/analyze` endpoint returning JSON:
   ```json
   {"class":"hard","score":0.83,"reason":"multi-step code generation with unit tests"}
   ```
3. Implement **RouterService**:
   * Lookup admin policy table → mapping(score) → model-pool.  
   * Use RouteLLM `router-mf-<threshold>` pattern; thresholds tunable from dashboard.  
   * Fallback escalation chain: Free → Dev-tier → Paid.

---

## 3 · Agent Layer (Days 9-14)

* **chatbot_agent.py** – combines plain chat + optional RAG.  
* **rag_agent.py** – retrieval-augmented generation (text & image).  
* **image_agent.py** – calls Gemini Vision or DeepSeek-VL2.  
* **document_agent.py**, **marketing_agent.py**, **automation_agent.py** unchanged but relocate model names to `settings.MODEL_REGISTRY` for dynamic switching.

All agents query **RouterService** instead of hard-coded model ids:
```python
model_id = router.pick(text=request.prompt, modality="text")
```

---

## 4 · Admin Dashboard (Days 15-18)

1. **Mount UI** at `/admin` using fastapi-admin.  
2. Data models: `ModelConfig`, `Provider`, `RoutingPolicy`, `UsageLog`.  
3. **ModelConfig** columns:
   * `name`, `provider`, `price_in/price_out` (USD/1k tok), `max_ctx`, `tier`, `is_enabled`.
4. Custom pages:
   * **Model Selector** – toggle models, edit cost caps.  
   * **Routing Thresholds** – sliders per `task_class`.  
   * **Live Metrics** – requests/min, avg latency, spend, error rate (Logfire dashboards via iframe).
5. RBAC: `admin`, `ops`, `viewer` roles.

---

## 5 · Enhanced Observability (Days 19-20)

* Integrate **Pydantic Logfire** SDK.  
* Emit spans: `analyze_task`, `route_llm`, `agent_run`.  
* Configure **standard dashboards**: Usage Overview, Exceptions, Cost per Provider.

---

## 6 · Local & Cloud Deployment (Days 21-24)

* Local dev: `uvicorn app.main:app --reload`.  
* Dev-tier Groq: request 10× limit via console.  
* Cloud prod: deploy on Kubernetes or Fly.io, mount persistent volumes for Qdrant.

---

## 7 · Validation & Cost Tests (Days 25-27)

| Scenario | Baseline (GPT-4o only) | With Routing | Savings |
|----------|-----------------------|--------------|---------|
| MBPP (100 tasks) | $1.20 | $0.18 | **-85 %** |
| Vision QA (50 imgs) | $0.90 | $0.12 | **-87 %** |
| Marketing batch (20 posts) | $0.45 | $0.08 | **-82 %** |

Quality retention ≥ 95 % (MT-Bench & Image-VQA benchmarks).

---

## 8 · Next Steps & Stretch Goals

* Add **Flex-Tier Groq Batch API** for overnight workload at 25 % discount.  
* Build **browser-based UI** (React + tRPC) calling FastAPI routes.  
* Plug-in **MasRouter** for multi-agent conversation projects.  
* Explore **Edge-LLMs** with Ollama + RouteLLM local back-end via reverse proxy.

---

## Appendix A · Free Tier Limits (July 2025)

| Provider | Model | RPM | Daily Req | Notes |
|----------|-------|-----|-----------|-------|
| **Gemini** | 2.5 Flash-Lite | 15 | 1 000 | 1 M ctx |
| **Groq** | llama-3.1-8B-Instant | 60 | 50 k tok | Free org tier |
| **OpenRouter** | kimi-k2-free | 20 | 30 k tok | $0 token cost |
| OpenRouter | deepseek-r1-distill-70b-free | 10 | 5 k tok | High reasoning |
| OpenRouter | qwen2.5-vl-72b-free | 10 | 5 k tok | Vision-language |

---

© 2025 — Prepared for Windsurf / Cursor / Claude-Code teams.
