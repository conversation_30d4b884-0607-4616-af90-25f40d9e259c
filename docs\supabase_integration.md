# Supabase Integration Guide

This guide explains how to use the Supabase integration in the Pydantic AI Backend application. The integration provides authentication, database operations, and file storage capabilities using Supabase as the backend service.

## Table of Contents

1. [Setup and Configuration](#setup-and-configuration)
2. [Authentication](#authentication)
3. [Database Operations](#database-operations)
4. [File Storage](#file-storage)
5. [API Endpoints](#api-endpoints)
6. [Usage Examples](#usage-examples)
7. [Security Considerations](#security-considerations)
8. [Troubleshooting](#troubleshooting)

## Setup and Configuration

### 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com) and create a new project
2. Name your project "ai-brain" (or any name you prefer)
3. Wait for the project to be fully initialized

### 2. Get Your Supabase Credentials

From your Supabase dashboard:

1. Go to **Settings** → **API**
2. Copy your **Project URL**
3. Copy your **anon/public key**
4. Copy your **service_role key** (keep this secret!)

### 3. Configure Environment Variables

Update your `.env` file with your Supabase credentials:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# JWT Configuration (optional - uses Supabase's JWT by default)
JWT_SECRET=your_jwt_secret_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24
```

### 4. Set Up Database Tables

Execute the following SQL in your Supabase SQL editor to create the required tables:

```sql
-- Create conversations table for AI chat history
CREATE TABLE IF NOT EXISTS conversations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create messages table for conversation messages
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Enable Row Level Security
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Create policies for conversations
CREATE POLICY "Users can view their own conversations" ON conversations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own conversations" ON conversations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own conversations" ON conversations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own conversations" ON conversations
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for messages
CREATE POLICY "Users can view messages in their conversations" ON messages
    FOR SELECT USING (
        conversation_id IN (
            SELECT id FROM conversations WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages in their conversations" ON messages
    FOR INSERT WITH CHECK (
        conversation_id IN (
            SELECT id FROM conversations WHERE user_id = auth.uid()
        )
    );
```

### 5. Set Up Storage Buckets

1. Go to **Storage** in your Supabase dashboard
2. Create a new bucket called "files"
3. Configure the bucket policies as needed (public or private)

## Authentication

The integration provides comprehensive authentication using Supabase Auth.

### Sign Up

```python
from app.services.auth_service import get_auth_service, UserSignUp

auth_service = get_auth_service()

user_data = UserSignUp(
    email="<EMAIL>",
    password="securepassword",
    full_name="John Doe",
    metadata={"role": "user"}
)

auth_response = await auth_service.sign_up(user_data)
print(f"User ID: {auth_response.user.id}")
print(f"Access Token: {auth_response.access_token}")
```

### Sign In

```python
from app.services.auth_service import UserSignIn

credentials = UserSignIn(
    email="<EMAIL>",
    password="securepassword"
)

auth_response = await auth_service.sign_in(credentials)
access_token = auth_response.access_token
```

### Protected Routes

Use the authentication middleware to protect routes:

```python
from fastapi import Depends
from app.middleware.auth_middleware import get_current_user

@app.get("/api/protected")
async def protected_endpoint(user: UserProfile = Depends(get_current_user)):
    return {"message": f"Hello {user.email}"}
```

## Database Operations

The database service provides type-safe operations for PostgreSQL.

### Query Data

```python
from app.services.database_service import get_database_service, DatabaseQuery

db_service = get_database_service()

query = DatabaseQuery(
    table="conversations",
    select="id,title,created_at",
    filters={"user_id": user.id},
    order_by="-created_at",
    limit=10
)

results = await db_service.query(query)
```

### Insert Data

```python
from app.services.database_service import DatabaseInsert

insert = DatabaseInsert(
    table="conversations",
    data={
        "user_id": user.id,
        "title": "New Conversation",
        "metadata": {"topic": "AI"}
    }
)

results = await db_service.insert(insert)
```

### Update Data

```python
from app.services.database_service import DatabaseUpdate

update = DatabaseUpdate(
    table="conversations",
    data={"title": "Updated Title"},
    filters={"id": conversation_id, "user_id": user.id}
)

results = await db_service.update(update)
```

### Complex Filters

```python
query = DatabaseQuery(
    table="conversations",
    filters={
        "created_at": {"gte": "2024-01-01"},
        "title": {"ilike": "%AI%"},
        "user_id": user.id
    }
)
```

## File Storage

The storage service provides file upload, download, and management capabilities.

### Upload File

```python
from app.services.storage_service import get_storage_service

storage_service = get_storage_service()

# Upload from bytes
file_data = b"File content here"
file_path = await storage_service.upload_file(
    "documents/my-file.txt",
    file_data,
    content_type="text/plain"
)

# Upload from local file
file_path = await storage_service.upload_file_from_path(
    "local/path/to/file.pdf",
    "documents/uploaded-file.pdf"
)
```

### Download File

```python
file_data = await storage_service.download_file("documents/my-file.txt")
```

### List Files

```python
files = await storage_service.list_files("documents/")
for file_info in files:
    print(f"{file_info.name} - {file_info.size} bytes")
```

### Get File URLs

```python
# Public URL
public_url = await storage_service.get_public_url("documents/my-file.txt")

# Signed URL (for private files)
signed_url = await storage_service.create_signed_url(
    "documents/my-file.txt",
    expires_in=3600  # 1 hour
)
```

## API Endpoints

The integration provides REST API endpoints for all operations:

### Authentication Endpoints

- `POST /api/supabase/auth/signup` - Sign up a new user
- `POST /api/supabase/auth/signin` - Sign in a user
- `POST /api/supabase/auth/signout` - Sign out current user
- `GET /api/supabase/auth/user` - Get current user profile
- `POST /api/supabase/auth/refresh` - Refresh access token

### Database Endpoints

- `POST /api/supabase/db/query` - Execute a query
- `POST /api/supabase/db/insert` - Insert data
- `PUT /api/supabase/db/update` - Update data
- `DELETE /api/supabase/db/delete` - Delete data
- `GET /api/supabase/db/schema/{table}` - Get table schema
- `GET /api/supabase/db/count/{table}` - Count records

### Storage Endpoints

- `POST /api/supabase/storage/upload` - Upload a file
- `GET /api/supabase/storage/download/{path}` - Download a file
- `DELETE /api/supabase/storage/delete/{path}` - Delete a file
- `GET /api/supabase/storage/list` - List files
- `GET /api/supabase/storage/url/{path}` - Get file URL

### Health Check

- `GET /api/supabase/health` - Check Supabase service health

## Usage Examples

See `examples/supabase_usage.py` for comprehensive examples of all features.

To run the examples:

```bash
python examples/supabase_usage.py
```

## Security Considerations

1. **Row Level Security (RLS)**: Always enable RLS on your tables and create appropriate policies
2. **API Keys**: Keep your service role key secret and never expose it in client-side code
3. **Authentication**: Use the provided middleware to protect sensitive endpoints
4. **File Access**: Implement proper file access controls based on user permissions
5. **Input Validation**: All inputs are validated using Pydantic models

## Troubleshooting

### Common Issues

1. **"Supabase client not initialized"**
   - Check your SUPABASE_URL and SUPABASE_ANON_KEY in .env
   - Ensure the values are correct and the project is active

2. **Authentication errors**
   - Verify your JWT configuration
   - Check that the user exists and credentials are correct

3. **Database permission errors**
   - Ensure RLS policies are correctly configured
   - Check that the user has the necessary permissions

4. **Storage upload failures**
   - Verify the storage bucket exists
   - Check file size limits and permissions

### Debug Mode

Enable debug logging to see detailed error information:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Health Check

Use the health check endpoint to verify your configuration:

```bash
curl http://localhost:8000/api/supabase/health
```

## Quick Setup Script

A setup script is available to help you get started quickly:

```bash
python scripts/setup_supabase.py
```

This script will:
- Validate your Supabase configuration
- Create required database tables
- Set up storage buckets
- Test the connection

## Next Steps

1. Customize the database schema for your specific use case
2. Implement additional authentication providers if needed
3. Set up real-time subscriptions using Supabase Realtime
4. Configure backup and monitoring for your Supabase project
5. Implement caching strategies for better performance
