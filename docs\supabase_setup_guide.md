# Supabase Project Setup Guide

This guide will walk you through setting up your Supabase project "ai-brain" step by step.

## Step 1: Create Supabase Project

1. **Go to Supabase Dashboard**
   - Visit [https://supabase.com](https://supabase.com)
   - Click "Start your project" or "New Project"
   - Sign in with your GitHub account or create a new account

2. **Create New Project**
   - Click "New Project"
   - **Project Name**: `ai-brain`
   - **Database Password**: Choose a strong password (save this!)
   - **Region**: Choose the region closest to you
   - **Pricing Plan**: Start with the Free tier
   - Click "Create new project"

3. **Wait for Project Initialization**
   - This usually takes 1-2 minutes
   - You'll see a progress indicator
   - Once complete, you'll be redirected to your project dashboard

## Step 2: Get Your Project Credentials

1. **Navigate to Settings**
   - In your project dashboard, click on the "Settings" icon (gear icon) in the left sidebar
   - Click on "API" in the settings menu

2. **Copy Your Credentials**
   - **Project URL**: Copy the URL (looks like `https://xxxxx.supabase.co`)
   - **anon/public key**: Copy the `anon` key (starts with `eyJ...`)
   - **service_role key**: Copy the `service_role` key (starts with `eyJ...`) - Keep this secret!

## Step 3: Configure Environment Variables

Update your `.env` file with the credentials you just copied:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Database Configuration (optional - for direct PostgreSQL access)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# JWT Configuration for Supabase Auth
JWT_SECRET=your_jwt_secret_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24
```

## Step 4: Set Up Authentication

1. **Configure Auth Settings**
   - Go to "Authentication" → "Settings" in your Supabase dashboard
   - Under "Site URL", add your application URL (e.g., `http://localhost:8000`)
   - Under "Redirect URLs", add any callback URLs you need

2. **Enable Email Confirmation (Optional)**
   - In Authentication → Settings
   - Toggle "Enable email confirmations" if you want email verification
   - For development, you can disable this

## Step 5: Create Database Tables

You have two options:

### Option A: Automatic Setup (Recommended)
Run our setup script:
```bash
python scripts/setup_supabase.py
```

### Option B: Manual Setup
1. Go to "SQL Editor" in your Supabase dashboard
2. Click "New query"
3. Copy and paste the SQL from `supabase_setup.sql` (generated by the setup script)
4. Click "Run" to execute the SQL

## Step 6: Set Up Storage

1. **Create Storage Bucket**
   - Go to "Storage" in your Supabase dashboard
   - Click "Create a new bucket"
   - **Name**: `files`
   - **Public bucket**: Leave unchecked (private by default)
   - Click "Create bucket"

2. **Configure Storage Policies (Optional)**
   - Click on your `files` bucket
   - Go to "Policies" tab
   - Add policies as needed for your use case

## Step 7: Test Your Setup

1. **Run the Setup Script**
   ```bash
   python scripts/setup_supabase.py
   ```

2. **Run the Example Script**
   ```bash
   python examples/supabase_usage.py
   ```

3. **Start Your Server**
   ```bash
   uvicorn app.main:app --reload
   ```

4. **Check the API Documentation**
   - Visit [http://localhost:8000/docs](http://localhost:8000/docs)
   - Look for the `/api/supabase/` endpoints

## Step 8: Verify Everything Works

1. **Health Check**
   ```bash
   curl http://localhost:8000/api/supabase/health
   ```

2. **Test Authentication**
   - Try signing up a new user through the API
   - Check that the user appears in Authentication → Users

3. **Test Database**
   - Try creating a conversation through the API
   - Check that data appears in your database tables

## Troubleshooting

### Common Issues

1. **"Supabase is not configured" Error**
   - Check that your `.env` file has the correct credentials
   - Restart your application after updating `.env`

2. **Database Connection Errors**
   - Verify your project URL and keys are correct
   - Check that your Supabase project is active (not paused)

3. **Authentication Errors**
   - Ensure your Site URL is configured correctly
   - Check that email confirmations are disabled for development

4. **Storage Errors**
   - Verify the `files` bucket exists
   - Check storage policies if you're getting permission errors

### Getting Help

- Check the [Supabase Documentation](https://supabase.com/docs)
- Visit the [Supabase Community](https://github.com/supabase/supabase/discussions)
- Review our integration documentation in `docs/supabase_integration.md`

## Next Steps

Once your Supabase project is set up:

1. **Customize Your Schema**
   - Add additional tables as needed for your application
   - Set up proper relationships and constraints

2. **Configure Row Level Security**
   - Review and customize the RLS policies
   - Add more granular permissions as needed

3. **Set Up Real-time Features**
   - Enable real-time subscriptions for live updates
   - Configure webhooks for external integrations

4. **Production Considerations**
   - Upgrade to a paid plan for production use
   - Set up proper backup and monitoring
   - Configure custom domains and SSL

## Security Checklist

- [ ] Service role key is kept secret and not exposed in client-side code
- [ ] Row Level Security (RLS) is enabled on all tables
- [ ] Proper authentication policies are in place
- [ ] Storage bucket permissions are configured correctly
- [ ] Site URL and redirect URLs are properly configured
- [ ] Database passwords are strong and secure

Congratulations! Your Supabase "ai-brain" project is now ready to use with your Pydantic AI backend.
