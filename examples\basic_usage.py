#!/usr/bin/env python3
"""
Basic usage examples for the Pydantic AI backend.
"""

import asyncio
import httpx
import json
from typing import Dict, Any


class PydanticAIClient:
    """Simple client for interacting with the Pydantic AI backend."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient()
    
    async def chat(self, message: str, conversation_id: str = None, **kwargs) -> Dict[str, Any]:
        """Send a chat message to the AI."""
        payload = {
            "message": message,
            **kwargs
        }
        if conversation_id:
            payload["conversation_id"] = conversation_id
        
        response = await self.client.post(f"{self.base_url}/chat", json=payload)
        response.raise_for_status()
        return response.json()
    
    async def get_health(self) -> Dict[str, Any]:
        """Check the health of the service."""
        response = await self.client.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    async def list_tools(self) -> Dict[str, Any]:
        """List available tools."""
        response = await self.client.get(f"{self.base_url}/tools")
        response.raise_for_status()
        return response.json()
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def main():
    """Run example interactions with the Pydantic AI backend."""
    client = PydanticAIClient()
    
    try:
        # Check health
        print("🏥 Checking service health...")
        health = await client.get_health()
        print(f"Status: {health['status']}")
        print(f"Version: {health['version']}")
        print()
        
        # List available tools
        print("🛠️ Available tools:")
        tools = await client.list_tools()
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        print()
        
        # Example 1: Basic conversation
        print("💬 Example 1: Basic conversation")
        response = await client.chat("Hello! What can you help me with?")
        print(f"AI: {response['response']}")
        print()
        
        # Example 2: Calculator tool
        print("🧮 Example 2: Using calculator")
        response = await client.chat("Calculate the square root of 144")
        print(f"AI: {response['response']}")
        print()
        
        # Example 3: Weather tool
        print("🌤️ Example 3: Weather information")
        response = await client.chat("What's the weather like in Tokyo?")
        print(f"AI: {response['response']}")
        print()
        
        # Example 4: Text processing
        print("📝 Example 4: Text processing")
        response = await client.chat(
            "Count the words in this text: 'Pydantic AI is an amazing framework for building AI applications'"
        )
        print(f"AI: {response['response']}")
        print()
        
        # Example 5: Utility functions
        print("🔧 Example 5: Utility functions")
        response = await client.chat("Generate a UUID for me")
        print(f"AI: {response['response']}")
        print()
        
        # Example 6: Password generation
        print("🔐 Example 6: Password generation")
        response = await client.chat("Generate a secure password with 16 characters")
        print(f"AI: {response['response']}")
        print()
        
        # Example 7: Base64 encoding
        print("🔤 Example 7: Base64 encoding")
        response = await client.chat("Encode 'Hello Pydantic AI' to base64")
        print(f"AI: {response['response']}")
        print()
        
        # Example 8: Complex calculation
        print("🧮 Example 8: Complex calculation")
        response = await client.chat("Calculate sin(pi/2) + cos(0) + sqrt(25)")
        print(f"AI: {response['response']}")
        print()
        
        # Example 9: Email extraction
        print("📧 Example 9: Email extraction")
        response = await client.chat(
            "Extract email addresses from this text: 'Contact <NAME_EMAIL> or <EMAIL> for more information'"
        )
        print(f"AI: {response['response']}")
        print()
        
        # Example 10: Conversation with context
        print("💭 Example 10: Conversation with context")
        conversation_id = "example-conversation"
        
        response1 = await client.chat(
            "My name is Alice and I'm a software developer",
            conversation_id=conversation_id
        )
        print(f"AI: {response1['response']}")
        
        response2 = await client.chat(
            "What's my name and profession?",
            conversation_id=conversation_id
        )
        print(f"AI: {response2['response']}")
        print()
        
        print("✅ All examples completed successfully!")
        
    except httpx.HTTPError as e:
        print(f"❌ HTTP error: {e}")
        print("Make sure the Pydantic AI backend is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await client.close()


if __name__ == "__main__":
    print("🚀 Pydantic AI Backend - Example Usage")
    print("=" * 50)
    asyncio.run(main())
