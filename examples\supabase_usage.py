"""
Comprehensive examples demonstrating Supabase integration usage.

This file shows how to use authentication, database operations, and storage
with the Supabase integration in the Pydantic AI backend.
"""

import asyncio
import httpx
import json
from typing import Dict, Any, Optional


class SupabaseClient:
    """Example client for interacting with Supabase endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the client."""
        self.base_url = base_url
        self.access_token: Optional[str] = None
        self.client = httpx.AsyncClient()
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers with authentication if available."""
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        return headers
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


# Authentication Examples
async def authentication_examples():
    """Demonstrate authentication operations."""
    print("=== Authentication Examples ===\n")
    
    client = SupabaseClient()
    
    try:
        # 1. Sign up a new user
        print("1. Signing up a new user...")
        signup_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "Test User",
            "metadata": {"role": "user", "preferences": {"theme": "dark"}}
        }
        
        response = await client.client.post(
            f"{client.base_url}/api/supabase/auth/signup",
            json=signup_data,
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            auth_data = response.json()
            client.access_token = auth_data["access_token"]
            print(f"✅ User signed up successfully!")
            print(f"   User ID: {auth_data['user']['id']}")
            print(f"   Email: {auth_data['user']['email']}")
            print(f"   Access Token: {auth_data['access_token'][:20]}...")
        else:
            print(f"❌ Sign up failed: {response.text}")
        
        print()
        
        # 2. Sign in with existing user
        print("2. Signing in with existing user...")
        signin_data = {
            "email": "<EMAIL>",
            "password": "securepassword123"
        }
        
        response = await client.client.post(
            f"{client.base_url}/api/supabase/auth/signin",
            json=signin_data,
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            auth_data = response.json()
            client.access_token = auth_data["access_token"]
            print(f"✅ User signed in successfully!")
            print(f"   Access Token: {auth_data['access_token'][:20]}...")
        else:
            print(f"❌ Sign in failed: {response.text}")
        
        print()
        
        # 3. Get user profile
        print("3. Getting user profile...")
        response = await client.client.get(
            f"{client.base_url}/api/supabase/auth/user",
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ User profile retrieved!")
            print(f"   ID: {user_data['id']}")
            print(f"   Email: {user_data['email']}")
            print(f"   Full Name: {user_data.get('full_name', 'N/A')}")
            print(f"   Created: {user_data['created_at']}")
        else:
            print(f"❌ Get profile failed: {response.text}")
        
        print()
        
    finally:
        await client.close()


# Database Examples
async def database_examples():
    """Demonstrate database operations."""
    print("=== Database Examples ===\n")
    
    client = SupabaseClient()
    
    # First, authenticate
    signin_data = {
        "email": "<EMAIL>",
        "password": "securepassword123"
    }
    
    auth_response = await client.client.post(
        f"{client.base_url}/api/supabase/auth/signin",
        json=signin_data,
        headers=client._get_headers()
    )
    
    if auth_response.status_code == 200:
        client.access_token = auth_response.json()["access_token"]
    else:
        print("❌ Authentication required for database examples")
        await client.close()
        return
    
    try:
        # 1. Insert data
        print("1. Inserting data into conversations table...")
        insert_data = {
            "table": "conversations",
            "data": {
                "title": "My First Conversation",
                "metadata": {"topic": "AI", "priority": "high"}
            }
        }
        
        response = await client.client.post(
            f"{client.base_url}/api/supabase/db/insert",
            json=insert_data,
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Data inserted successfully!")
            print(f"   Records created: {result['count']}")
            if result['data']:
                conversation_id = result['data'][0]['id']
                print(f"   Conversation ID: {conversation_id}")
        else:
            print(f"❌ Insert failed: {response.text}")
        
        print()
        
        # 2. Query data
        print("2. Querying conversations...")
        query_data = {
            "table": "conversations",
            "select": "id,title,created_at,metadata",
            "order_by": "-created_at",
            "limit": 10
        }
        
        response = await client.client.post(
            f"{client.base_url}/api/supabase/db/query",
            json=query_data,
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Query successful!")
            print(f"   Records found: {result['count']}")
            for record in result['data'][:3]:  # Show first 3 records
                print(f"   - {record['title']} (ID: {record['id'][:8]}...)")
        else:
            print(f"❌ Query failed: {response.text}")
        
        print()
        
        # 3. Update data
        print("3. Updating conversation...")
        update_data = {
            "table": "conversations",
            "data": {"title": "Updated Conversation Title"},
            "filters": {"title": "My First Conversation"}
        }
        
        response = await client.client.put(
            f"{client.base_url}/api/supabase/db/update",
            json=update_data,
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Update successful!")
            print(f"   Records updated: {result['count']}")
        else:
            print(f"❌ Update failed: {response.text}")
        
        print()
        
        # 4. Count records
        print("4. Counting records...")
        response = await client.client.get(
            f"{client.base_url}/api/supabase/db/count/conversations",
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Count successful!")
            print(f"   Total conversations: {result['count']}")
        else:
            print(f"❌ Count failed: {response.text}")
        
        print()
        
    finally:
        await client.close()


# Storage Examples
async def storage_examples():
    """Demonstrate storage operations."""
    print("=== Storage Examples ===\n")
    
    client = SupabaseClient()
    
    # First, authenticate
    signin_data = {
        "email": "<EMAIL>",
        "password": "securepassword123"
    }
    
    auth_response = await client.client.post(
        f"{client.base_url}/api/supabase/auth/signin",
        json=signin_data,
        headers=client._get_headers()
    )
    
    if auth_response.status_code == 200:
        client.access_token = auth_response.json()["access_token"]
    else:
        print("❌ Authentication required for storage examples")
        await client.close()
        return
    
    try:
        # 1. Upload a file
        print("1. Uploading a file...")
        
        # Create a sample file
        file_content = b"This is a sample file for testing Supabase storage integration."
        
        files = {"file": ("test.txt", file_content, "text/plain")}
        data = {"path": "documents/test.txt"}
        
        # Note: For file upload, we need to use multipart/form-data
        headers = {"Authorization": f"Bearer {client.access_token}"}
        
        response = await client.client.post(
            f"{client.base_url}/api/supabase/storage/upload",
            files=files,
            data=data,
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ File uploaded successfully!")
            print(f"   Path: {result['path']}")
            print(f"   Size: {result['size']} bytes")
            print(f"   Content Type: {result['content_type']}")
        else:
            print(f"❌ Upload failed: {response.text}")
        
        print()
        
        # 2. List files
        print("2. Listing files...")
        response = await client.client.get(
            f"{client.base_url}/api/supabase/storage/list",
            params={"folder": "documents"},
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            files = response.json()
            print(f"✅ Files listed successfully!")
            print(f"   Found {len(files)} files:")
            for file_info in files:
                print(f"   - {file_info['name']} ({file_info.get('size', 'unknown')} bytes)")
        else:
            print(f"❌ List files failed: {response.text}")
        
        print()
        
        # 3. Get file URL
        print("3. Getting file URL...")
        response = await client.client.get(
            f"{client.base_url}/api/supabase/storage/url/documents/test.txt",
            params={"signed": True, "expires_in": 3600},
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ File URL generated!")
            print(f"   URL: {result['url'][:50]}...")
            print(f"   Expires in: {result['expires_in']} seconds")
        else:
            print(f"❌ Get URL failed: {response.text}")
        
        print()
        
        # 4. Download file
        print("4. Downloading file...")
        response = await client.client.get(
            f"{client.base_url}/api/supabase/storage/download/documents/test.txt",
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            downloaded_content = response.content
            print(f"✅ File downloaded successfully!")
            print(f"   Size: {len(downloaded_content)} bytes")
            print(f"   Content: {downloaded_content.decode()[:50]}...")
        else:
            print(f"❌ Download failed: {response.text}")
        
        print()
        
    finally:
        await client.close()


# Health Check Example
async def health_check_example():
    """Demonstrate health check."""
    print("=== Health Check Example ===\n")
    
    client = SupabaseClient()
    
    try:
        response = await client.client.get(
            f"{client.base_url}/api/supabase/health",
            headers=client._get_headers()
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Health check successful!")
            print(f"   Supabase Status: {result['supabase']['status']}")
            print(f"   Authenticated: {result['authenticated']}")
            print(f"   User ID: {result.get('user_id', 'N/A')}")
        else:
            print(f"❌ Health check failed: {response.text}")
        
        print()
        
    finally:
        await client.close()


# Main function to run all examples
async def main():
    """Run all Supabase integration examples."""
    print("🚀 Supabase Integration Examples\n")
    print("Make sure your Supabase project is configured and the server is running.\n")
    
    try:
        await health_check_example()
        await authentication_examples()
        await database_examples()
        await storage_examples()
        
        print("✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        print("\nMake sure:")
        print("1. Your Supabase project is configured in .env")
        print("2. The server is running on http://localhost:8000")
        print("3. Required database tables exist")


if __name__ == "__main__":
    asyncio.run(main())
