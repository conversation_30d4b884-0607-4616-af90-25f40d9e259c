# Context-Prompt Chat API Frontend Tester

A simple HTML interface to test the new context-prompt chat API endpoint.

## 🚀 Quick Start

1. **Start the Backend Server**:
   ```bash
   cd ../  # Go back to the main project directory
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Open the Frontend**:
   - Simply open `index.html` in your web browser
   - Or serve it locally:
     ```bash
     # Using Python's built-in server
     python -m http.server 3000
     # Then visit http://localhost:3000
     ```

## 📋 Features

### Form Fields
- **Context**: Background information/system prompt for the AI
- **Prompt**: Your specific question or request
- **Provider**: Choose AI provider (Auto-select, Google, Groq, etc.)
- **Max Tokens**: Maximum response length (1-4000)
- **Temperature**: Response creativity (0.0-2.0)

### Built-in Examples
Click on any example to auto-fill the form:
- **Coding Assistant**: Programming help and examples
- **Math <PERSON>**: Mathematical explanations
- **Creative Writer**: Story and content generation

### User Experience
- ✅ Real-time form validation
- ⏳ Loading states with spinner
- 📱 Responsive design for mobile/desktop
- 🎨 Clean, professional styling
- ❌ Comprehensive error handling

## 🔧 API Integration

The frontend sends POST requests to:
```
http://localhost:8000/chat/context-prompt
```

### Request Format
```json
{
  "context": "You are a helpful assistant...",
  "prompt": "What is the capital of France?",
  "provider": "google",
  "max_tokens": 1000,
  "temperature": 0.7
}
```

### Response Format
```json
{
  "response": "Paris is the capital of France.",
  "conversation_id": "uuid-string",
  "model_used": "google:gemini-2.0-flash"
}
```

## 🛠️ Troubleshooting

### Common Issues

1. **CORS Errors**:
   - The backend should already have CORS enabled
   - If issues persist, check the FastAPI CORS middleware configuration

2. **Connection Refused**:
   - Ensure the backend server is running on `http://localhost:8000`
   - Check that the API endpoint `/chat/context-prompt` is available

3. **Validation Errors**:
   - Make sure both context and prompt fields are filled
   - Check that max_tokens is between 1-4000
   - Verify temperature is between 0.0-2.0

### Testing the Backend

You can also test the API directly with curl:
```bash
curl -X POST http://localhost:8000/chat/context-prompt \
  -H "Content-Type: application/json" \
  -d '{
    "context": "You are a helpful assistant.",
    "prompt": "What is 2+2?",
    "provider": "google"
  }'
```

## 📝 Example Usage Scenarios

### 1. Code Review Assistant
- **Context**: "You are a senior software engineer reviewing code for best practices, security, and performance."
- **Prompt**: "Review this Python function: [paste code here]"

### 2. Learning Tutor
- **Context**: "You are a patient teacher explaining concepts to a beginner student."
- **Prompt**: "Explain how databases work in simple terms."

### 3. Creative Writing Helper
- **Context**: "You are a creative writing coach helping with story development and character creation."
- **Prompt**: "Help me develop a compelling antagonist for my fantasy novel."

## 🔄 Provider Selection

- **Auto-select**: Uses Google Gemini as primary, Groq as fallback
- **Google**: Uses Gemini models (gemini-2.0-flash for free tier)
- **Groq**: Uses Llama models (llama-3.3-70b-versatile)
- **Others**: OpenAI, Anthropic (if configured in backend)

## 📱 Mobile Support

The interface is fully responsive and works well on:
- Desktop browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Android Chrome)
- Tablet devices

## 🎨 Customization

The HTML file is self-contained and easy to customize:
- Modify CSS variables for colors and styling
- Add new example prompts in the JavaScript `examples` object
- Adjust form fields or validation rules
- Change the API endpoint URL in the `API_BASE_URL` constant

Enjoy testing your new context-prompt chat API! 🚀
