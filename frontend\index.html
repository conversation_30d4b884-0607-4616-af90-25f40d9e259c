<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Context-Prompt Chat API Tester</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }

        .form-section {
            padding: 30px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }

        .response-section {
            padding: 30px;
            background: white;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        textarea, select, input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        textarea:focus, select:focus, input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            width: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .response-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 200px;
        }

        .response-text {
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 12px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 12px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .examples {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }

        .examples h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .example-item {
            margin-bottom: 10px;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .example-item:hover {
            background-color: rgba(33, 150, 243, 0.1);
        }

        .example-item strong {
            color: #1976d2;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .form-section {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Context-Prompt Chat API Tester</h1>
            <p>Test the new context-prompt endpoint with structured AI conversations</p>
        </div>

        <div class="main-content">
            <div class="form-section">
                <div class="examples">
                    <h3>📝 Example Prompts</h3>
                    <div class="example-item" onclick="loadExample('coding')">
                        <strong>Coding Assistant:</strong> Help with programming questions
                    </div>
                    <div class="example-item" onclick="loadExample('math')">
                        <strong>Math Tutor:</strong> Explain mathematical concepts
                    </div>
                    <div class="example-item" onclick="loadExample('creative')">
                        <strong>Creative Writer:</strong> Generate creative content
                    </div>
                </div>

                <form id="chatForm">
                    <div class="form-group">
                        <label for="context">Context (System Prompt):</label>
                        <textarea 
                            id="context" 
                            name="context" 
                            placeholder="You are a helpful assistant who provides accurate and detailed information..."
                            required
                        ></textarea>
                    </div>

                    <div class="form-group">
                        <label for="prompt">Prompt (User Question):</label>
                        <textarea 
                            id="prompt" 
                            name="prompt" 
                            placeholder="What would you like to ask or discuss?"
                            required
                        ></textarea>
                    </div>

                    <div class="form-group">
                        <label for="provider">AI Provider:</label>
                        <select id="provider" name="provider">
                            <option value="">Auto-select (Google → Groq fallback)</option>
                            <option value="google">Google Gemini</option>
                            <option value="groq">Groq</option>
                            <option value="openai">OpenAI</option>
                            <option value="anthropic">Anthropic</option>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="maxTokens">Max Tokens:</label>
                            <input type="number" id="maxTokens" name="maxTokens" value="1000" min="1" max="4000">
                        </div>
                        <div class="form-group">
                            <label for="temperature">Temperature:</label>
                            <input type="number" id="temperature" name="temperature" value="0.7" min="0" max="2" step="0.1">
                        </div>
                    </div>

                    <button type="submit" class="submit-btn" id="submitBtn">
                        🚀 Send Request
                    </button>
                </form>
            </div>

            <div class="response-section">
                <h2>📤 API Response</h2>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Processing your request...</p>
                </div>

                <div class="response-container" id="responseContainer">
                    <p style="color: #666; text-align: center; margin-top: 80px;">
                        👆 Fill out the form and click "Send Request" to see the AI response here
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        
        const examples = {
            coding: {
                context: "You are an experienced software engineer and coding mentor. You provide clear, practical examples and explain complex concepts in simple terms. You focus on best practices and help developers write clean, efficient code.",
                prompt: "How do I create a REST API in Python using FastAPI? Please include a simple example with GET and POST endpoints."
            },
            math: {
                context: "You are a patient and knowledgeable math tutor who explains concepts step-by-step. You use real-world examples to make abstract concepts easier to understand and always check if the student needs clarification.",
                prompt: "Can you explain the Pythagorean theorem and show me how to use it to find the length of the hypotenuse in a right triangle?"
            },
            creative: {
                context: "You are a creative writing assistant with expertise in storytelling, character development, and narrative structure. You help writers brainstorm ideas, develop plots, and improve their writing style.",
                prompt: "Help me write the opening paragraph for a science fiction story about a colony on Mars discovering something unexpected underground."
            }
        };

        function loadExample(type) {
            const example = examples[type];
            if (example) {
                document.getElementById('context').value = example.context;
                document.getElementById('prompt').value = example.prompt;
            }
        }

        document.getElementById('chatForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const requestData = {
                context: formData.get('context').trim(),
                prompt: formData.get('prompt').trim(),
                max_tokens: parseInt(formData.get('maxTokens')) || 1000,
                temperature: parseFloat(formData.get('temperature')) || 0.7
            };

            // Add provider if selected
            if (formData.get('provider')) {
                requestData.provider = formData.get('provider');
            }

            // Basic validation
            if (!requestData.context || !requestData.prompt) {
                showError('Both context and prompt are required!');
                return;
            }

            await sendRequest(requestData);
        });

        async function sendRequest(requestData) {
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            const responseContainer = document.getElementById('responseContainer');

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ Processing...';
            loading.style.display = 'block';
            responseContainer.innerHTML = '';

            try {
                const response = await fetch(`${API_BASE_URL}/chat/context-prompt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.detail || `HTTP ${response.status}: ${response.statusText}`);
                }

                showResponse(data);

            } catch (error) {
                console.error('Error:', error);
                showError(error.message || 'Failed to connect to the API. Make sure the server is running on http://localhost:8000');
            } finally {
                // Reset loading state
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 Send Request';
                loading.style.display = 'none';
            }
        }

        function showResponse(data) {
            const responseContainer = document.getElementById('responseContainer');
            
            responseContainer.innerHTML = `
                <div class="success">
                    ✅ Request successful! Model: ${data.model_used || 'Unknown'}
                </div>
                <div class="response-text">${data.response}</div>
                ${data.conversation_id ? `<p style="margin-top: 15px; color: #666; font-size: 0.9em;">Conversation ID: ${data.conversation_id}</p>` : ''}
            `;
        }

        function showError(message) {
            const responseContainer = document.getElementById('responseContainer');
            responseContainer.innerHTML = `
                <div class="error">
                    ❌ Error: ${message}
                </div>
                <p style="margin-top: 15px; color: #666;">
                    Make sure the Pydantic AI backend is running on <code>http://localhost:8000</code>
                </p>
            `;
        }

        // Load a default example on page load
        window.addEventListener('load', function() {
            loadExample('coding');
        });
    </script>
</body>
</html>
