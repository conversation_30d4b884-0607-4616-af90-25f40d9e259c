{"name": "First AI Agent", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-180, -160], "id": "272ad39d-bf1c-40e9-9041-50abadcbe263", "name": "When chat message received", "webhookId": "c12aa4cd-62d8-41ab-a8b8-052f74a461e3"}, {"parameters": {"options": {"systemMessage": "=You are a helpful assistant\n\nYou must always look in the contacts database before doing something like creating an event or sending an email. You need the person's email address in order to do one of those actions. \n\nNever make up someone's email address. You must look in the contacts database tool. \n\nHere is the current date/time: {{ $now }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [60, -160], "id": "a6b7d27b-92d7-45e2-a9b2-92e01866155c", "name": "AI Agent"}, {"parameters": {"model": "openai/gpt-4.1-mini", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-60, 60], "id": "49878f3f-134f-47c5-8e4a-1a256ed392a7", "name": "GPT 4.1 mini", "credentials": {"openRouterApi": {"id": "fpo6OUh9TcHg29jk", "name": "OpenRouter account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [80, 60], "id": "ce412c9c-0f3a-41fb-a92d-ceea38042711", "name": "Simple Memory"}, {"parameters": {"documentId": {"__rl": true, "value": "1fLoBDKHF3a3LyWE5rAlALfW50A6Z34_xeZjq7MLYJus", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1fLoBDKHF3a3LyWE5rAlALfW50A6Z34_xeZjq7MLYJus/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1fLoBDKHF3a3LyWE5rAlALfW50A6Z34_xeZjq7MLYJus/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.5, "position": [360, 60], "id": "8eb44a3a-752e-422a-8c0a-f28accfaf78f", "name": "Contacts Database", "credentials": {"googleSheetsOAuth2Api": {"id": "s7PIUa59FFmDLn1e", "name": "Demo 4/2"}}}, {"parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "emailType": "text", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [500, 60], "id": "1eb53d9d-4222-48e0-b2f6-f9936d438817", "name": "Send Email", "webhookId": "5347f524-af2b-4076-8b36-0427d914d75b", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "additionalFields": {"attendees": ["={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('attendees0_Attendees', ``, 'string') }}"], "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [220, 60], "id": "c23b0a00-23d2-4e02-88e3-659937f55821", "name": "Create Event", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"content": "# Your First AI Agent\n- Connect OpenRouter Credential\n- Connect Google Credentials\n- Start Experimenting\n", "height": 640, "width": 1060}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-320, -360], "id": "0bd04855-7c39-47c6-9f92-cae2b4836f6e", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "GPT 4.1 mini": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Contacts Database": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Send Email": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Create Event": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3fa86941-31a4-4cff-94b5-a82c2eea071c", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "Lxt9tsogEZwWTmrw", "tags": []}