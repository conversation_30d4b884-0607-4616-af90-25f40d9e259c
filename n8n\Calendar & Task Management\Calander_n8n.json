{"nodes": [{"parameters": {"resource": "calendar", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start_Time', `Start time in Kathmandu timezone (e.g., 2025-05-15T14:00:00+05:45)`, 'string') }}", "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End_Time', `End time in Kathmandu timezone (e.g., 2025-05-15T15:00:00+05:45)`, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [300, 380], "id": "********-3415-4f92-938e-796e127fca9e", "name": "Check Time Slots", "credentials": {"googleCalendarOAuth2Api": {"id": "ktKkRytufrxv6XQq", "name": "Google Calendar account"}}}, {"parameters": {"operation": "delete", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [460, 400], "id": "b6c0e69e-fedb-4c74-bb05-f6c8bdad892e", "name": "Delete Event", "credentials": {"googleCalendarOAuth2Api": {"id": "ktKkRytufrxv6XQq", "name": "Google Calendar account"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', `Start date and time (e.g., 2025-05-15T14:00:00+05:45 for Nepal timezone)`, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', `End date and time (e.g., 2025-05-15T15:00:00+05:45 for Nepal timezone)`, 'string') }}", "additionalFields": {"timeZone": "Asia/Kathmandu", "description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', `Description of the Event`, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', `Summary of the Event`, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [300, 200], "id": "a0dbbb27-a1e7-43b2-a9f8-da3d68042580", "name": "Create Event", "credentials": {"googleCalendarOAuth2Api": {"id": "ktKkRytufrxv6XQq", "name": "Google Calendar account"}}}, {"parameters": {"contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [160, 240], "id": "18670cf1-1d92-4246-9768-5dcc7f5bf9e6", "name": "Simple Memory"}, {"parameters": {"model": "meta-llama/llama-4-maverick-17b-128e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [0, 240], "id": "3d39084a-4855-4158-ad99-6331ec9cc34b", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "PUE4PqELQgpM61IW", "name": "Groq account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "2a50435c-908c-4096-8b12-8fec7f21e255", "name": "When chat message received", "webhookId": "95f9137a-3d64-4605-94c1-82eb28bb61e8"}, {"parameters": {"options": {"systemMessage": "=You are a professional Calendar Assistant for Nepal, working in the Kathmandu timezone.\n\nYour primary responsibilities are:\n1. Creating new calendar events based on user's exact specifications\n2. Updating existing events when requested\n3. Deleting events when requested\n4. Viewing events when requested\n\nIMPORTANT TIMEZONE REQUIREMENTS:\n- ALWAYS use the Nepal/Kathmandu timezone (GMT+05:45) for all operations\n- Format ALL dates exactly as specified by the user - never assume or change dates\n- If the user says 'tomorrow at 3pm', make sure to calculate the correct date based on Nepal time\n- Add timezone indicator '+05:45' to all date/time values\n\nWhen creating events:\n1. ALWAYS get full details: date, start time, end time, summary, and description\n2. Format dates in ISO format with Kathmandu timezone: YYYY-MM-DDThh:mm:ss+05:45\n3. Example: 2025-05-15T14:00:00+05:45 for May 15, 2025 at 2:00 PM Nepal time\n\nFor event updates or deletions:\n1. First ask for the event name or ID\n2. Use the Get Event or Get All Events tools to find the event\n3. Then use Update Event or Delete Event tools\n\nAVOID THESE COMMON ERRORS:\n- NEVER use default dates or times - always get explicit confirmation\n- NEVER use the wrong timezone (especially avoid using US timezones)\n- NEVER reformat dates provided by users - use exactly what they specify\n\nThe current date and time in Nepal is: {{ DateTime.now().setZone('Asia/Kathmandu').format('yyyy-MM-dd HH:mm:ss') }}\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [220, 0], "id": "83364807-a9d9-4506-9a7d-10199ad3770a", "name": "Calander Agent"}, {"parameters": {"operation": "update", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', `Event ID to update`, 'string') }}", "updateFields": {"start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', `Start date and time (e.g., 2025-05-15T14:00:00+05:45 for Nepal timezone)`, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', `End date and time (e.g., 2025-05-15T15:00:00+05:45 for Nepal timezone)`, 'string') }}", "description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [560, 300], "id": "f4148ab9-490c-4a87-b78c-59e33daaf3ea", "name": "Update Event", "credentials": {"googleCalendarOAuth2Api": {"id": "ktKkRytufrxv6XQq", "name": "Google Calendar account"}}}, {"parameters": {"operation": "get", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [720, 240], "id": "bda5727c-33b6-4bca-bca2-a4f09a5df3af", "name": "Get an Event", "credentials": {"googleCalendarOAuth2Api": {"id": "ktKkRytufrxv6XQq", "name": "Google Calendar account"}}}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', `Maximum number of events to retrieve`, 'number') }}", "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('After', `Start time in Kathmandu timezone (e.g., 2025-05-15T00:00:00+05:45)`, 'string') }}", "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Before', `End time in Kathmandu timezone (e.g., 2025-05-15T23:59:59+05:45)`, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [460, 220], "id": "78fd8b8c-5b71-4041-a3d0-0cdc7ef6ab74", "name": "Get All Events", "credentials": {"googleCalendarOAuth2Api": {"id": "ktKkRytufrxv6XQq", "name": "Google Calendar account"}}}], "connections": {"Check Time Slots": {"ai_tool": [[{"node": "Calander Agent", "type": "ai_tool", "index": 0}]]}, "Delete Event": {"ai_tool": [[{"node": "Calander Agent", "type": "ai_tool", "index": 0}]]}, "Create Event": {"ai_tool": [[{"node": "Calander Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Calander Agent", "type": "ai_memory", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "Calander Agent", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Calander Agent", "type": "main", "index": 0}]]}, "Update Event": {"ai_tool": [[{"node": "Calander Agent", "type": "ai_tool", "index": 0}]]}, "Get an Event": {"ai_tool": [[{"node": "Calander Agent", "type": "ai_tool", "index": 0}]]}, "Get All Events": {"ai_tool": [[{"node": "Calander Agent", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}