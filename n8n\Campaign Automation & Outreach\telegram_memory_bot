{"nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "293c513b-3af3-4358-800e-bf642b9350d4", "name": "<PERSON>eg<PERSON>", "webhookId": "fe8f1521-9c90-45f6-8d3d-ff7c2ddfce7d", "credentials": {"telegramApi": {"id": "n3vpZg09pbBonUwd", "name": "Telegram Supermemory_n8n_bot"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appmhwpE5CaRrvJhf", "mode": "list", "cachedResultName": "Agent Memory", "cachedResultUrl": "https://airtable.com/appmhwpE5CaRrvJhf"}, "table": {"__rl": true, "value": "tblCjlZ1p9hWZgSMN", "mode": "list", "cachedResultName": "Memory", "cachedResultUrl": "https://airtable.com/appmhwpE5CaRrvJhf/tblCjlZ1p9hWZgSMN"}, "returnAll": false, "limit": 50, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [220, 100], "id": "********-83d8-455c-95b7-062c7bb730a5", "name": "Airtable", "credentials": {"airtableTokenApi": {"id": "Mggkzxf9oxfJl93x", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "Memory"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [440, 100], "id": "4587a8f3-2991-421c-a473-e23f14875f40", "name": "Aggregate"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [640, 20], "id": "8021076a-7d78-4a97-ad08-5f745e65ed00", "name": "<PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "={{ $json.message.text }}", "options": {"systemMessage": "=Current Time/Day: {{ DateTime.now().setZone('Asia/Kathmandu').format('yyyy-MM-dd HH:mm:ss') }}\n\nCurrency is: Rs.\n\nYou are a helpful assistant with the ability to add and read \"memories\" about the user.\n\nTo add a new memory or insight to the top of your list of recent memories you can use the memory_tool which allows you to add short 1 sentence insights about the user to your memory for the future, in order to help you customize your response output.\n\nYou don't have to always \"customize\" based on the memories, but if there is a good reason to customize your response you can use the memories below to do so. These are memories that have been added by you. If you need to learn more about the user for the future you can ask questions in order to take note of their preferences.\n\nUse your current memories of the user to recall past insights about them.\n\nRead your most recent memories here:\n\n{{ $json.Memory }}\n\nTailor your response based on relevant memories if you find that a memory is relevant to the response.\n\nAlways output your final response as a conversational piece rather than a list or blog post. If you must make a list keep it simple and don't add too much hierarchy only share the most important notes! After thinking of your response, consider the TLDR version and always give a conversational, cheeky, fun reply while remaining assertive, helpful and not too playful. Give a meek tone to your response.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [860, 20], "id": "faa486d1-6351-4041-8350-f24e99a318cd", "name": "AI Agent"}, {"parameters": {"model": "meta-llama/llama-4-maverick-17b-128e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [720, 220], "id": "50c6ac43-be11-4e72-8e74-e15463407259", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "PUE4PqELQgpM61IW", "name": "Groq account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Merge').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [900, 260], "id": "a4170727-1a78-4060-b414-95ef64540338", "name": "Simple Memory"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appmhwpE5CaRrvJhf", "mode": "list", "cachedResultName": "Agent Memory", "cachedResultUrl": "https://airtable.com/appmhwpE5CaRrvJhf"}, "table": {"__rl": true, "value": "tblCjlZ1p9hWZgSMN", "mode": "list", "cachedResultName": "Memory", "cachedResultUrl": "https://airtable.com/appmhwpE5CaRrvJhf/tblCjlZ1p9hWZgSMN"}, "columns": {"mappingMode": "defineBelow", "value": {"Memory": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Memory', ``, 'string') }}"}, "matchingColumns": ["Memory"], "schema": [{"id": "Memory", "displayName": "Memory", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [1100, 280], "id": "5fa8405c-2528-453b-b108-6f573ba66885", "name": "memory_tool", "credentials": {"airtableTokenApi": {"id": "Mggkzxf9oxfJl93x", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"disable_web_page_preview": true}}, "continueOnFail": true, "disablePoweredBy": true, "typeOptions": {"hideFooter": true}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1220, 20], "id": "d828c800-eb01-4c93-890f-2385e13df49c", "name": "Telegram", "webhookId": "1be8f3da-6d3e-4578-9b49-cbbe5c558b29", "credentials": {"telegramApi": {"id": "n3vpZg09pbBonUwd", "name": "Telegram Supermemory_n8n_bot"}}}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "// Convert markdown to plain text\nconst text = $input.item.json.output || '';\n\n// Remove markdown formatting and special characters\nconst cleanText = text\n  .replace(/\\*/g, '') // Remove asterisks (bold/italic formatting)\n  .replace(/\\_/g, '') // Remove underscores (italic formatting)\n  .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Convert markdown links to just the text\n  .replace(/\\n\\s*\\n/g, '\\n\\n') // Normalize multiple line breaks\n  .replace(/\\n\\s*-\\s*/g, '\\n• ') // Convert markdown list items to bullet points\n  .replace(/\\n\\s*\\d+\\.\\s*/g, '\\n• '); // Convert numbered lists to bullet points\n\nreturn [{\n  output: cleanText\n}];\n"}, "id": "c1ad07b4-2e91-4a60-9a4c-33e81756f1e5", "name": "Clean Message", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 20]}], "connections": {"Telegram Trigger": {"main": [[{"node": "Airtable", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Clean Message", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "memory_tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Clean Message": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}