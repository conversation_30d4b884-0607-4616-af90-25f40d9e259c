{"nodes": [{"parameters": {"operation": "search", "base": {"__rl": true, "value": "appmhwpE5CaRrvJhf", "mode": "list", "cachedResultName": "Agent Memory", "cachedResultUrl": "https://airtable.com/appmhwpE5CaRrvJhf"}, "table": {"__rl": true, "value": "tblCjlZ1p9hWZgSMN", "mode": "list", "cachedResultName": "Memory", "cachedResultUrl": "https://airtable.com/appmhwpE5CaRrvJhf/tblCjlZ1p9hWZgSMN"}, "returnAll": false, "limit": 10, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [860, 260], "id": "b6c74f39-a252-46ce-9618-852b9452769b", "name": "Airtable", "credentials": {"airtableTokenApi": {"id": "JtJr69JM2USjClZ3", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "Memory"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1080, 260], "id": "c44a67f7-cc1b-46e3-8c28-dc018284af6d", "name": "Aggregate"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1280, 180], "id": "e7c112fc-05c5-452d-8336-910c5522b2f8", "name": "<PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "={{ $json.messages[0].text.body }}", "options": {"systemMessage": "=Current Time/Day: {{ DateTime.now().setZone('Asia/Kathmandu').format('yyyy-MM-dd HH:mm:ss') }}\n\nCurrency is: Rs.\n\nYou are a helpful assistant with the ability to add and read \"memories\" about the user.\n\nTo add a new memory or insight to the top of your list of recent memories you can use the memory_tool which allows you to add short 1 sentence insights about the user to your memory for the future, in order to help you customize your response output.\n\nYou don't have to always \"customize\" based on the memories, but if there is a good reason to customize your response you can use the memories below to do so. These are memories that have been added by you. If you need to learn more about the user for the future you can ask questions in order to take note of their preferences.\n\nUse your current memories of the user to recall past insights about them.\n\nRead your most recent memories here:\n\n{{ $json.Memory }}\n\nTailor your response based on relevant memories if you find that a memory is relevant to the response.\n\nAlways output your final response as a conversational piece rather than a list or blog post. If you must make a list keep it simple and don't add too much hierarchy only share the most important notes! After thinking of your response, consider the TLDR version and always give a conversational, cheeky, fun reply while remaining assertive, helpful and not too playful. Give a meek tone to your response.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1500, 180], "id": "a88f2772-a3f7-49eb-aec2-9f25e7ef4b97", "name": "AI Agent"}, {"parameters": {"model": "meta-llama/llama-4-maverick-17b-128e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [1360, 380], "id": "9e639f35-2744-4a25-b01b-14a7514a2134", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "nvf67f6nOZ3yOadn", "name": "Groq account"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appmhwpE5CaRrvJhf", "mode": "list", "cachedResultName": "Agent Memory", "cachedResultUrl": "https://airtable.com/appmhwpE5CaRrvJhf"}, "table": {"__rl": true, "value": "tblCjlZ1p9hWZgSMN", "mode": "list", "cachedResultName": "Memory", "cachedResultUrl": "https://airtable.com/appmhwpE5CaRrvJhf/tblCjlZ1p9hWZgSMN"}, "columns": {"mappingMode": "defineBelow", "value": {"Memory": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Memory', ``, 'string') }}"}, "matchingColumns": ["Memory"], "schema": [{"id": "Memory", "displayName": "Memory", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [1740, 440], "id": "14e6c05f-bc88-42dc-b3fb-08998aca25e1", "name": "memory_tool", "credentials": {"airtableTokenApi": {"id": "JtJr69JM2USjClZ3", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"updates": ["messages"], "options": {}}, "type": "n8n-nodes-base.whatsAppTrigger", "typeVersion": 1, "position": [640, 160], "id": "af1a27a3-5bca-453b-9475-61331ea1d328", "name": "<PERSON><PERSON><PERSON><PERSON>", "webhookId": "f461d3aa-971d-49a9-87e1-099b1e233880", "credentials": {"whatsAppTriggerApi": {"id": "7Ez1kSBYf8M4qtL5", "name": "WhatsApp OAuth account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.messages }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1600, 400], "id": "3a56d129-1502-48a7-b630-cb5ee81e1705", "name": "Simple Memory"}, {"parameters": {"jsCode": "// Get the AI response\nconst aiResponse = $input.all()[0].json;\n\n// Log the full response for debugging\nconsole.log('AI Response:', JSON.stringify(aiResponse, null, 2));\n\n// Extract the message text\nlet messageText = '';\nif (Array.isArray(aiResponse) && aiResponse[0] && aiResponse[0].output) {\n  messageText = aiResponse[0].output;\n} else if (aiResponse.output) {\n  messageText = aiResponse.output;\n}\n\n// Return the message in the expected format\nreturn {\n  json: {\n    message: messageText\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1860, 180], "id": "5267f2da-6bf2-4813-8151-ce640e638e74", "name": "Code"}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "textBody": "={{ $json.message }}", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [2080, 180], "id": "b2295686-4778-4eb7-bb33-a3c898192b21", "name": "WhatsApp Business Cloud", "webhookId": "4fa7db03-b7ca-49d2-9be7-84b5c6eecc1b", "credentials": {"whatsAppApi": {"id": "6xJKABw4BzIPLNUQ", "name": "WhatsApp account"}}}], "connections": {"Airtable": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "memory_tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Airtable", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Code": {"main": [[{"node": "WhatsApp Business Cloud", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "3bd33844332cebc13c03a393ff8e7df77226768d904b375cb37eff5f3272c20c"}}