{"name": "OpenAI Image Gen LinkedIn Post", "nodes": [{"parameters": {"toolDescription": "Use this tool to search the web. ", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{searchTerm}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 1,\n  \"time_range\": null,\n  \"days\": 7,\n  \"include_answer\": true,\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for. "}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [160, 120], "id": "********-10ee-493f-bad5-1eb711549717", "name": "<PERSON><PERSON>", "credentials": {"httpHeaderAuth": {"id": "1Gs5ooRQh4ZYMIK6", "name": "<PERSON>ly <PERSON>"}}}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [300, 120], "id": "2941ec38-b4c9-414a-a4ca-9f682aa7c9ac", "name": "GPT_4.1", "credentials": {"openRouterApi": {"id": "1dQlOKKWiigrH9O9", "name": "OpenRouter account"}}}, {"parameters": {"person": "=", "shareMediaCategory": "IMAGE", "additionalFields": {}}, "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [1660, -160], "id": "f62fbb38-e4bc-406b-806b-301e1a6163e8", "name": "LinkedIn", "credentials": {"linkedInOAuth2Api": {"id": "ZuB067RUvLFXZGQs", "name": "LinkedIn account"}}, "disabled": true}, {"parameters": {"promptType": "define", "text": "=Topic of Post: {{ $json['Topic of Post'] }}\n\nTarget Audience: {{ $json['Target Audience'] }}", "options": {"systemMessage": "=# Overview\nYou are an AI agent specialized in creating professional, educational, and engaging LinkedIn posts based on any topic provided by the user.\n\n## Objectives:\nAlways begin by conducting a real-time search using the Tavily tool to gather the most accurate, up-to-date information on the topic. The post should be written to appeal to the provided target audience.\n\nBased on your research, generate a well-structured LinkedIn post that:\n- Starts with an engaging hook\n- Professional in tone\n- Clear and easy to read\n- Educational and insightful\n- Light on emojis (use only when highly relevant and minimal)\n- Includes proper source attribution (e.g., “according to [source]”)\n- Contains relevant hashtags to improve visibility\n- Ends with a clear call to action (e.g., asking for thoughts, feedback, or shares)\n\n## Output Instructions:\n- Your ONLY output should be the final LinkedIn post text.\n- Do not include explanations, notes, or anything beyond the post itself.\n\n## Example Workflow:\n1) Receive a topic (e.g., “The ROI of warehouse automation”)\n2) Use Tavily to search and gather recent information or case studies\n3) Draft a LinkedIn post using that research\n4) Format it with source citations, clean structure, optional hashtags, and a call to action"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [140, -160], "id": "5fb364cb-3b18-4cbf-9120-eaced28dc451", "name": "LinkedIn Post Agent"}, {"parameters": {"promptType": "define", "text": "=LinkedIn Post: \n{{ $json.output }}", "options": {"systemMessage": "=# Overview\nYou are an AI agent that transforms LinkedIn posts into visual prompt descriptions for generating graphic marketing materials. These visuals are designed to be paired with the post on LinkedIn, helping communicate the message in a visually engaging, brand-aligned way.\n\n## Objective:\n- Read and analyze the given LinkedIn post.\n- Identify the main message, insight, or takeaway from the post.\n- Create a clear and compelling graphic prompt that can be used with a text-to-image generator.\n- The result should be a marketing-style graphic — not a literal scene or hyperrealistic photo — that:\n1) Visually supports or illustrates the key idea of the post\n2) Looks appropriate for use in a professional LinkedIn feed\n3) Feels polished, modern, and engaging\n\n## Output Instructions:\n- Output only the final image prompt. Do not output quotation marks.\n- Do not repeat or rephrase the LinkedIn post.\n- Do not add any explanations or extra content — just the image prompt.\n- Never leave things blank like \"Header area reserved for customizable callout text\"\n- Output numeric stats when available in the original post\n\n## Style Guidelines:\n- Think like a brand designer or marketing creative.\n- Visuals may include: text, charts, icons, abstract shapes, overlays, modern illustrations, motion-like effects, bold typography elements (described, not rendered), or metaphorical concepts.\n- You can mention layout suggestions (e.g., \"split screen design,\" \"header with bold title and subtle background illustration\").\n- Assume the output will be generated using AI image tools — your prompt should guide those tools effectively.\n\n## Example Prompt Format:\nA modern flat-style graphic showing a human brain connected to mechanical gears, representing the fusion of AI and automation. Minimalist background, soft gradients, clean sans-serif text placement space at the top.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [580, -160], "id": "6fc0b285-2390-4519-a6a7-ddcc1ee0f2d0", "name": "Image Prompt Agent"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR API KEY"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.output }}"}, {"name": "size", "value": "1024x1024"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1020, -160], "id": "ee9a482e-1ff4-4ebe-96ad-bbf2b18cb439", "name": "Generate Image"}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1220, -160], "id": "75c64beb-e5d6-40af-a957-d00ca741d581", "name": "Convert to <PERSON>ary"}, {"parameters": {"sendTo": "={{ $('On form submission').item.json.Email }}", "subject": "Your LinkedIn Post is Here!", "emailType": "text", "message": "=Here you go Nate!\n\n\n{{ $('LinkedIn Post Agent').item.json.output }}", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1480, -160], "id": "4aa49f18-2970-4591-9e88-d6edbcd9e295", "name": "Send Post", "webhookId": "20f8ef41-7183-41f1-9104-a6a042b807ce", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"formTitle": "LinkedIn Post Generator", "formDescription": "Fill out these fields and you'll have a full LinkedIn post ready to go in a minute. ", "formFields": {"values": [{"fieldLabel": "Email", "fieldType": "email", "placeholder": "<EMAIL>", "requiredField": true}, {"fieldLabel": "Topic of Post", "placeholder": "Robots", "requiredField": true}, {"fieldLabel": "Target Audience", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-140, -160], "id": "13b71d98-c8ce-4280-ba63-e622bd6f47f2", "name": "On form submission", "webhookId": "359fa996-5531-40ae-be37-4a6e136952e2"}, {"parameters": {"content": "# Content Generation", "height": 260, "width": 380}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [80, -260], "id": "61234665-cb8f-4000-b931-41013784e943", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Generate Image\n", "height": 260, "width": 460, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [940, -260], "id": "c97f3d21-fd2b-4536-ab11-ec3edfd7fcf6", "name": "Sticky Note1"}, {"parameters": {"content": "# Post", "height": 260, "width": 400, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1420, -260], "id": "11f9f277-f5d8-49e7-b772-4b53552810ab", "name": "Sticky Note3"}, {"parameters": {"content": "# Image Prompt\n", "height": 260, "width": 440, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [480, -260], "id": "e086d06c-189c-4429-8ca5-c44c2bc5c321", "name": "Sticky Note4"}, {"parameters": {"content": "# Form Trigger", "height": 260, "width": 280, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, -260], "id": "b3217938-6b99-475e-9240-4ccfed473711", "name": "Sticky Note5"}, {"parameters": {"content": "## Tool & Model", "height": 240, "width": 380, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [80, 20], "id": "bcd38822-6a70-4771-aef2-ae6eab8742d0", "name": "Sticky Note2"}, {"parameters": {"content": "# 🛠️ Setup Guide  \n**Author:** [<PERSON>](https://www.youtube.com/@nateherk)\n\nThis guide will walk you through the four steps to fully configure your automation workflow using OpenRouter, Tavily, OpenAI, and either Gmail or LinkedIn.\n\n---\n\n## ✅ Step 1: Set Up OpenRouter Credentials  \n1. Go to [openrouter.ai](https://openrouter.ai/)  \n2. Create an account and generate your API key  \n3. In n8n, create a new credential and paste in your OpenRouter API key for use with chat models  \n\n---\n\n## ✅ Step 2: Add Your Tavily Credential  \n1. Visit [tavily.com](https://tavily.com/)  \n2. Sign up and retrieve your API key  \n3. Create a new HTTP credential in n8n and input your Tavily key for web search capabilities  \n\n---\n\n## ✅ Step 3: Configure OpenAI for Image Generation  \n1. Go to the [OpenAI API Platform](https://platform.openai.com/docs/overview)  \n2. Generate an API key  \n3. In n8n, create a credential and use it in your HTTP Request node for generating images  \n\n---\n\n## ✅ Step 4: Connect Gmail or LinkedIn  \nTo distribute your AI-generated content, connect one of the following:  \n- **Gmail:** Use OAuth2 credentials and the Gmail node to send content via email  \n- **LinkedIn:** Use OAuth2 or an HTTP node (with access token) to post content directly to your feed  \n\n---\n\nNow you're ready to run your workflow. Happy automating!\n", "height": 980, "width": 720}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-960, -260], "id": "1a0a9f29-cf13-41f2-aff5-a89de2085491", "name": "Sticky Note6"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-120, 480], "id": "8b180c79-d617-4369-9780-9f6d8ad545f2", "name": "When clicking ‘Test workflow’"}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [320, 480], "id": "100ea834-4610-403e-b6e7-442d940ada6d", "name": "Convert to File"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR API KEY"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "A cute baby sea otter"}, {"name": "size", "value": "1024x1024"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [100, 480], "id": "4c1b184e-7424-4b36-9f1f-4052a971a873", "name": "Generate Image1"}, {"parameters": {"content": "# Generation Image -> Binary Building Block", "height": 380, "width": 760, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, 340], "id": "fb1e64d8-b060-4b0f-840b-c3eca5ec5091", "name": "Sticky Note7"}], "pinData": {}, "connections": {"Tavily": {"ai_tool": [[{"node": "LinkedIn Post Agent", "type": "ai_tool", "index": 0}]]}, "GPT_4.1": {"ai_languageModel": [[{"node": "LinkedIn Post Agent", "type": "ai_languageModel", "index": 0}, {"node": "Image Prompt Agent", "type": "ai_languageModel", "index": 0}]]}, "LinkedIn Post Agent": {"main": [[{"node": "Image Prompt Agent", "type": "main", "index": 0}]]}, "Image Prompt Agent": {"main": [[{"node": "Generate Image", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Convert to <PERSON>ary", "type": "main", "index": 0}]]}, "Convert to Binary": {"main": [[{"node": "Send Post", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "LinkedIn Post Agent", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Generate Image1", "type": "main", "index": 0}]]}, "Generate Image1": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5d8fd5f7-c6fc-4da2-93cb-959f4e74cd70", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "RN2UzJModtElmTqD", "tags": []}