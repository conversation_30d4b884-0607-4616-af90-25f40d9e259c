{"nodes": [{"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{$node[\"Set API Key\"].json[\"apiKey\"]}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{ $json.userPrompt }}"}, {"name": "n", "value": 1}, {"name": "size", "value": "1024x1024"}, {"name": "response_format", "value": "b64_json"}]}, "options": {"response": {"response": {"fullResponse": true}}, "timeout": 60000, "retryOnFail": true, "maxRetries": 3, "retryDelay": 2000}}, "name": "DALL-E Image Generation", "type": "n8n-nodes-base.httpRequest", "typeVersion": "4.1", "position": [1380, 920], "id": "9987e865-57c4-4488-b824-c53b4d64c55e"}, {"parameters": {"values": {"string": [{"name": "imageBase64", "value": "={{ $json.body.data[0].b64_json }}"}]}, "options": {}}, "name": "Extract Base64 Image", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1640, 920], "id": "f6433c10-5f21-464b-8d21-640a9ce7e917"}, {"parameters": {"mode": "base64ToBinary", "options": {}}, "name": "Convert Base64 to Binary Image", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1.1, "position": [1900, 920], "id": "d54add33-3bff-4dec-9c85-ea02389c8fea"}, {"parameters": {"values": {"string": [{"name": "userPrompt", "value": "={{ $('Chat Trigger: Image Prompt').item.json.chatInput }}"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "YOUR_OPENAI_API_KEY_HERE"}]}, "options": {}}, "name": "Set API Key", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1140, 920], "id": "e9a14342-39d0-432b-a35f-2e243e6e5eda"}, {"parameters": {"options": {}}, "name": "Chat Trigger: Image Prompt", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [640, 920], "id": "efedd102-c04e-445e-9a64-838a2f261314"}], "connections": {"DALL-E Image Generation": {"main": [[{"node": "Extract Base64 Image", "type": "main", "index": 0}]]}, "Extract Base64 Image": {"main": [[{"node": "Convert Base64 to Binary Image", "type": "main", "index": 0}]]}, "Set API Key": {"main": [[{"node": "DALL-E Image Generation", "type": "main", "index": 0}]]}, "Chat Trigger: Image Prompt": {"main": [[{"node": "Set API Key", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}