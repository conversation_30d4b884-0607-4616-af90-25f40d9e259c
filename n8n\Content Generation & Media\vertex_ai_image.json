{"nodes": [{"parameters": {"mode": "base64ToBinary", "options": {}}, "name": "Convert Base64 to Binary Image", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1.1, "position": [1900, 920], "id": "d54add33-3bff-4dec-9c85-ea02389c8fea"}, {"parameters": {"values": {"string": [{"name": "imageBase64", "value": "={{ $json.predictions[0].bytesBase64Encoded }}"}]}, "options": {}}, "name": "Extract Base64 Image", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1640, 920], "id": "f6433c10-5f21-464b-8d21-640a9ce7e917"}, {"parameters": {"method": "POST", "url": "=https://{{$node[\"Prepare API Call Data\"].json[\"gcpLocation\"]}}-aiplatform.googleapis.com/v1/projects/{{$node[\"Prepare API Call Data\"].json[\"gcpProjectId\"]}}/locations/{{$node[\"Prepare API Call Data\"].json[\"gcpLocation\"]}}/publishers/google/models/imagegeneration:predict", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Get Google Access Token').item.json.accessToken }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{ $json.userPrompt }}"}]}, "options": {"response": {}}}, "name": "Vertex AI Image Generation", "type": "n8n-nodes-base.httpRequest", "typeVersion": "4.1", "position": [1380, 920], "id": "9987e865-57c4-4488-b824-c53b4d64c55e"}, {"parameters": {"values": {"string": [{"name": "gcpProjectId", "value": "exobank-web-app-s5vj1d"}, {"name": "gcpLocation", "value": "us-central1"}, {"name": "userPrompt", "value": "={{ $('Chat Trigger: Image Prompt').item.json.chatInput }}"}]}, "options": {}}, "name": "Prepare API Call Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1140, 920], "id": "e9a14342-39d0-432b-a35f-2e243e6e5eda"}, {"parameters": {"jsCode": "// README: \n// 1. Get your Service Account JSON key file from Google Cloud.\n// 2. REPLACE 'YOUR_SERVICE_ACCOUNT_CLIENT_EMAIL' below with the 'client_email' from your JSON key.\n// 3. REPLACE '-----<PERSON><PERSON><PERSON> PRIVATE KEY-----\\nYOUR_PRIVATE_KEY_CONTENTS_HERE\\n-----END PRIVATE KEY-----\\n' \n//    below with the entire 'private_key' value from your JSON key, including the BEGIN and END lines.\n\nconst crypto = require('crypto');\nconst https = require('https');\n\n// --- START: USER CONFIGURATION ---\nconst clientEmail = '<EMAIL>'; // e.g., '<EMAIL>'\nconst privateKeyPem = `-----B<PERSON>IN PRIVATE KEY-----\\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC0voZ+9dZ542mS\\nL9kroWsBQldXEEzaP0kWn7wBF4VCgnfcgdkFpt9/v34E+iTUUrLmYntWf6gw2aVv\\nI39YjhYLqZmUof+i412cKiRyqB7WF5+ybh8Hm3DvFR032ql47QhTSn05EDPEet/V\\nxN4bu76FhG4nxwmal1xnyBJC66ztkcrPTdG3aoelPZ9WXurhtEMB2T3LfQzSw4s/\\noaeCXJYHviC3JOCVgE0z7w8J4UfcFRXERtkI0a01OhPbg7YyRWHZj9gdjg0hZWMH\\nbkd2mtmIAvXmcaWVFip1sWxJh+w439GJQ0gQr0pjLRV4S7dD0/rvGcdRilXFAtyN\\noguUv3CXAgMBAAECggEABYc4TOTPCQ5cGcUN4ug+ghzZ2AdfGmPKI/+BvBTQlh6w\\ndNWh4ire9iTQwW03OClJYIONmzEvX0Mg7LvMttQtt3CaCnJEA+LpZ4/gaoNa4hh5\\n4Xa9yzlFZIKkUKOHAHRbh1SlbNvigXVyqDKrynRdGO9agSv0zr4nbOMxL8vOj7uT\\nJekbKZhr8TpXmVnHfV27wKJP1yTqAXZD25Sl3VPsHWse9GwshOe5UUI1d0mNbgT3\\nFVGFYwdGBS1JadXbe28BbL8UVQ+LrOfWwJZtp6BudyNjSBahUX+nKR9QF0mJh9d5\\nZFOD3Y2MMZsqeVYQY4ktA6DbtNFtGhRxNDqFUnRfxQKBgQDk0wnXB4Bg9B/mauGq\\nI6/CJCjgnIR8rguyf5Clbiv/KhrLX6fB7HmbKTrEtMTo3hwZ+wGkC7rdOjr/SRWh\\naozVokok3fkv6gIvsfr/p/feixtbErE6izTijGoS6HzaW2LJMvQJtBmaQxiwo0lg\\nxiSsO7uH9P9BdEraget1UMhXmwKBgQDKNbQqXLL5dmVTz4kmiudvAM069kxCcjSE\\nam80BIIkIuRa9J9rBbbpFpQDpN1x4mZrj/pXVkaidQzs9ykjv83k0PlRZw9wJ+60\\n2ANv5uQaJBE/1b4RPXrFaukR6Tt/Atf/fJJErLxKIItpbfCvTWayRxWJSNvN0TEr\\nyTXIRKSAtQKBgBLY5xiU/5f8w5vg9dFjuxhMjilh+u+iDkiXhTx7BJixyR8zM8gf\\nSCzlinelRArCMKJbMFlote1+3FkmleeDtTHrehvrRJkTtzZifIAOP7ZO0uP/eCWQ\\nTbzTP/FqwM3jdRGJZtxdvzYbjK8P4/1alK6zBNE/9r5Xa8rvpIs3Kik9AoGABWlu\\nivu0Jo0OpTGWwBtTmlrxIzTYUabw1OJsW34LZaAxcGW/poOeo5phF7/f1V5z00kl\\nMBU1CsALOoRa64QEpK0bHxydAylucBSf8uzBChoRBB5aWefFU2dLGZmeaAXRdhsF\\nZyUgXPOGB6pTmL1/lCHm56e1ikqhAEUAVX3Q0UkCgYAZzonR1gHno9rkSlbq4DUE\\nM+R7T+Vk3wY9QpB0+RlZaHIaSZ4qTEDbxTxcDTZ27mNCtadu4khhnaeV9H4HEtXR\\nuCxZ0WHvYYFCIkaZu68BkgsccEGwl6qp5Ya7Usb6NP1VzBFlu7Hv/16Yd0UvhN3m\\nCRuIWQlMsnB3vW44H0KBhQ==\\n-----END PRIVATE KEY-----\\n`;\n// --- END: USER CONFIGURATION ---\n\nconst tokenUri = 'https://oauth2.googleapis.com/token';\nconst scopes = 'https://www.googleapis.com/auth/cloud-platform';\n\nasync function getAccessToken() {\n    const header = {\n        alg: 'RS256',\n        typ: 'JWT'\n    };\n\n    const now = Math.floor(Date.now() / 1000);\n    const claimSet = {\n        iss: clientEmail,\n        scope: scopes,\n        aud: tokenUri,\n        exp: now + 3500, // Token valid for just under 1 hour\n        iat: now\n    };\n\n    const toSign = `${Buffer.from(JSON.stringify(header)).toString('base64url')}.${Buffer.from(JSON.stringify(claimSet)).toString('base64url')}`;\n\n    const sign = crypto.createSign('RSA-SHA256');\n    sign.update(toSign);\n    const signature = sign.sign(privateKeyPem, 'base64url');\n\n    const assertion = `${toSign}.${signature}`;\n\n    const postData = `grant_type=urn%3Aietf%3Aparams%3Aoauth%3Agrant-type%3Ajwt-bearer&assertion=${assertion}`;\n\n    const options = {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/x-www-form-urlencoded'\n        }\n    };\n\n    return new Promise((resolve, reject) => {\n        const req = https.request(tokenUri, options, (res) => {\n            let data = '';\n            res.on('data', (chunk) => {\n                data += chunk;\n            });\n            res.on('end', () => {\n                if (res.statusCode >= 200 && res.statusCode < 300) {\n                    try {\n                        resolve(JSON.parse(data).access_token);\n                    } catch (e) {\n                        reject(`Error parsing token response: ${e.message}. Response: ${data}`);\n                    }\n                } else {\n                    reject(`Error fetching token: ${res.statusCode} ${data}`);\n                }\n            });\n        });\n        req.on('error', (e) => {\n            reject(`Request error: ${e.message}`);\n        });\n        req.write(postData);\n        req.end();\n    });\n}\n\ntry {\n  const accessToken = await getAccessToken();\n  return [{ json: { accessToken: accessToken } }];\n} catch (error) {\n  console.error('Error in Code Node:', error);\n  return [{ json: { error: error.toString(), details: error.stack } }]; \n}\n"}, "name": "Get Google Access Token", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [900, 920], "id": "46d02605-edd6-4649-9617-cc48ddd948ce"}, {"parameters": {"options": {}}, "name": "Chat Trigger: Image Prompt", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [640, 920], "id": "efedd102-c04e-445e-9a64-838a2f261314", "webhookId": "your_unique_chat_webhook_id"}], "connections": {"Extract Base64 Image": {"main": [[{"node": "Convert Base64 to Binary Image", "type": "main", "index": 0}]]}, "Vertex AI Image Generation": {"main": [[{"node": "Extract Base64 Image", "type": "main", "index": 0}]]}, "Prepare API Call Data": {"main": [[{"node": "Vertex AI Image Generation", "type": "main", "index": 0}]]}, "Get Google Access Token": {"main": [[{"node": "Prepare API Call Data", "type": "main", "index": 0}]]}, "Chat Trigger: Image Prompt": {"main": [[{"node": "Get Google Access Token", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}