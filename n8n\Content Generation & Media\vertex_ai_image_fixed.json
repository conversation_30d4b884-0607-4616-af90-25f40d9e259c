{"nodes": [{"parameters": {"jsCode": "// README: \n// 1. Get your Service Account JSON key file from Google Cloud.\n// 2. REPLACE 'YOUR_SERVICE_ACCOUNT_CLIENT_EMAIL' below with the 'client_email' from your JSON key.\n// 3. REPLACE '-----<PERSON><PERSON><PERSON> PRIVATE KEY-----\\nYOUR_PRIVATE_KEY_CONTENTS_HERE\\n-----END PRIVATE KEY-----\\n' \n//    below with the entire 'private_key' value from your JSON key, including the BEGIN and END lines.\n\nconst crypto = require('crypto');\nconst https = require('https');\n\nconst clientEmail = '<EMAIL>';\nconst privateKeyPem = `-----BEGIN PRIVATE KEY-----\\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC0voZ+9dZ542mS\\nL9kroWsBQldXEEzaP0kWn7wBF4VCgnfcgdkFpt9/v34E+iTUUrLmYntWf6gw2aVv\\nI39YjhYLqZmUof+i412cKiRyqB7WF5+ybh8Hm3DvFR032ql47QhTSn05EDPEet/V\\nxN4bu76FhG4nxwmal1xnyBJC66ztkcrPTdG3aoelPZ9WXurhtEMB2T3LfQzSw4s/\\noaeCXJYHviC3JOCVgE0z7w8J4UfcFRXERtkI0a01OhPbg7YyRWHZj9gdjg0hZWMH\\nbkd2mtmIAvXmcaWVFip1sWxJh+w439GJQ0gQr0pjLRV4S7dD0/rvGcdRilXFAtyN\\noguUv3CXAgMBAAECggEABYc4TOTPCQ5cGcUN4ug+ghzZ2AdfGmPKI/+BvBTQlh6w\\ndNWh4ire9iTQwW03OClJYIONmzEvX0Mg7LvMttQtt3CaCnJEA+LpZ4/gaoNa4hh5\\n4Xa9yzlFZIKkUKOHAHRbh1SlbNvigXVyqDKrynRdGO9agSv0zr4nbOMxL8vOj7uT\\nJekbKZhr8TpXmVnHfV27wKJP1yTqAXZD25Sl3VPsHWse9GwshOe5UUI1d0mNbgT3\\nFVGFYwdGBS1JadXbe28BbL8UVQ+LrOfWwJZtp6BudyNjSBahUX+nKR9QF0mJh9d5\\nZFOD3Y2MMZsqeVYQY4ktA6DbtNFtGhRxNDqFUnRfxQKBgQDk0wnXB4Bg9B/mauGq\\nI6/CJCjgnIR8rguyf5Clbiv/KhrLX6fB7HmbKTrEtMTo3hwZ+wGkC7rdOjr/SRWh\\naozVokok3fkv6gIvsfr/p/feixtbErE6izTijGoS6HzaW2LJMvQJtBmaQxiwo0lg\\nxiSsO7uH9P9BdEraget1UMhXmwKBgQDKNbQqXLL5dmVTz4kmiudvAM069kxCcjSE\\nam80BIIkIuRa9J9rBbbpFpQDpN1x4mZrj/pXVkaidQzs9ykjv83k0PlRZw9wJ+60\\n2ANv5uQaJBE/1b4RPXrFaukR6Tt/Atf/fJJErLxKIItpbfCvTWayRxWJSNvN0TEr\\nyTXIRKSAtQKBgBLY5xiU/5f8w5vg9dFjuxhMjilh+u+iDkiXhTx7BJixyR8zM8gf\\nSCzlinelRArCMKJbMFlote1+3FkmleeDtTHrehvrRJkTtzZifIAOP7ZO0uP/eCWQ\\nTbzTP/FqwM3jdRGJZtxdvzYbjK8P4/1alK6zBNE/9r5Xa8rvpIs3Kik9AoGABWlu\\nivu0Jo0OpTGWwBtTmlrxIzTYUabw1OJsW34LZaAxcGW/poOeo5phF7/f1V5z00kl\\nMBU1CsALOoRa64QEpK0bHxydAylucBSf8uzBChoRBB5aWefFU2dLGZmeaAXRdhsF\\nZyUgXPOGB6pTmL1/lCHm56e1ikqhAEUAVX3Q0UkCgYAZzonR1gHno9rkSlbq4DUE\\nM+R7T+Vk3wY9QpB0+RlZaHIaSZ4qTEDbxTxcDTZ27mNCtadu4khhnaeV9H4HEtXR\\nuCxZ0WHvYYFCIkaZu68BkgsccEGwl6qp5Ya7Usb6NP1VzBFlu7Hv/16Yd0UvhN3m\\nCRuIWQlMsnB3vW44H0KBhQ==\\n-----END PRIVATE KEY-----\\n`;\n\nconst tokenUri = 'https://oauth2.googleapis.com/token';\nconst scopes = 'https://www.googleapis.com/auth/cloud-platform';\n\nasync function getAccessToken() {\n    const header = {\n        alg: 'RS256',\n        typ: 'JWT'\n    };\n    const now = Math.floor(Date.now() / 1000);\n    const claimSet = {\n        iss: clientEmail,\n        scope: scopes,\n        aud: tokenUri,\n        exp: now + 3500,\n        iat: now\n    };\n    const toSign = `${Buffer.from(JSON.stringify(header)).toString('base64url')}.${Buffer.from(JSON.stringify(claimSet)).toString('base64url')}`;\n    const sign = crypto.createSign('RSA-SHA256');\n    sign.update(toSign);\n    const signature = sign.sign(privateKeyPem, 'base64url');\n    const assertion = `${toSign}.${signature}`;\n    const postData = `grant_type=urn%3Aietf%3Aparams%3Aoauth%3Agrant-type%3Ajwt-bearer&assertion=${assertion}`;\n\n    const options = {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }\n    };\n\n    return new Promise((resolve, reject) => {\n        const req = https.request(tokenUri, options, (res) => {\n            let data = '';\n            res.on('data', (chunk) => { data += chunk; });\n            res.on('end', () => {\n                if (res.statusCode >= 200 && res.statusCode < 300) {\n                    try {\n                        resolve(JSON.parse(data).access_token);\n                    } catch (e) {\n                        reject(`Error parsing token response: ${e.message}. Response: ${data}`);\n                    }\n                } else {\n                    reject(`Error fetching token: ${res.statusCode} ${data}`);\n                }\n            });\n        });\n        req.on('error', (e) => reject(`Request error: ${e.message}`));\n        req.write(postData);\n        req.end();\n    });\n}\n\ntry {\n  const accessToken = await getAccessToken();\n  return [{ json: { accessToken } }];\n} catch (error) {\n  return [{ json: { error: error.toString(), details: error.stack } }];\n}"}, "name": "Get Google Access Token", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1160, 580], "id": "3f5df7ff-9b03-48e7-afcb-5e1759d37af6"}, {"parameters": {"values": {"string": [{"name": "gcpProjectId", "value": "exobank-web-app-s5vj1d"}, {"name": "gcpLocation", "value": "us-central1"}, {"name": "userPrompt", "value": "={{ $('Chat Trigger: Image Prompt').item.json.chatInput }}"}]}, "options": {}}, "name": "Prepare API Call Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1400, 580], "id": "da3bd976-e600-4028-9485-84979c2f12be"}, {"parameters": {"method": "POST", "url": "=https://{{$node[\"Prepare API Call Data\"].json[\"gcpLocation\"]}}-aiplatform.googleapis.com/v1/projects/{{$node[\"Prepare API Call Data\"].json[\"gcpProjectId\"]}}/locations/{{$node[\"Prepare API Call Data\"].json[\"gcpLocation\"]}}/publishers/google/models/imagegeneration:predict", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Get Google Access Token').item.json.accessToken }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "instances", "value": "={{ [{prompt: $json.userPrompt}] }}"}, {"name": "parameters", "value": "={{ [{sampleCount: 6}] }}"}]}, "options": {"response": {"response": {"fullResponse": true}}, "proxy": "", "timeout": 60000}}, "name": "Vertex AI Image Generation", "type": "n8n-nodes-base.httpRequest", "typeVersion": "4.1", "position": [1640, 580], "id": "afcf0395-20a4-4825-b1d9-7decde5db7db"}, {"parameters": {"options": {}}, "name": "Chat Trigger: Image Prompt", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [900, 580], "id": "1b8ba3c5-33e8-4fb8-81fa-6d48e6690a4f", "webhookId": "9184439f-6bc5-47ce-a72c-59c6ed46f4a8"}, {"parameters": {"assignments": {"assignments": [{"id": "ddaf92aa-7ea1-489c-a983-1617b4a5a0ec", "name": "body.predictions[1]", "value": "={{ $json.body.predictions[1].bytesBase64Encoded }}", "type": "string"}, {"id": "e903cc0f-d681-4fc8-a3fa-6e7dd99cdfb5", "name": "body.predictions[0]", "value": "={{ $json.body.predictions[0].bytesBase64Encoded }}", "type": "string"}, {"id": "03a22bea-2e05-4014-994e-f0e5074088f7", "name": "body.predictions[2]", "value": "={{ $json.body.predictions[2].bytesBase64Encoded }}", "type": "string"}, {"id": "e16735df-ce89-40a5-94c3-70ceae83e291", "name": "body.predictions[3]", "value": "={{ $json.body.predictions[3].bytesBase64Encoded }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1860, 580], "id": "f1af1b84-949b-48b5-86f5-786518f15689", "name": "<PERSON>"}, {"parameters": {"operation": "toBinary", "sourceProperty": "body.predictions[1]", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2080, 580], "id": "73ad85ff-390c-4b62-87b0-c9ff443d6fb5", "name": "Convert to File"}, {"parameters": {"operation": "toBinary", "sourceProperty": "body.predictions[0]", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2080, 380], "id": "947338e3-0ea8-4b5e-ab67-d8a6661bc66f", "name": "Convert to File1"}, {"parameters": {"operation": "toBinary", "sourceProperty": "body.predictions[2]", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2080, 780], "id": "f6a22c3d-0465-4c73-9580-dc8229b599cd", "name": "Convert to File2"}, {"parameters": {"operation": "toBinary", "sourceProperty": "body.predictions[3]", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2080, 980], "id": "dc38a398-a72f-47d8-89ce-7a04ca8f0ed3", "name": "Convert to File3"}], "connections": {"Get Google Access Token": {"main": [[{"node": "Prepare API Call Data", "type": "main", "index": 0}]]}, "Prepare API Call Data": {"main": [[{"node": "Vertex AI Image Generation", "type": "main", "index": 0}]]}, "Vertex AI Image Generation": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Chat Trigger: Image Prompt": {"main": [[{"node": "Get Google Access Token", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}, {"node": "Convert to File1", "type": "main", "index": 0}, {"node": "Convert to File2", "type": "main", "index": 0}, {"node": "Convert to File3", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}