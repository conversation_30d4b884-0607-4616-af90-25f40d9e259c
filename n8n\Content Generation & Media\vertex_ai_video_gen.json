{"nodes": [{"parameters": {"jsCode": "const crypto = require('crypto');\nconst https = require('https');\n\nconst clientEmail = '<EMAIL>'; \nconst privateKeyPem = `-----B<PERSON>IN PRIVATE KEY-----\\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC0voZ+9dZ542mS\\nL9kroWsBQldXEEzaP0kWn7wBF4VCgnfcgdkFpt9/v34E+iTUUrLmYntWf6gw2aVv\\nI39YjhYLqZmUof+i412cKiRyqB7WF5+ybh8Hm3DvFR032ql47QhTSn05EDPEet/V\\nxN4bu76FhG4nxwmal1xnyBJC66ztkcrPTdG3aoelPZ9WXurhtEMB2T3LfQzSw4s/\\noaeCXJYHviC3JOCVgE0z7w8J4UfcFRXERtkI0a01OhPbg7YyRWHZj9gdjg0hZWMH\\nbkd2mtmIAvXmcaWVFip1sWxJh+w439GJQ0gQr0pjLRV4S7dD0/rvGcdRilXFAtyN\\noguUv3CXAgMBAAECggEABYc4TOTPCQ5cGcUN4ug+ghzZ2AdfGmPKI/+BvBTQlh6w\\ndNWh4ire9iTQwW03OClJYIONmzEvX0Mg7LvMttQtt3CaCnJEA+LpZ4/gaoNa4hh5\\n4Xa9yzlFZIKkUKOHAHRbh1SlbNvigXVyqDKrynRdGO9agSv0zr4nbOMxL8vOj7uT\\nJekbKZhr8TpXmVnHfV27wKJP1yTqAXZD25Sl3VPsHWse9GwshOe5UUI1d0mNbgT3\\nFVGFYwdGBS1JadXbe28BbL8UVQ+LrOfWwJZtp6BudyNjSBahUX+nKR9QF0mJh9d5\\nZFOD3Y2MMZsqeVYQY4ktA6DbtNFtGhRxNDqFUnRfxQKBgQDk0wnXB4Bg9B/mauGq\\nI6/CJCjgnIR8rguyf5Clbiv/KhrLX6fB7HmbKTrEtMTo3hwZ+wGkC7rdOjr/SRWh\\naozVokok3fkv6gIvsfr/p/feixtbErE6izTijGoS6HzaW2LJMvQJtBmaQxiwo0lg\\nxiSsO7uH9P9BdEraget1UMhXmwKBgQDKNbQqXLL5dmVTz4kmiudvAM069kxCcjSE\\nam80BIIkIuRa9J9rBbbpFpQDpN1x4mZrj/pXVkaidQzs9ykjv83k0PlRZw9wJ+60\\n2ANv5uQaJBE/1b4RPXrFaukR6Tt/Atf/fJJErLxKIItpbfCvTWayRxWJSNvN0TEr\\nyTXIRKSAtQKBgBLY5xiU/5f8w5vg9dFjuxhMjilh+u+iDkiXhTx7BJixyR8zM8gf\\nSCzlinelRArCMKJbMFlote1+3FkmleeDtTHrehvrRJkTtzZifIAOP7ZO0uP/eCWQ\\nTbzTP/FqwM3jdRGJZtxdvzYbjK8P4/1alK6zBNE/9r5Xa8rvpIs3Kik9AoGABWlu\\nivu0Jo0OpTGWwBtTmlrxIzTYUabw1OJsW34LZaAxcGW/poOeo5phF7/f1V5z00kl\\nMBU1CsALOoRa64QEpK0bHxydAylucBSf8uzBChoRBB5aWefFU2dLGZmeaAXRdhsF\\nZyUgXPOGB6pTmL1/lCHm56e1ikqhAEUAVX3Q0UkCgYAZzonR1gHno9rkSlbq4DUE\\nM+R7T+Vk3wY9QpB0+RlZaHIaSZ4qTEDbxTxcDTZ27mNCtadu4khhnaeV9H4HEtXR\\nuCxZ0WHvYYFCIkaZu68BkgsccEGwl6qp5Ya7Usb6NP1VzBFlu7Hv/16Yd0UvhN3m\\nCRuIWQlMsnB3vW44H0KBhQ==\\n-----END PRIVATE KEY-----\\n`; \n\nconst tokenUri = 'https://oauth2.googleapis.com/token';\nconst scopes = 'https://www.googleapis.com/auth/cloud-platform';\n\nasync function getAccessToken() {\n    const header = {\n        alg: 'RS256',\n        typ: 'JWT'\n    };\n    const now = Math.floor(Date.now() / 1000);\n    const claimSet = {\n        iss: clientEmail,\n        scope: scopes,\n        aud: tokenUri,\n        exp: now + 3500, \n        iat: now\n    };\n    const toSign = `${Buffer.from(JSON.stringify(header)).toString('base64url')}.${Buffer.from(JSON.stringify(claimSet)).toString('base64url')}`;\n    const sign = crypto.createSign('RSA-SHA256');\n    sign.update(toSign);\n    const signature = sign.sign(privateKeyPem, 'base64url');\n    const assertion = `${toSign}.${signature}`;\n    const postData = `grant_type=urn%3Aietf%3Aparams%3Aoauth%3Agrant-type%3Ajwt-bearer&assertion=${assertion}`;\n\n    const options = {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n        family: 4 \n    };\n\n    return new Promise((resolve, reject) => {\n        const req = https.request(tokenUri, options, (res) => {\n            let data = '';\n            res.on('data', (chunk) => { data += chunk; });\n            res.on('end', () => {\n                if (res.statusCode >= 200 && res.statusCode < 300) {\n                    try {\n                        resolve(JSON.parse(data).access_token);\n                    } catch (e) {\n                        reject(`Error parsing token response: ${e.message}. Response: ${data}`);\n                    }\n                } else {\n                    reject(`Error fetching token: ${res.statusCode} ${data}`);\n                }\n            });\n        });\n        req.on('error', (e) => reject(`Request error: ${e.message}`));\n        req.write(postData);\n        req.end();\n    });\n}\n\ntry {\n  if (clientEmail === 'YOUR_SERVICE_ACCOUNT_CLIENT_EMAIL' || privateKeyPem.includes('YOUR_PRIVATE_KEY_CONTENTS_HERE')) {\n    throw new Error('Please configure your Service Account client_email and private_key in the Get Google Access Token node. The current clientEmail is: ' + clientEmail);\n  }\n  const accessToken = await getAccessToken();\n  return [{ json: { accessToken } }];\n} catch (error) {\n  return [{ json: { error: error.toString(), details: error.stack } }];\n}"}, "name": "Get Google Access Token", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1040, 620], "id": "a5dcbb7c-cae2-446b-98db-cde547b5162e"}, {"parameters": {"jsCode": "const https = require('https');\n\n// Get data from previous nodes\nconst gcpLocation = $('Prepare API Call Data').item.json.gcpLocation;\nconst gcpProjectId = $('Prepare API Call Data').item.json.gcpProjectId;\nconst accessToken = $('Get Google Access Token').item.json.accessToken;\nconst userPrompt = $('Prepare API Call Data').item.json.userPrompt;\nconst videoLengthSeconds = $('Prepare API Call Data').item.json.videoLengthSeconds;\nconst aspectRatio = $('Prepare API Call Data').item.json.aspectRatio;\n\n// Use proper long-running operation endpoint\nconst apiUrl = `https://${gcpLocation}-aiplatform.googleapis.com/v1/projects/${gcpProjectId}/locations/${gcpLocation}/publishers/google/models/veo-3.0-generate-001:predict`;\n\n// Structure the payload according to Vertex AI documentation for long-running video generation\nconst postDataPayload = {\n    instances: [{\n        prompt: userPrompt\n    }],\n    parameters: {\n        sampleCount: 1,\n        videoLengthSeconds: videoLengthSeconds,\n        aspectRatio: aspectRatio\n    }\n};\n\nconst postDataString = JSON.stringify(postDataPayload);\n\nconst options = {\n    method: 'POST',\n    headers: {\n        'Authorization': `Bearer ${accessToken}`, \n        'Content-Type': 'application/json',\n        'Content-Length': Buffer.byteLength(postDataString)\n    },\n    family: 4, // Force IPv4\n    timeout: 180000 // 3 minutes timeout\n};\n\nreturn new Promise((resolve, reject) => {\n    const req = https.request(apiUrl, options, (res) => {\n        let data = '';\n        res.on('data', (chunk) => {\n            data += chunk;\n        });\n        res.on('end', () => {\n            try {\n                // Handle empty response\n                if (!data || data.trim() === '') {\n                    resolve([{ json: { body: {}, statusCode: res.statusCode, headers: res.headers } }]);\n                    return;\n                }\n\n                const responseBody = JSON.parse(data);\n\n                // Add better debugging information\n                const result = { \n                    body: responseBody, \n                    statusCode: res.statusCode, \n                    headers: res.headers,\n                    debug: {\n                        requestUrl: apiUrl,\n                        requestPayload: postDataPayload\n                    }\n                };\n\n                // Return structure similar to HTTP Request node with 'Full Response'\n                resolve([{ json: result }]);\n            } catch (e) {\n                // If JSON parsing fails, return raw data along with error\n                reject(`Error parsing JSON response: ${e.message}. Raw response: ${data}. Status: ${res.statusCode}`);\n            }\n        });\n    });\n\n    req.on('error', (e) => {\n        reject(`Request error: ${e.message}`);\n    });\n\n    req.on('timeout', () => {\n        req.destroy();\n        reject('Request timed out after 180 seconds');\n    });\n\n    req.write(postDataString);\n    req.end();\n});"}, "name": "Vertex AI Video Generation", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1520, 620], "id": "d0133dbe-c013-4f73-b4a8-c05b8e26b9ae"}, {"parameters": {"options": {}}, "name": "<PERSON><PERSON> Trigger: Video Prompt", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [780, 620], "id": "bed00020-80f3-4bfa-9e82-b31fe955dcae", "webhookId": "your-new-webhook-id-for-video"}, {"parameters": {"assignments": {"assignments": [{"id": "video-data-assignment-uuid-1", "name": "video_data_for_conversion", "value": "={{ ($json.body && $json.body.predictions && $json.body.predictions.length > 0 && $json.body.predictions[0].bytesBase64Encoded) ? $json.body.predictions[0].bytesBase64Encoded : null }}", "type": "string"}, {"id": "debug-info-uuid", "name": "debug_info", "value": "={{ $json }}", "type": "object"}]}, "options": {}}, "name": "Extract Video Data", "type": "n8n-nodes-base.set", "typeVersion": "3.4", "position": [1740, 620], "id": "f8d59489-1a79-4360-b6b2-0d902f0e2dff"}, {"parameters": {"operation": "toBinary", "sourceProperty": "video_data_for_conversion", "options": {"fileName": "={{ 'generated_video_' + $now.toFormat('yyyyMMddHHmmss') + '.mp4' }}", "mimeType": "video/mp4"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1960, 620], "id": "e09b02e9-cccf-4903-85f7-8b60a9104bd2", "name": "Convert Video to File"}, {"parameters": {"values": {"string": [{"name": "gcpProjectId", "value": "exobank-web-app-s5vj1d"}, {"name": "gcpLocation", "value": "us-central1"}, {"name": "userPrompt", "value": "={{ $('Chat Trigger: Video Prompt').item.json.chatInput }}"}, {"name": "videoLengthSeconds", "value": "3"}, {"name": "aspectRatio", "value": "16:9"}, {"name": "modelVersion", "value": "videogeneration"}]}, "options": {}}, "name": "Prepare API Call Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1280, 620], "id": "e109b25d-bf4c-4c66-82b5-e589f9f7cf81"}], "connections": {"Get Google Access Token": {"main": [[{"node": "Prepare API Call Data", "type": "main", "index": 0}]]}, "Vertex AI Video Generation": {"main": [[{"node": "Extract Video Data", "type": "main", "index": 0}]]}, "Chat Trigger: Video Prompt": {"main": [[{"node": "Get Google Access Token", "type": "main", "index": 0}]]}, "Extract Video Data": {"main": [[{"node": "Convert Video to File", "type": "main", "index": 0}]]}, "Prepare API Call Data": {"main": [[{"node": "Vertex AI Video Generation", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}