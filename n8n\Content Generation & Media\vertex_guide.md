# Vertex AI Video Generation (Veo) Integration Guide for AI Agents

This guide provides instructions for AI agents to build video generation tools using Vertex AI's Veo model. It focuses on integration using **n8n** and **Node.js**.

**Core Veo API Documentation:**
* **Generate Videos Guide:** [https://cloud.google.com/vertex-ai/generative-ai/docs/video/generate-videos](https://cloud.google.com/vertex-ai/generative-ai/docs/video/generate-videos)
* **Veo API Reference:** [https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/veo-video-generation](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/veo-video-generation)
* **Prompting Guide:** [https://cloud.google.com/vertex-ai/generative-ai/docs/video/video-gen-prompt-guide](https://cloud.google.com/vertex-ai/generative-ai/docs/video/video-gen-prompt-guide)

---

## 1. Core Concepts & Prerequisites

### 1.1. Overview of Veo
Veo is a generative AI model on Google Cloud's Vertex AI platform, designed for creating videos from text prompts (text-to-video) or images and text prompts (image-to-video). While Vertex AI Studio (specifically Media Studio) offers a UI for generation, programmatic access via the API is necessary for building automated tools.

### 1.2. Prerequisites
Before starting, ensure the following are set up:
* **Google Cloud Platform (GCP) Project**: An active project with billing enabled.
* **Vertex AI API Enabled**: In your GCP project, enable the "Vertex AI API".
* **Service Account**:
    * Create a service account with the following roles:
        * `Vertex AI User` (roles/aiplatform.user)
        * `Storage Object Admin` (roles/storage.objectAdmin) - for writing video outputs to GCS.
    * Download the JSON key file for this service account.
* **Google Cloud Storage (GCS) Bucket**: A GCS bucket where generated videos will be stored. The service account needs write permissions to this bucket.
* **Model Familiarity**: Identify the target Veo model ID you intend to use (e.g., `veo-2.0-generate-001`). Check the [Veo API Reference](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/veo-video-generation) for the latest available models and their capabilities.
* **(Potentially) Allowlisting**: Some Veo models might be in Preview and require you to request access or be allowlisted.

---

## 2. Understanding the Veo API (REST)

### 2.1. API Endpoint for Initiating Generation
The primary method for initiating video generation is via a `predict` call to the model's endpoint:

* **Method**: `POST`
* **URL Structure**:
    `https://<REGION>-aiplatform.googleapis.com/v1/projects/<PROJECT_ID>/locations/<REGION>/publishers/google/models/<MODEL_ID>:predict`
* **Example URL**:
    `https://us-central1-aiplatform.googleapis.com/v1/projects/your-gcp-project-id/locations/us-central1/publishers/google/models/veo-2.0-generate-001:predict`

    *(Replace `<REGION>`, `<PROJECT_ID>`, and `<MODEL_ID>` with your specific values.)*

### 2.2. Authentication
Requests must be authenticated using a Bearer token obtained from the service account JSON key.

* **Header**: `Authorization: Bearer <ACCESS_TOKEN>`
* **Header**: `Content-Type: application/json`

### 2.3. Request Payload
The body of the POST request defines the video generation parameters.

* **Key Fields**:
    * `instances` (array): Contains the input(s) for generation. For Veo, this is typically a single object.
        * `prompt` (string): The text description for the video.
        * `image_bytes` (string, optional): Base64-encoded image string for image-to-video generation.
    * `parameters` (object): Model-specific configurations.
        * `video_length_sec` (number): Desired length of the video in seconds.
        * `aspect_ratio` (string): e.g., "16:9", "9:16", "1:1".
        * `fps` (number, optional): Frames per second for the output video.
        * `output_gcs_directory` (string): **Crucial.** The GCS path (URI) where the generated video(s) will be saved. Must start with `gs://` and end with a `/`. Example: `gs://your-bucket-name/outputs/my-videos/`
        * `operation_id` (string, optional but recommended): A unique ID for the operation, aiding in idempotency and tracking.

* **Example JSON Body (Text-to-Video)**:
    ```json
    {
      "instances": [
        {
          "prompt": "A futuristic cityscape at sunset with flying cars and neon lights."
        }
      ],
      "parameters": {
        "video_length_sec": 10,
        "aspect_ratio": "16:9",
        "output_gcs_directory": "gs://your-gcs-bucket-name/video_outputs/",
        "operation_id": "unique-video-request-001"
      }
    }
    ```

### 2.4. API Response & Long-Running Operations (LRO)
Video generation is an asynchronous, long-running operation.

1.  **Initial Response**: The `POST` request to the `:predict` endpoint will return an `Operation` object. This response indicates the task has started.
    * **Key field**: `name` (string) - The unique identifier for the LRO. Example: `projects/<PROJECT_NUMBER>/locations/<REGION>/operations/<OPERATION_ID>`

2.  **Polling for Status**: You must poll the status of the LRO using its `name`.
    * **Method**: `GET`
    * **URL Structure**: `https://<REGION>-aiplatform.googleapis.com/v1/<OPERATION_NAME>`
    * **Example URL**: `https://us-central1-aiplatform.googleapis.com/v1/projects/1234567890/locations/us-central1/operations/9876543210987654321`
    * **Authentication**: Same Bearer token.
    * **Response (Pending)**:
        ```json
        {
          "name": "projects/.../operations/...",
          "metadata": {
            "@type": "[type.googleapis.com/google.cloud.aiplatform.v1.GenerateVideoMetadata](https://type.googleapis.com/google.cloud.aiplatform.v1.GenerateVideoMetadata)",
            "progress_message": "Video generation is in progress...",
            // ... other metadata
          },
          "done": false
        }
        ```
    * **Response (Completed Successfully)**:
        ```json
        {
          "name": "projects/.../operations/...",
          "metadata": {
            "@type": "[type.googleapis.com/google.cloud.aiplatform.v1.GenerateVideoMetadata](https://type.googleapis.com/google.cloud.aiplatform.v1.GenerateVideoMetadata)",
            // ... metadata confirming completion
          },
          "done": true,
          "response": {
            "@type": "[type.googleapis.com/google.cloud.aiplatform.v1.GenerateVideoResponse](https://type.googleapis.com/google.cloud.aiplatform.v1.GenerateVideoResponse)"
            // This response field for Veo might be simple acknowledgement,
            // as the primary output is the video file in the GCS directory.
          }
        }
        ```
        The generated video files will be located in the `output_gcs_directory` specified in the initial request. The metadata or response of the completed operation might contain information about the exact filenames, or you may need to list the contents of the GCS directory.

    * **Response (Failed)**:
        ```json
        {
          "name": "projects/.../operations/...",
          "metadata": { ... },
          "done": true,
          "error": {
            "code": <error_code>,
            "message": "<error_message>"
          }
        }
        ```

### 2.5. Prompting
Crafting effective prompts is key. Refer to the [Vertex AI video generation prompt guide](https://cloud.google.com/vertex-ai/generative-ai/docs/video/video-gen-prompt-guide) for detailed best practices.

---

## 3. n8n Integration

This outlines how to build an n8n workflow to call the Veo API.

### 3.1. Setup Google Credentials in n8n
1.  In n8n, go to **Credentials**.
2.  Click **Add credential**.
3.  Search for and select **Google OAuth2 API**.
4.  **Credential Name**: Give it a descriptive name (e.g., `VertexAI-Veo-ServiceAcct`).
5.  **Authentication**: Select `Service Account`.
6.  **Service Account JSON**: Paste the entire content of your downloaded service account JSON key file.
7.  **Scopes**: Add the following scope: `https://www.googleapis.com/auth/cloud-platform`
8.  Click **Save**.

### 3.2. n8n Workflow Steps

**(Start Node: e.g., Manual, Webhook, Schedule)**
   - This node will provide the input data like `prompt`, `gcsOutputPath`, `videoLengthSec`, `aspectRatio`.

**Node 1: Set Node (Prepare Request Body)**
   - Purpose: Construct the JSON payload for the API.
   - Mode: `Define Below`
   - Add expressions to build the body:
     * `instances`: `{{ [{ "prompt": $input.item.json.prompt }] }}`
     * `parameters`:
         ```json
         {
           "video_length_sec": {{ $input.item.json.videoLengthSec || 10 }},
           "aspect_ratio": "{{ $input.item.json.aspectRatio || '16:9' }}",
           "output_gcs_directory": "{{ $input.item.json.gcsOutputPath }}",
           "operation_id": "n8n-{{ $workflow.id }}-{{ $now.toMillis() }}"
         }
         ```

**Node 2: HTTP Request Node (Start Video Generation)**
   - **Method**: `POST`
   - **URL**: `https://<REGION>-aiplatform.googleapis.com/v1/projects/<PROJECT_ID>/locations/<REGION>/publishers/google/models/<MODEL_ID>:predict` (fill in your details)
   - **Authentication**: `OAuth2`
   - **Credential for Google OAuth2 API**: Select the credential you created (e.g., `VertexAI-Veo-ServiceAcct`).
   - **Send Body**: `true`
   - **Body Content Type**: `JSON`
   - **JSON Body**: `{{ $('Set Node Name').item.json }}` (referencing the output of Node 1)
   - **Options**:
     * `Response Format`: `JSON`
     * (Optional) `Ignore SSL Issues`: `false` (unless specific proxy issues)
   - *This node's output will contain the initial `Operation` object, including `name`.*

**Node 3: Wait Node (Initial Delay for LRO)**
   - **Wait Time**: `30` (Units: `Seconds`). Adjust as video generation times can vary.

**(Start of Polling Loop - Conceptual: Use a Loop Over Items node or IF/GoTo for max retries)**
   *A simple loop can be made by connecting the output of a conditional check back to the polling HTTP Request.*
   *Add a counter variable in the workflow if using IF/GoTo to limit retries.*

**Node 4: HTTP Request Node (Check Operation Status)**
   - **Method**: `GET`
   - **URL**: `https://<REGION>-aiplatform.googleapis.com/v1/{{ $('Node 2 HTTP Request').item.json.name }}`
     *(Use an expression to get the `name` from Node 2's output. Replace `<REGION>`.)*
   - **Authentication**: `OAuth2`
   - **Credential for Google OAuth2 API**: Same as Node 2.
   - **Options**:
     * `Response Format`: `JSON`

**Node 5: IF Node (Check if Done)**
   - **Conditions**:
     * Add condition: `{{ $('Node 4 HTTP Request').item.json.done }}` - `Boolean` - `is true`
   - **Output for `true`**: Connect to a node that processes the successful result (e.g., a Set node, or a Google Cloud Storage node).
   - **Output for `false`**: Connect to a "Wait Node" (e.g., 30 seconds), and then connect that Wait Node's output back to "Node 4: HTTP Request (Check Operation Status)" to continue polling. Implement a max retries mechanism to prevent infinite loops (e.g., using a counter with another IF node).

**Node 6: (On IF True Path) Set Node / Process Result**
   - If the operation was successful (`done: true` and no `error` field).
   - The video is in the GCS bucket. You can now use the `output_gcs_directory` (from Node 1 inputs) along with any specific filenames if returned in Node 4's metadata (though often you just know the directory).
   - Example: Set a variable `videoReadyMessage`: `Video generation complete. Check {{ $('Set Node Name').item.json.parameters.output_gcs_directory }}`

**Node 7: (Optional, On IF True Path) Google Cloud Storage Node**
   - Purpose: List files in the output directory to get exact video URIs, or download.
   - **Authentication**: Use a Google Service Account credential (can be the same one used for OAuth2 if you also create a "Google Service Account" credential type in n8n with the JSON key).
   - **Resource**: `File` or `Folder`
   - **Operation**: `List` (to find files in `output_gcs_directory`) or `Download` (if filename is known/derived).

**(Error Handling for Nodes 2 & 4)**
   - Use the "Settings" tab of the HTTP Request nodes to configure "Continue on Fail" if you want to handle errors explicitly with further nodes (e.g., send a notification on failure).

---

## 4. Node.js Integration

### 4.1. Setup
Install necessary Google Cloud client libraries:
```bash
npm install @google-cloud/aiplatform @google-cloud/storage --save
# or
yarn add @google-cloud/aiplatform @google-cloud/storage