{"name": "Customer Support Workflow", "nodes": [{"parameters": {"content": "# 2) Customer Support Workflow\n", "height": 80, "width": 580, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-120, -340], "id": "7bb88475-d8de-476e-803b-30921a97f60e", "name": "<PERSON><PERSON>"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {}, "options": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [-360, -20], "id": "1517509f-455d-44f8-ab03-5a220450c33b", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"inputText": "={{ $json.text }}", "categories": {"categories": [{"category": "Customer Support", "description": "An email that is related to helping out a customer. They may be asking questions about our policies or questions about our products or services."}, {"category": "Other", "description": "Any email that is not customer support related."}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1, "position": [-100, -20], "id": "181c9b3c-c2f3-4a5b-b5ab-435bdc47d26f", "name": "Text Classifier"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-160, 160], "id": "c9a0f456-2dfe-4fdc-b40b-4ae9254a3ece", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "OKbg2dxiNxleXQic", "name": "Demo 4/2"}}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [340, 200], "id": "e750b21f-1fa4-4ada-b428-ba4f146c2a84", "name": "No Operation, do nothing"}, {"parameters": {"promptType": "define", "text": "={{ $('G<PERSON> Trigger').item.json.text }}", "options": {"systemMessage": "=# Overview\nYou are a customer support agent from Tech Haven. Your job is to write responses to incoming emails using your knowledgeBase tool.\n\n## Output\nOnly output the email body\n\n## Instructions\n- Write friendly emails with emojis\n- Sign off as Mr. Helpful from Tech Haven Solutions"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [380, -140], "id": "a20c4dcc-8ed5-4698-99e3-74577549072d", "name": "AI Agent"}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "knowledgeBase", "toolDescription": "Call this tool to access information about Policies and FAQ", "pineconeIndex": {"__rl": true, "value": "sample", "mode": "list", "cachedResultName": "sample"}, "options": {"pineconeNamespace": "FAQ"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [520, 40], "id": "a946a825-8fe7-4905-83e2-f9c2c826eaae", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "87xHLzLON9BYVGHw", "name": "Demo 4/2"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [560, 220], "id": "e4124204-b90a-49f2-b448-eeae828207a3", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "WnxUhaEPMn5hIsEp", "name": "Demo 4/2"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [360, 20], "id": "a6b5a555-b8a6-4b4f-be65-6a0b0f8b3128", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "WnxUhaEPMn5hIsEp", "name": "Demo 4/2"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "labelIds": ["Label_1594706753190197855"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [740, -140], "id": "5ff212e0-7f6b-4ba2-80ab-fabdb6287c58", "name": "Label", "webhookId": "c0c7c2b3-31f9-4cd1-8216-b5862601ddc7", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "emailType": "text", "message": "={{ $json.output }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [940, -140], "id": "7d1f41f3-97c9-4127-8eef-551b2688451a", "name": "Send", "webhookId": "63fe17a2-3eca-4444-9cea-34b80b57ee49", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}], "pinData": {}, "connections": {"Gmail Trigger": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Pinecone Vector Store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Label", "type": "main", "index": 0}]]}, "Label": {"main": [[{"node": "Send", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "86388fd5-b1c1-49b5-b047-419ad75fa5aa", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "xJAfJtLTjPUuZcJi", "tags": []}