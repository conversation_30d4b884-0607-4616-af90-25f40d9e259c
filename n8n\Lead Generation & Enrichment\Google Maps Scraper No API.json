{"nodes": [{"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [-2180, 400], "id": "01041dfa-1e00-4c18-8816-d967385db55c", "name": "Remove Duplicates"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1780, 400], "id": "6bba33a8-d061-4500-8e1f-359ee39eecf4", "name": "Loop Over Items"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-1420, 500], "id": "50d59b40-345d-4bec-9b84-e0da36033126", "name": "Wait", "webhookId": "19cc6ed4-4fe7-485b-b879-c679e4b3374d"}, {"parameters": {"maxItems": 10}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-2000, 400], "id": "351baa70-82b4-4eee-a227-4deba55b1841", "name": "Limit"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-1600, 340], "id": "6ca38cf4-4f33-4b4b-96b5-e3b2689142a6", "name": "Wait1", "webhookId": "0fe34756-6e43-4603-8891-5747a9a6500a"}, {"parameters": {"fieldToSplitOut": "emails", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-1240, 340], "id": "7fa0b55c-9c1e-4805-af8b-513ee4264be4", "name": "Split Out"}, {"parameters": {"jsCode": "const input = $input.first().json.data\nconst regex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.(?!jpeg|jpg|png|gif|webp|svg)[a-zA-Z]{2,}/g\nconst emails = input.match(regex)\nreturn {json: {emails:emails}}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1240, 500], "id": "68636e9c-1cf2-4bce-bdad-4881f79113e0", "name": "Extract Emails", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Get data from the HTTP response\nconst input = $input.first().json.data;\n\n// Extract websites\nconst regex = /https?:\\/\\/[^\\/\\s\"'>]+/g;\nconst websites = input.match(regex) || [];\n\n// Extract business names\nconst businessNameRegex = /<h3 class=[\"'].*?[\"']>(.*?)<\\/h3>/g;\nconst businessNames = [];\nlet match;\nwhile ((match = businessNameRegex.exec(input)) !== null) {\n  businessNames.push(match[1]);\n}\n\n// Extract phone numbers\nconst phoneRegex = /\\+?[\\d\\s-()]{10,20}/g;\nconst phoneNumbers = input.match(phoneRegex) || [];\n\n// Create an array to store the data\nconst result = [];\n\n// Create items with website, business name, and phone number\nfor (let i = 0; i < Math.max(websites.length, businessNames.length, phoneNumbers.length); i++) {\n  result.push({\n    json: {\n      website: websites[i] || '',\n      businessName: businessNames[i] || '',\n      phoneNumber: phoneNumbers[i] || ''\n    }\n  });\n}\n\n// Return the result\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2580, 400], "id": "2a91aa10-edee-46c8-b87f-0055d116921a", "name": "Extract URLs"}, {"parameters": {"url": "={{ `https://www.google.com/maps/search/${encodeURIComponent($node['When chat message received'].json.chatInput)}` }}", "options": {"allowUnauthorizedCerts": true, "response": {"response": {"fullResponse": true}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2780, 400], "id": "2a5a339e-29f5-4e0f-bf20-9cffe44176c1", "name": "Scrape Google Maps"}, {"parameters": {"url": "={{ $json.website }}", "options": {"redirect": {"redirect": {"followRedirects": false}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1600, 500], "id": "05fa5d73-46ee-415f-9639-5c3ce67906fb", "name": "Scrape Site", "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6786c58-424a-409a-b87f-8a7592cb7944", "leftValue": "={{ $json.emails }}", "rightValue": "", "operator": {"type": "array", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-1420, 340], "id": "90601672-326d-4f44-9668-583f6f06c460", "name": "Filter Out Empties"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bf0a5053-9660-457c-9581-964793bb6d7d", "leftValue": "={{ $json.website }}", "rightValue": "schema", "operator": {"type": "string", "operation": "notContains"}}, {"id": "9110b9e0-12aa-45cc-bde0-9eda8c10970e", "leftValue": "={{ $json.website }}", "rightValue": "google", "operator": {"type": "string", "operation": "notContains"}}, {"id": "fb9b6ed6-96a5-4560-ab10-b8a4b9a61a2b", "leftValue": "={{ $json.website }}", "rightValue": "gg", "operator": {"type": "string", "operation": "notContains"}}, {"id": "10500c0b-cdbd-4816-aba3-df60d69845dc", "leftValue": "={{ $json.website }}", "rightValue": "gstatic", "operator": {"type": "string", "operation": "notContains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-2380, 400], "id": "c5237b35-fb60-455c-9a00-f97e9fd6615d", "name": "Filter Google URLs"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [-1040, 340], "id": "71cf5b36-6e3a-4da9-b7c1-f2bc1a3f3638", "name": "Remove Duplicates (2)"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-820, 340], "id": "e8bcb343-b0ff-4c1a-bb76-aebdc38fa974", "name": "Convert to File"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-3020, 400], "id": "23228c1b-71de-4d3f-bb28-c8fb31f26a91", "name": "When chat message received", "webhookId": "74099e61-d45f-4ee3-96cf-ad8daf4d6b11"}], "connections": {"Remove Duplicates": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Wait1", "type": "main", "index": 0}], [{"node": "Scrape Site", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Extract Emails", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Filter Out Empties", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Remove Duplicates (2)", "type": "main", "index": 0}]]}, "Extract Emails": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract URLs": {"main": [[{"node": "Filter Google URLs", "type": "main", "index": 0}]]}, "Scrape Google Maps": {"main": [[{"node": "Extract URLs", "type": "main", "index": 0}]]}, "Scrape Site": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Filter Out Empties": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Filter Google URLs": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates (2)": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[]]}, "When chat message received": {"main": [[{"node": "Scrape Google Maps", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}