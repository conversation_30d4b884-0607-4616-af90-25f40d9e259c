{"nodes": [{"parameters": {"url": "={{ $json.url }}", "options": {"allowUnauthorizedCerts": true, "redirect": {"redirect": {"maxRedirects": 3}}, "response": {"response": {"fullResponse": true}}, "timeout": 10000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [920, 1360], "id": "a01f2c05-3d9d-4e13-9cd1-f5cf78ebe74c", "name": "Scrape Business Site", "onError": "continueRegularOutput"}, {"parameters": {"amount": 2}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1120, 1360], "id": "e5fcab13-a7cc-4d69-a084-27a302a8afae", "name": "Wait", "webhookId": "19cc6ed4-4fe7-485b-b879-c679e4b3374d"}, {"parameters": {"functionCode": "// Simple extractor with minimal regex to avoid JSON issues\nconst item = $input.first();\nlet html = '';\ntry { html = item.json.data || ''; } catch (e) { html = ''; }\n\n// Initialize business data\nconst businessData = {\n  url: item.json.url || '',\n  source: item.json.source || '',\n  business_name: item.json.business_name || '',\n  extracted_at: item.json.extracted_at || new Date().toISOString()\n};\n\n// Extract business name from title\nif (!businessData.business_name) {\n  try {\n    const titleMatch = html.match(/<title>([^<]*)<\\/title>/i);\n    if (titleMatch && titleMatch[1]) {\n      businessData.business_name = titleMatch[1].split('|')[0].trim();\n    }\n  } catch (e) {\n    const urlParts = businessData.url.split('/');\n    businessData.business_name = urlParts[2] || '';\n  }\n}\n\n// Find phone numbers\nconst phones = [];\nconst phonePattern = /(\\+?[0-9]{1,3})?[\\s-]?\\(?[0-9]{3}\\)?[\\s-]?[0-9]{3}[\\s-]?[0-9]{4}/g;\nlet phoneMatch;\nwhile ((phoneMatch = phonePattern.exec(html)) !== null) {\n  phones.push(phoneMatch[0]);\n}\n\n// Clean and deduplicate phones\nconst uniquePhones = [];\nfor (const phone of phones) {\n  if (!uniquePhones.includes(phone)) {\n    uniquePhones.push(phone);\n  }\n}\n\nbusinessData.phone_numbers = uniquePhones;\nif (uniquePhones.length > 0) businessData.phone_1 = uniquePhones[0];\nif (uniquePhones.length > 1) businessData.phone_2 = uniquePhones[1];\n\n// Find email addresses\nconst emails = [];\nconst emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g;\nlet emailMatch;\nwhile ((emailMatch = emailPattern.exec(html)) !== null) {\n  emails.push(emailMatch[0]);\n}\n\n// Clean and deduplicate emails\nconst validEmails = [];\nfor (const email of emails) {\n  if (!validEmails.includes(email) && !email.includes('example.com')) {\n    validEmails.push(email);\n  }\n}\n\nbusinessData.emails = validEmails;\nif (validEmails.length > 0) businessData.email_1 = validEmails[0];\nif (validEmails.length > 1) businessData.email_2 = validEmails[1];\n\n// Extract address fragments\nconst addressFragments = [];\nconst addressIndicators = ['street', 'avenue', 'road', 'boulevard', 'drive', 'lane', 'suite'];\n\nfor (const indicator of addressIndicators) {\n  const pattern = new RegExp('\\\\d+[^<>]{1,50}\\\\s' + indicator, 'gi');\n  let match;\n  while ((match = pattern.exec(html)) !== null) {\n    addressFragments.push(match[0]);\n  }\n}\n\nbusinessData.address_fragments = addressFragments;\nif (addressFragments.length > 0) businessData.address_1 = addressFragments[0];\n\n// Extract social media\nconst socialLinks = [];\nconst domains = ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com'];\n\nfor (const domain of domains) {\n  const pattern = new RegExp('https?:\\\\/\\\\/(?:www\\\\.)?' + domain.replace('.', '\\\\.') + '\\\\/[^\\\\s\\'\"<>)]+', 'gi');\n  let match;\n  while ((match = pattern.exec(html)) !== null) {\n    socialLinks.push(match[0]);\n  }\n}\n\nbusinessData.social_links = socialLinks;\n\n// Add platform-specific URLs\nfor (const link of socialLinks) {\n  if (link.includes('facebook.com')) businessData.facebook_url = link;\n  else if (link.includes('twitter.com')) businessData.twitter_url = link;\n  else if (link.includes('instagram.com')) businessData.instagram_url = link;\n  else if (link.includes('linkedin.com')) businessData.linkedin_url = link;\n}\n\nreturn { json: businessData };"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1320, 1360], "id": "de2507d9-6a8c-45df-9d6d-b2348e3a44e3", "name": "Extract Business Info", "alwaysOutputData": true, "onError": "continueRegularOutput", "notes": "Extracts comprehensive business information: name, phone numbers, emails, addresses, and social media links"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [140, 1180], "id": "ca3c760b-8f8b-496a-8632-3905a1b54825", "name": "When chat message received", "webhookId": "aaaaaaaa-bbbb-4ccc-dddd-eeeeeeeeeeee"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [840, 980], "id": "be08fc03-cbc5-46c4-8c58-e344ad6db8a7", "name": "Remove Duplicates"}, {"parameters": {"options": {"google_domain": "google.com"}}, "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "typeVersion": 1, "position": [560, 1520], "id": "f513710b-2c7f-47f6-990a-378e7496f838", "name": "SerpAPI", "credentials": {"serpApi": {"id": "3jc394WGm0xkTqU2", "name": "SerpAPI account"}}}, {"parameters": {"options": {"systemMessage": "You are a Business Information Extractor Agent that helps find businesses on Facebook, Instagram, and Google Maps around the world using the SerpAPI tool.\n\nSTEPS TO FOLLOW:\n1. When a user messages you, extract the business category or name and location they're looking for\n2. Use the SerpAPI tool by calling it with specific queries like these examples:\n   - Call SerpAP<PERSON> with query: \"coffee shops in [location]\"\n   - Call SerpAPI with query: \"[business name] facebook page [location]\"\n   - Call SerpAPI with query: \"[business name] instagram [location]\"\n   - Call SerpAPI with query: \"[business type] google maps [location]\"\n3. If the user doesn't specify a location, ask them for one or use major cities worldwide\n4. Format ALL business URLs you find into a structured JSON array with the format:\n   [{\"url\": \"https://example.com\", \"source\": \"google\", \"business_name\": \"Example Business\"}]\n\nEXAMPLE CONVERSATION:\nUser: \"Find coffee shops in Tokyo\"\nYou: I'll search for coffee shops in Tokyo using SerpAPI.\n[You call SerpAP<PERSON> with query: \"coffee shops in Tokyo\"]\nYou: Here are the coffee shops I found: [Return JSON array with results]\n\nEXAMPLE FUNCTION CALL:\nYou must use the SerpAPI tool by passing the search query to it. For example, if looking for coffee shops in Paris, call the SerpAPI tool with the query \"coffee shops in Paris\".\n\nIMPORTANT: ALWAYS return your results as a structured JSON array of objects, even if you only find one business. This is critical for the workflow to function properly.\n\nIf the user provides a number, use that as the number of businesses to find. Otherwise, aim for at least 5-10 businesses."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [360, 1180], "id": "fce870e3-ec0f-4744-ba8b-54edccab91de", "name": "AI Agent"}, {"parameters": {"contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [440, 1500], "id": "252e2201-c88e-4496-9988-5e8635900627", "name": "Simple Memory"}, {"parameters": {"values": {"string": [{"name": "inspectionTime", "value": "={{ $now }}"}], "boolean": [{"name": "hasData", "value": "={{ !!$json && !!$json.url }}"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [1020, 980], "id": "1eba3a5f-9a50-4131-8b11-cf52592622f1", "name": "Inspect Data"}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {"temperature": 0.2}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [340, 1380], "id": "56f3d9b4-2cab-45ae-8131-c94add05f782", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "PUE4PqELQgpM61IW", "name": "Groq account"}}}, {"parameters": {"functionCode": "// Simple URL extractor that preserves original data structure\n// Following MarketPilot workflow standards for data handling\n\n// Create a test URL for development purposes\nconst testUrl = {\n  url: 'https://example.com',\n  source: 'test',\n  business_name: 'Example Test Business',\n  extracted_at: new Date().toISOString()\n};\n\n// Handle the input data from AI Agent\ntry {\n  // CASE 1: Already properly formatted URL data\n  if (items && items.length > 0) {\n    const firstItem = items[0];\n    \n    // If data already contains URL property, pass it through\n    if (firstItem.json && firstItem.json.url) {\n      return items;\n    }\n    \n    // Extract from AI output if available\n    if (firstItem.json && firstItem.json.output) {\n      const output = firstItem.json.output;\n      let extractedUrls = [];\n      \n      // Handle string output with possible JSON\n      if (typeof output === 'string') {\n        // Try to parse JSON from string\n        try {\n          // Look for JSON array in text\n          const match = output.match(/\\[\\s*\\{[\\s\\S]*?\\}\\s*\\]/); \n          if (match) {\n            extractedUrls = JSON.parse(match[0]);\n          }\n        } catch (e) {\n          // JSON extraction failed\n        }\n        \n        // If no URLs found via JSON, try regex\n        if (extractedUrls.length === 0) {\n          // Extract URLs using basic regex\n          const urlMatches = output.match(/https?:\\/\\/[^\\s\"'<>)]+/g) || [];\n          \n          extractedUrls = urlMatches.map(url => ({\n            url: url,\n            source: 'extracted',\n            business_name: url.split('/')[2] || 'Unknown',\n            extracted_at: new Date().toISOString()\n          }));\n        }\n      }\n      // Handle array output directly\n      else if (Array.isArray(output)) {\n        extractedUrls = output;\n      }\n      \n      // Filter and validate\n      const validUrls = extractedUrls.filter(item => \n        item && item.url && typeof item.url === 'string'\n      );\n      \n      // Return extracted URLs or test URL if none found\n      return validUrls.length > 0 \n        ? validUrls.map(item => ({ json: item }))\n        : [{ json: testUrl }];\n    }\n  }\n  \n  // Default: return test URL if nothing else worked\n  return [{ json: testUrl }];\n  \n} catch (error) {\n  // Error handling with fallback\n  return [{ json: testUrl }];\n}"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [660, 980], "id": "d787f161-0710-4b41-906f-630bb429537b", "name": "Process URLs"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [980, 1200], "id": "748dfbb1-64d9-41ec-b76b-1d6bbdb0d6f1", "name": "Wait1", "webhookId": "0fe34756-6e43-4603-8891-5747a9a6500a"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6786c58-424a-409a-b87f-8a7592cb7944", "leftValue": "={{ $json.emails }}", "rightValue": "", "operator": {"type": "array", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [1220, 1200], "id": "64b51620-e852-4b3b-82f4-8a953d50509b", "name": "Filter Out Empties"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1700, 1200], "id": "0beffcb5-f184-47a2-b27e-8e09df20a483", "name": "Remove Duplicates (2)"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1880, 1200], "id": "024d539a-200b-4c09-9e91-900a7819de64", "name": "Convert to File1"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [760, 1220], "id": "a6679f12-90f0-4cb1-8284-07aa906bff7b", "name": "Loop Over Items"}, {"parameters": {"options": {"dotNotation": true}}, "type": "n8n-nodes-base.itemLists", "typeVersion": 3, "position": [1480, 1200], "id": "a866e19e-890a-4e3b-8af9-0e669375625f", "name": "Preserve Business Info", "notes": "This node preserves all business information (URLs, names, phones, emails, addresses, etc.) instead of just extracting emails."}], "connections": {"Scrape Business Site": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Extract Business Info", "type": "main", "index": 0}]]}, "Extract Business Info": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Inspect Data", "type": "main", "index": 0}]]}, "SerpAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Process URLs", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Inspect Data": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Process URLs": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Filter Out Empties", "type": "main", "index": 0}]]}, "Filter Out Empties": {"main": [[{"node": "Preserve Business Info", "type": "main", "index": 0}]]}, "Remove Duplicates (2)": {"main": [[{"node": "Convert to File1", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Wait1", "type": "main", "index": 0}], [{"node": "Scrape Business Site", "type": "main", "index": 0}]]}, "Preserve Business Info": {"main": [[{"node": "Remove Duplicates (2)", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}