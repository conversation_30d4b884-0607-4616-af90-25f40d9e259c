{"nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-200, 140], "id": "f0e0d444-31c0-47aa-bb1b-0c9ed93bfe29", "name": "When chat message received", "webhookId": "aaaaaaaa-bbbb-4ccc-dddd-eeeeeeeeeeee"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [380, 140], "id": "eecb1659-f376-4e99-9313-276da085a95a", "name": "Remove Duplicates"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [600, 140], "id": "d056c08e-20a3-4b18-aaa3-aff3871577ec", "name": "Convert to File"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "typeVersion": 1, "position": [240, 340], "id": "422214f6-914a-40ea-a0f1-c99c27e9b60a", "name": "SerpAPI", "credentials": {"serpApi": {"id": "3jc394WGm0xkTqU2", "name": "SerpAPI account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [20, 140], "id": "71bd3a70-f226-4292-8754-8e7c09b39caa", "name": "AI Agent"}, {"parameters": {"contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [120, 360], "id": "3050f7ac-a223-46a7-9d30-b74490d31efa", "name": "Simple Memory"}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [0, 340], "id": "9c0fdf2a-4a75-4fd8-b455-67eb5ea856ee", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "PUE4PqELQgpM61IW", "name": "Groq account"}}}], "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "SerpAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}