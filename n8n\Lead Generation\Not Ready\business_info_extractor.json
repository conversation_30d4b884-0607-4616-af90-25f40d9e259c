{"nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-3000, 400], "id": "a1b2c3d4-e5f6-4a1b-8c2d-abcdef123456", "name": "When chat message received", "webhookId": "aaaaaaaa-bbbb-4ccc-dddd-eeeeeeeeeeee"}, {"parameters": {"url": "", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-2600, 400], "id": "b1c2d3e4-f5a6-4b1c-9d3e-bcdefa234567", "name": "Google Search API"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [-2200, 400], "id": "c1d2e3f4-a5b6-4c1d-0e4f-cdefab345678", "name": "Remove Duplicates"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1800, 400], "id": "d1e2f3a4-b5c6-4d1e-1f5a-defabc456789", "name": "Loop Over Items"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-1400, 400], "id": "e1f2a3b4-c5d6-4e1f-2a6b-efabcd567890", "name": "Wait"}, {"parameters": {"url": "={{ $json.url }}", "options": {"redirect": {"followRedirects": false}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-1000, 400], "id": "f1a2b3c4-d5e6-4f1a-3b7c-fabcde678901", "name": "Scrape Site"}, {"parameters": {"jsCode": "const html = $input.first().json.body;\nconst businessName = html.match(/<h1[^>]*>([^<]+)<\\/h1>/)?.[1] || '';\nconst phone = html.match(/(\\+?\\d[\\d\\-\\s]{8,}\\d)/)?.[1] || '';\nconst email = html.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/)?.[0] || '';\nconst website = html.match(/https?:\\/\\/[^(\\s'\\\")]+/)?.[0] || '';\nreturn { json: { businessName, phone, email, website } };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-600, 400], "id": "a9b8c7d6-e5f4-4a9b-4c8d-bcdef9876543", "name": "Extract Business Info"}, {"parameters": {"conditions": {"conditions": [{"id": "cond1", "leftValue": "={{ $json.businessName }}", "operator": {"type": "string", "operation": "notEqual"}, "rightValue": ""}], "combinator": "and", "options": {"caseSensitive": false, "typeValidation": "strict", "version": 2}}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-200, 400], "id": "b9c8d7e6-a5f4-4b9c-5d7e-cdefed876543", "name": "Filter Out Empties"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [200, 400], "id": "c9d8e7f6-b5a4-4c9d-6e8f-defeab765432", "name": "Remove Duplicates (2)"}, {"parameters": {"options": {"fileFormat": "csv"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [600, 400], "id": "d9e8f7a6-c5b4-4d9e-7f9a-efebcd654321", "name": "Convert to File"}], "connections": {"When chat message received": {"main": [[{"node": "Google Search API", "type": "main", "index": 0}]]}, "Google Search API": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Wait", "type": "main", "index": 0}], [{"node": "Scrape Site", "type": "main", "index": 0}]]}, "Scrape Site": {"main": [[{"node": "Extract Business Info", "type": "main", "index": 0}]]}, "Extract Business Info": {"main": [[{"node": "Filter Out Empties", "type": "main", "index": 0}]]}, "Filter Out Empties": {"main": [[{"node": "Remove Duplicates (2)", "type": "main", "index": 0}]]}, "Remove Duplicates (2)": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": []}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "z9y8x7w6-v5u4-4t9s-8r0p-onmlkjihgfed"}}