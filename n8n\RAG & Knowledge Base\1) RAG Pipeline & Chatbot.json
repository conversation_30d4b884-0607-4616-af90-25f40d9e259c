{"name": "RAG Pipeline & Chatbot", "nodes": [{"parameters": {"content": "# 1) RAG Pipeline & Chatbot", "height": 80, "width": 480, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-180, -320], "id": "bc1be2a2-0153-4a03-b072-c72a53c2c425", "name": "<PERSON><PERSON>"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1FTsF7Hl2fPouUz6mWMh-ZD0kgf-iJfLe", "mode": "list", "cachedResultName": "FAQ", "cachedResultUrl": "https://drive.google.com/drive/folders/1FTsF7Hl2fPouUz6mWMh-ZD0kgf-iJfLe"}, "event": "fileCreated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-300, -100], "id": "c4ff0c70-7093-495b-82d3-a1f8a8de41fe", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "V2ewjiHO0o6xhQ2R", "name": "<EMAIL>"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-80, -100], "id": "423fe340-4990-4435-9ce3-a92da010cba0", "name": "Download File", "credentials": {"googleDriveOAuth2Api": {"id": "V2ewjiHO0o6xhQ2R", "name": "<EMAIL>"}}}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "sample", "mode": "list", "cachedResultName": "sample"}, "options": {"pineconeNamespace": "FAQ"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [140, -100], "id": "da38a30c-8730-4a79-8058-eb00affe6537", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "87xHLzLON9BYVGHw", "name": "Demo 4/2"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [60, 120], "id": "4438c2d6-b2d1-4fa1-98cf-82c105c92c5e", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "WnxUhaEPMn5hIsEp", "name": "Demo 4/2"}}}, {"parameters": {"dataType": "binary", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [280, 120], "id": "a7fea062-ee44-442f-8b9d-9726a6098e43", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [380, 340], "id": "08b9d836-5eb1-42a7-8cc0-30d4d1f6ea5b", "name": "Recursive Character Text Splitter"}, {"parameters": {"options": {"systemMessage": "You are a helpful assistant"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [820, -100], "id": "6693613b-3821-4c43-b0d3-5eb8ba9bffd9", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [660, -100], "id": "dd1ec800-d5c0-49bf-a55d-0c550cfe1250", "name": "When chat message received", "webhookId": "a3171267-791f-4b3b-b499-e03979296b9c"}, {"parameters": {"model": "anthropic/claude-3.5-sonnet", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [860, 120], "id": "cc5bf795-9056-4bf3-a0a3-5cdaf6ab7ab4", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "OKbg2dxiNxleXQic", "name": "Demo 4/2"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "knowledgeBase", "toolDescription": "Call this tool to access the policy and FAQ database", "pineconeIndex": {"__rl": true, "value": "sample", "mode": "list", "cachedResultName": "sample"}, "options": {"pineconeNamespace": "FAQ"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [1040, 120], "id": "e5a2023e-2183-470e-81fe-9f635d54075e", "name": "Pinecone Vector Store1", "credentials": {"pineconeApi": {"id": "87xHLzLON9BYVGHw", "name": "Demo 4/2"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1140, 340], "id": "1083a24d-a84c-4a95-b4dd-b81943c28d0c", "name": "Embeddings OpenAI1", "credentials": {"openAiApi": {"id": "WnxUhaEPMn5hIsEp", "name": "Demo 4/2"}}}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Pinecone Vector Store1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Pinecone Vector Store1", "type": "ai_embedding", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "883cc5ed-5a46-4be8-ab2b-130856864395", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "kJMk9nrFpUJTylN9", "tags": []}