{"name": "Research", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "de240b60-a74c-467f-b499-4df8e3d7ca0b", "name": "When clicking ‘Test workflow’"}, {"parameters": {"assignments": {"assignments": [{"id": "a50e150a-f942-4ac7-8233-0f8e9e0a8057", "name": "Research Term", "value": "Anthropic latest developments", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 0], "id": "4a22636e-318b-4340-8ab6-ad55757e6adb", "name": "Set Search Term"}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR API KEY>"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"sonar\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"Be precise and concise.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json['Research Term'] }}\"\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 0], "id": "90076152-288d-4d1b-808b-884139d867d9", "name": "Perplexity"}, {"parameters": {"content": "# Perplexity API Call\n- Set \"Research Term\"\n- Connect Your Perplexity API Key\n", "height": 460, "width": 840}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-120, -180], "id": "b32b769f-97f3-429c-a343-4a6ebef60792", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Set Search Term", "type": "main", "index": 0}]]}, "Set Search Term": {"main": [[{"node": "Perplexity", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8df7ef85-8300-41aa-9e58-5f272197a09b", "meta": {"instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "4dOBHZtdMlw6r2HF", "tags": []}