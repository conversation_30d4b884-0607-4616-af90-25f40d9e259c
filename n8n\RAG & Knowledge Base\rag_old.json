{"nodes": [{"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').first().json.file_id }}"}, {"name": "file_title", "value": "={{ $('Set File ID').first().json.file_title }}"}]}}}, "id": "7353228b-19a7-452c-9c18-0c1a6338ff51", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [860, 1140]}, {"parameters": {"content": "## Agent Tools for RAG", "height": 528.85546469693, "width": 583.4552380860637, "color": 4}, "id": "3e6868d9-a8d0-4425-9ed7-ef9f14fdd539", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-140, 0]}, {"parameters": {"content": "## Tool to Add a Google Drive File to Vector DB", "height": 867, "width": 3073, "color": 5}, "id": "fced9961-d60e-4d5c-90a1-5b038814886c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1880, 520]}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Set File ID').item.json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "1dfb14b9-d22a-429d-911b-9e12b5894f78", "name": "Download File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-800, 820], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "TGl3biWKK0ipuyJr", "name": "Google Drive (Exo Group)"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1Za7gR8f_RSH1m7mOVC4shf1JaMSxa_zF", "mode": "list", "cachedResultName": "n8n", "cachedResultUrl": "https://drive.google.com/drive/folders/1Za7gR8f_RSH1m7mOVC4shf1JaMSxa_zF"}, "event": "fileCreated", "options": {}}, "id": "c90dae81-7637-4cd7-a417-ca34976feba1", "name": "File Created", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1820, 660], "credentials": {"googleDriveOAuth2Api": {"id": "TGl3biWKK0ipuyJr", "name": "Google Drive (Exo Group)"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1Za7gR8f_RSH1m7mOVC4shf1JaMSxa_zF", "mode": "list", "cachedResultName": "n8n", "cachedResultUrl": "https://drive.google.com/drive/folders/1Za7gR8f_RSH1m7mOVC4shf1JaMSxa_zF"}, "event": "fileUpdated", "options": {}}, "id": "7233f2d0-f027-4df6-818c-0906693bc60c", "name": "File Updated", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1820, 820], "credentials": {"googleDriveOAuth2Api": {"id": "TGl3biWKK0ipuyJr", "name": "Google Drive (Exo Group)"}}}, {"parameters": {"operation": "text", "options": {}}, "id": "2e75f34a-5268-4d95-b9f0-b18c697fe87b", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [60, 1140], "alwaysOutputData": true}, {"parameters": {}, "id": "7bdaf270-e04f-489a-8d34-b8cedad801a7", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-740, 360], "notesInFlow": false, "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $json.file_id }}*"}, "id": "ab3b1df5-0e09-4ded-9c97-ed1a45f2b6bc", "name": "Delete Old Doc Rows", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1280, 660], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "xEcy8wQAa6WTUr14", "name": "Supabase <EMAIL>"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.id }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.mimeType }}", "type": "string"}, {"id": "77d782de-169d-4a46-8a8e-a3831c04d90f", "name": "file_title", "value": "={{ $json.name }}", "type": "string"}, {"id": "9bde4d7f-e4f3-4ebd-9338-dce1350f9eab", "name": "file_url", "value": "={{ $json.webViewLink }}", "type": "string"}]}, "options": {}}, "id": "1cb2b44a-ea2f-4688-8cfb-8e9b104792ea", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1460, 820]}, {"parameters": {"content": "## RAG AI Agent with Cha<PERSON> Interface", "height": 464.8027193303974, "width": 1035.6381264595484}, "id": "f8acf8dd-9afe-4770-90ad-d487fdb226f8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1180, 60]}, {"parameters": {"options": {}}, "id": "373c3971-ebba-44ce-8e5e-6eae15062719", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-320, 140], "disabled": true}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "fcafdd00-1c18-408d-b44d-9aedf8b07214", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-880, 140]}, {"parameters": {"public": true, "options": {}}, "id": "d4e35f9e-df51-4369-b13e-284e4da520ec", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1140, 140], "webhookId": "e104e40e-6134-4825-a6f0-8a646d882662"}, {"parameters": {"httpMethod": "POST", "path": "bf4dd093-bb02-472c-9454-7ab9af97bd1d", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "063e8776-eb84-4544-a65f-659d0bfb603c", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1140, 340], "webhookId": "bf4dd093-bb02-472c-9454-7ab9af97bd1d", "disabled": true}, {"parameters": {"operation": "pdf", "options": {}}, "id": "baf63e33-7868-47fd-8388-8be822ec398a", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [60, 580]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "1b3a9418-5cbb-4bba-ace4-da98fccd9077", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [100, 760]}, {"parameters": {"chunkOverlap": 100}, "id": "8d6d4ff8-f19a-4d33-8e98-f8bbcb37a19e", "name": "Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [760, 1260]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "c99da8ea-321b-4ade-9bdc-fc764f26fae2", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [300, 840]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions from a corpus of documents. The documents are either text based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents).\n\nYou are given tools to perform RAG in the 'documents' table, look up the documents available in your knowledge base in the 'document_metadata' table, extract all the text from a given document, and query the tabular files with SQL in the 'document_rows' table.\n\nAlways start by performing RAG unless the question requires a SQL query for tabular data (fetching a sum, finding a max, something a RAG lookup would be unreliable for). If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those.\n\nAlways tell the user if you didn't find the answer. Don't make something up just to please them."}}, "id": "4d032db9-ba50-4efe-9d85-4796f1f3c253", "name": "RAG AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-660, 140]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "=application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b69f5605-0179-4b02-9a32-e34bb085f82d", "leftValue": "={{ $('Set File ID').item.json.file_type }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": 3}}, "id": "0a9faad9-86a1-4269-9a00-86a6d35d99e5", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [-600, 800]}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "id": "f82d9759-57a0-45c4-9b4b-8efe651b3b9c", "name": "Insert into Supabase Vectorstore", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [780, 920], "credentials": {"supabaseApi": {"id": "xEcy8wQAa6WTUr14", "name": "Supabase <EMAIL>"}}}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "97eee77c-f2b0-4beb-b074-8fddcb5d66c2", "name": "Extract from Excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-120, 760]}, {"parameters": {"assignments": {"assignments": [{"id": "f422e2e0-381c-46ea-8f38-3f58c501d8b9", "name": "schema", "value": "={{ $('Extract from Excel').isExecuted ? $('Extract from Excel').first().json.keys().toJsonString() : $('Extract from CSV').first().json.keys().toJsonString() }}", "type": "string"}, {"id": "bb07c71e-5b60-4795-864c-cc3845b6bc46", "name": "data", "value": "={{ $json.concatenated_data }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [740, 700], "id": "40615e19-9b8e-4784-99fe-f21fb1f72ff0", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-120, 940], "id": "65a40ba0-e4f7-46b7-9cd4-3713c0410f79", "name": "Extract from CSV"}, {"parameters": {"content": "## Run Each Node Once to Set Up Database Tables", "height": 300, "width": 680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-1880, 220], "typeVersion": 1, "id": "d187fe24-2e68-46ac-81a0-6e2c73e21de3", "name": "Sticky Note3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_metadata (\n    id TEXT PRIMARY KEY,\n    title TEXT,\n    url TEXT,\n    created_at TIMESTAMP DEFAULT NOW(),\n    schema TEXT\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1600, 320], "id": "d17c2f17-2cdc-48fc-98ad-09cd08fe5f2b", "name": "Create Document Metadata Table", "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE document_rows (\n    id SERIAL PRIMARY KEY,\n    dataset_id TEXT REFERENCES document_metadata(id),\n    row_data JSONB  -- Store the actual row data\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1400, 320], "id": "fe03ce56-111d-479d-81e3-c6924186d63d", "name": "Create Document Rows Table (for Tabular Data)", "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to fetch all available documents, including the table schema if the file is a CSV or Excel file.", "operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-600, 360], "id": "738ff848-4194-4aa6-bbbe-5100369f7a55", "name": "List Documents", "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Given a file ID, fetches the text from the document.", "operation": "execute<PERSON>uery", "query": "SELECT \n    string_agg(content, ' ') as document_text\nFROM documents\n  WHERE metadata->>'file_id' = $1\nGROUP BY metadata->>'file_id';", "options": {"queryReplacement": "={{ $fromAI('file_id') }}"}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-460, 360], "id": "8ab8492d-423a-4cd2-8a78-0897f8ee4fa5", "name": "Get File Contents", "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Run a SQL query - use this to query from the document_rows table once you know the file ID you are querying. dataset_id is the file_id and you are always using the row_data for filtering, which is a jsonb field that has all the keys from the file schema given in the document_metadata table.\n\nExample query:\n\nSELECT AVG((row_data->>'revenue')::numeric)\nFROM document_rows\nWHERE dataset_id = '123';\n\nExample query 2:\n\nSELECT \n    row_data->>'category' as category,\n    SUM((row_data->>'sales')::numeric) as total_sales\nFROM dataset_rows\nWHERE dataset_id = '123'\nGROUP BY row_data->>'category';", "operation": "execute<PERSON>uery", "query": "{{ $fromAI('sql_query') }}", "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-300, 360], "id": "3baabd59-a252-408c-b909-4f280b362bb7", "name": "Query Document Rows", "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "documents", "toolDescription": "Use RAG to look up information in the knowledgebase.", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [20, 140], "id": "cb674be5-0b9e-48ef-a1a2-9f6dfa8f8eb3", "name": "Supabase Vector Store1", "credentials": {"supabaseApi": {"id": "xEcy8wQAa6WTUr14", "name": "Supabase <EMAIL>"}}}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1640, 660], "id": "fe4165a3-c5d8-4455-aa8f-ead120f448cd", "name": "Loop Over Items"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Enable the pgvector extension to work with embedding vectors\ncreate extension vector;\n\n-- Create a table to store your documents\ncreate table documents (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_documents (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (documents.embedding <=> query_embedding) as similarity\n  from documents\n  where metadata @> filter\n  order by documents.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-1820, 320], "id": "e960bbd7-8b85-4dce-8199-4eb84a79d246", "name": "Create Documents Table and Match Function", "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"operation": "delete", "tableId": "document_rows", "filters": {"conditions": [{"keyName": "dataset_id", "condition": "eq", "keyValue": "={{ $('Set File ID').item.json.file_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1120, 820], "id": "f8629b01-153b-4b08-90db-bbbe5c532cb8", "name": "Delete Old Data Rows", "alwaysOutputData": true, "executeOnce": true, "credentials": {"supabaseApi": {"id": "xEcy8wQAa6WTUr14", "name": "Supabase <EMAIL>"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "title": "={{ $('Set File ID').item.json.file_title }}", "url": "={{ $('Set File ID').item.json.file_url }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [-960, 680], "id": "914c8fb8-1a25-4acd-aefd-cb3fe5706b06", "name": "Insert Document Metadata", "executeOnce": true, "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_rows", "mode": "list", "cachedResultName": "document_rows"}, "columns": {"mappingMode": "defineBelow", "value": {"dataset_id": "={{ $('Set File ID').item.json.file_id }}", "row_data": "={{ $json.toJsonString().replaceAll(/'/g, \"''\") }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "dataset_id", "displayName": "dataset_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_data", "displayName": "row_data", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [100, 940], "id": "6af17d07-707c-48c2-92ce-************", "name": "Insert Table Rows", "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "document_metadata", "mode": "list", "cachedResultName": "document_metadata"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Set File ID').item.json.file_id }}", "schema": "={{ $json.schema }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": true, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "schema", "displayName": "schema", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [960, 700], "id": "3f67ad62-5f7f-4dd5-a2ad-98e90ee10394", "name": "Update <PERSON><PERSON>a for Document Metadata", "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"content": "## 🚀 Ultimate n8n Agentic RAG Template\n\n**Author:** [<PERSON>](https://www.youtube.com/@ColeMedin)\n\n## What is this?\nThis template provides a complete implementation of an **Agentic RAG (Retrieval Augmented Generation)** system in n8n that can be extended easily for your specific use case and knowledge base. Unlike standard RAG which only performs simple lookups, this agent can reason about your knowledge base, self-improve retrieval, and dynamically switch between different tools based on the specific question.\n\n## Why Agentic RAG?\nStandard RAG has significant limitations:\n- Poor analysis of numerical/tabular data\n- Missing context due to document chunking\n- Inability to connect information across documents\n- No dynamic tool selection based on question type\n\n## What makes this template powerful:\n- **Intelligent tool selection**: Switches between RAG lookups, SQL queries, or full document retrieval based on the question\n- **Complete document context**: Accesses entire documents when needed instead of just chunks\n- **Accurate numerical analysis**: Uses SQL for precise calculations on spreadsheet/tabular data\n- **Cross-document insights**: Connects information across your entire knowledge base\n- **Multi-file processing**: Handles multiple documents in a single workflow loop\n- **Efficient storage**: Uses JSONB in Supabase to store tabular data without creating new tables for each CSV\n\n## Getting Started\n1. Run the table creation nodes first to set up your database tables in Supabase\n2. Upload your documents through Google Drive (or swap out for a different file storage solution)\n3. The agent will process them automatically (chunking text, storing tabular data in Supabase)\n4. Start asking questions that leverage the agent's multiple reasoning approaches\n\n## Customization\nThis template provides a solid foundation that you can extend by:\n- Tuning the system prompt for your specific use case\n- Adding document metadata like summaries\n- Implementing more advanced RAG techniques\n- Optimizing for larger knowledge bases\n\n---\n\nI do intend on making a local version of this agent very soon!", "height": 1320, "width": 560, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-2460, 80], "typeVersion": 1, "id": "00c40de7-a404-4ad8-b308-792ff6e50a53", "name": "Sticky Note9"}, {"parameters": {"model": "meta-llama/llama-4-maverick-17b-128e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-880, 360], "id": "e3a9f28c-3ade-4d9e-82a8-31b2c32e0b92", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "PUE4PqELQgpM61IW", "name": "Groq account"}}}, {"parameters": {"modelName": "models/text-embedding-004"}, "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "typeVersion": 1, "position": [120, 360], "id": "1ee954ad-ad36-4c6b-a136-e1f4b0ce57eb", "name": "Embeddings Google Gemini", "credentials": {"googlePalmApi": {"id": "npqTjEkXj4pfPb9E", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/text-embedding-004"}, "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "typeVersion": 1, "position": [600, 1180], "id": "1426eadd-62fb-495b-8d2d-30bad813b36e", "name": "Embeddings Google Gemini1", "credentials": {"googlePalmApi": {"id": "npqTjEkXj4pfPb9E", "name": "Google Gemini(PaLM) Api account"}}}], "connections": {"Default Data Loader": {"ai_document": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_document", "index": 0}]]}, "Download File": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "File Created": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "File Updated": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract Document Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Delete Old Doc Rows": {"main": [[{"node": "Delete Old Data Rows", "type": "main", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Rows", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Webhook": {"main": [[]]}, "Extract PDF Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Summarize": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "Extract from Excel", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "Insert into Supabase Vectorstore": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract from Excel": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Set Schema": {"main": [[{"node": "Update <PERSON><PERSON>a for Document Metadata", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}, {"node": "Insert Table Rows", "type": "main", "index": 0}]]}, "Create Document Metadata Table": {"main": [[{"node": "Create Document Rows Table (for Tabular Data)", "type": "main", "index": 0}]]}, "List Documents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Get File Contents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Query Document Rows": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase Vector Store1": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Set File ID", "type": "main", "index": 0}]]}, "Create Documents Table and Match Function": {"main": [[{"node": "Create Document Metadata Table", "type": "main", "index": 0}]]}, "Delete Old Data Rows": {"main": [[{"node": "Insert Document Metadata", "type": "main", "index": 0}]]}, "Insert Document Metadata": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Embeddings Google Gemini": {"ai_embedding": [[{"node": "Supabase Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Embeddings Google Gemini1": {"ai_embedding": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_embedding", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}