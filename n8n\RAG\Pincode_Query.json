{"name": "Pincode Query", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-180, 0], "id": "5834c652-8b29-4451-a4ce-f6af3f97ab9e", "name": "When chat message received", "webhookId": "65dec1cb-eeed-48ab-914e-d7c2d1cd9ab2", "disabled": true}, {"parameters": {"promptType": "define", "text": "={{ $json.query }}", "options": {"systemMessage": "=Current Time: {{ DateTime.now().setZone('Asia/Kathmandu').format('yyyy-MM-dd HH:mm:ss') }}\n\nUse the retrieval of files tool to find information asked in chat trigger.\n\nCurrency is Rs. \n", "maxIterations": 50}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [220, 0], "id": "3f8de281-cd87-4c08-8a5d-7b39b70960c4", "name": "AI Agent"}, {"parameters": {"model": "meta-llama/llama-4-scout-17b-16e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [120, 200], "id": "d72c3b26-405b-4ba1-a2f2-bb72e2bbe636", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "PUE4PqELQgpM61IW", "name": "Groq account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [260, 220], "id": "cd6bdf1c-11ff-4570-a72b-179c7493469a", "name": "Simple Memory"}, {"parameters": {"description": "A tool used to retrieve documents stored in the user's google drive."}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1.1, "position": [380, 220], "id": "c35111c0-b19e-4f88-aac0-7740dc455d29", "name": "Answer questions with a vector store"}, {"parameters": {"pineconeIndex": {"__rl": true, "value": "n8ngemini", "mode": "list", "cachedResultName": "n8ngemini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [320, 380], "id": "7e72ee77-4312-45d0-9bc1-ad75cde09c2f", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "ZNOpiBHKvXACJITp", "name": "PineconeApi n8n"}}}, {"parameters": {"model": "meta-llama/llama-4-scout-17b-16e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [640, 380], "id": "23144c30-5967-4832-8b10-ae87a1b0f2ab", "name": "Groq Chat Model1", "credentials": {"groqApi": {"id": "PUE4PqELQgpM61IW", "name": "Groq account"}}}, {"parameters": {"modelName": "models/text-embedding-004"}, "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "typeVersion": 1, "position": [460, 560], "id": "9fd6bd55-6c6f-4f9b-90b9-04e2205b07ff", "name": "Embeddings Google Gemini", "credentials": {"googlePalmApi": {"id": "npqTjEkXj4pfPb9E", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"path": "438af313-16a9-44cd-b403-91652177a683", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-180, 180], "id": "********-cae8-4bbd-9899-93f4bc25f0ea", "name": "Webhook", "webhookId": "438af313-16a9-44cd-b403-91652177a683"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [580, 0], "id": "fd12985d-d3e6-43c9-9683-5e6b345a6591", "name": "Respond to Webhook"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Answer questions with a vector store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Pinecone Vector Store": {"ai_vectorStore": [[{"node": "Answer questions with a vector store", "type": "ai_vectorStore", "index": 0}]]}, "Groq Chat Model1": {"ai_languageModel": [[{"node": "Answer questions with a vector store", "type": "ai_languageModel", "index": 0}]]}, "Embeddings Google Gemini": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Webhook": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "531f710c-c446-4583-bdfc-61f811eb3ee9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}, "id": "UveN9XG0t9u3U5kL", "tags": [{"createdAt": "2025-05-17T05:13:50.201Z", "updatedAt": "2025-05-17T05:13:50.201Z", "id": "DFjrtvfj2rJgmmbE", "name": "Pinecode"}, {"createdAt": "2025-05-17T05:13:11.842Z", "updatedAt": "2025-05-17T05:13:11.842Z", "id": "p5IaXwqI0s7NoMjS", "name": "<PERSON><PERSON><PERSON>"}]}