{"nodes": [{"parameters": {"public": true, "options": {}}, "id": "d4e35f9e-df51-4369-b13e-284e4da520ec", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1020, 140], "webhookId": "supabase-rag-agent"}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "fcafdd00-1c18-408d-b44d-9aedf8b07214", "name": "Process Input", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-800, 140]}, {"parameters": {}, "id": "7bdaf270-e04f-489a-8d34-b8cedad801a7", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-440, 320], "notesInFlow": false, "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "queryType": "queryDocuments", "query": "={{ $json.chatInput }}", "customAttributeName": "metadata"}, "id": "2d3f81e9-6b4a-4bf7-a4ff-e7f8ec0eaa0b", "name": "Retrieve Documents", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [-600, 140], "credentials": {"supabaseApi": {"id": "xEcy8wQAa6WTUr14", "name": "Supabase <EMAIL>"}}}, {"parameters": {"functionCode": "// Enhanced query intent analysis for RAG AI Agent\nconst query = items[0].json.chatInput.trim();\nconst queryLower = query.toLowerCase();\n\n// Keywords that suggest a database/SQL query\nconst sqlKeywords = [\n  'select', 'from', 'where', 'group by', 'order by', 'limit', 'offset',\n  'join', 'inner join', 'left join', 'right join', 'full join',\n  'having', 'distinct', 'count', 'sum', 'avg', 'min', 'max',\n  'between', 'like', 'in', 'exists', 'not', 'and', 'or',\n  'table', 'column', 'schema', 'index', 'view', 'function', 'procedure',\n  'insert', 'update', 'delete', 'truncate', 'create', 'alter', 'drop',\n  'with', 'recursive'\n];\n\n// Patterns that indicate a SQL query\nconst sqlPatterns = [\n  /select\\s+.+\\s+from\\s+.+/i,\n  /insert\\s+into\\s+.+\\s+values/i,\n  /update\\s+.+\\s+set\\s+/i,\n  /delete\\s+from\\s+/i,\n  /create\\s+table\\s+/i,\n  /alter\\s+table\\s+/i,\n  /drop\\s+table\\s+/i,\n  /truncate\\s+table\\s+/i\n];\n\n// Check if the query contains SQL keywords\nconst hasSqlKeywords = sqlKeywords.some(keyword => \n  new RegExp(`\\\\b${keyword}\\\\b`, 'i').test(query)\n);\n\n// Check if the query matches SQL patterns\nconst matchesSqlPattern = sqlPatterns.some(pattern => pattern.test(query));\n\n// Check if this is a request to run a SQL query\nconst isExplicitSqlRequest = /(run|execute|write)\\s+(a\\s+)?(sql\\s+)?query/i.test(queryLower);\n\n// Check if this is a question about the database schema\nconst isSchemaQuestion = /(what|which|show|list|describe|explain)\\s+(the\\s+)?(database|table|schema|column|index)/i.test(queryLower);\n\n// Extract SQL query if it's embedded in the message\nlet extractedSql = null;\nif (hasSqlKeywords || matchesSqlPattern) {\n  // Try to find SQL between backticks first\n  const backtickMatch = query.match(/```(?:sql)?\\s*([\\s\\S]*?)\\s*```/i);\n  if (backtickMatch) {\n    extractedSql = backtickMatch[1].trim();\n  } else {\n    // Otherwise, try to extract the SQL directly\n    const sqlMatch = query.match(/(select|insert|update|delete|create|alter|drop|truncate|with)[^;]*(;|$)/i);\n    if (sqlMatch) {\n      extractedSql = sqlMatch[0].trim();\n    }\n  }\n}\n\n// Determine if we should execute SQL\nconst shouldExecuteSql = (isExplicitSqlRequest || hasSqlKeywords || matchesSqlPattern) && extractedSql;\n\n// Determine if we need schema context\nconst needsSchemaContext = isSchemaQuestion || shouldExecuteSql;\n\n// Log the analysis for debugging\nconsole.log('Query Analysis:', {\n  originalQuery: query,\n  hasSqlKeywords,\n  matchesSqlPattern,\n  isExplicitSqlRequest,\n  isSchemaQuestion,\n  extractedSql,\n  shouldExecuteSql,\n  needsSchemaContext\n});\n\nreturn [\n  {\n    json: {\n      ...items[0].json,\n      isSchemaQuestion,\n      isSqlRequest: isExplicitSqlRequest || hasSqlKeywords || matchesSqlPattern,\n      extractedSql,\n      needsSchemaContext,\n      shouldExecuteSql,\n      queryAnalysis: {\n        hasSqlKeywords,\n        matchesSqlPattern,\n        isExplicitSqlRequest,\n        isSchemaQuestion\n      }\n    }\n  }\n];"}, "id": "b18c1df7-c44a-469d-ae98-55ec1e686e7c", "name": "Analyze Query In<PERSON>t", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-440, 140]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.shouldExecuteSql }}", "value2": true}]}}, "id": "28aa33bc-d8c4-4a2f-9d4d-c9c0dbf36e39", "name": "Should Execute SQL?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-240, 140]}, {"parameters": {"functionCode": "// Extract and prepare the SQL query for execution\nconst query = items[0].json.extractedSql || items[0].json.chatInput;\n\nreturn [\n  {\n    json: {\n      ...items[0].json,\n      query\n    }\n  }\n];"}, "id": "9ce79e46-dc27-4b9e-a80c-eab3dce6eba0", "name": "Prepare SQL Query", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-40, 60]}, {"parameters": {"functionCode": "// Enhanced SQL Query Validation and Sanitization\nconst { query, extractedSql } = items[0].json;\nconst sqlQuery = extractedSql || query;\n\n// Simple validation to prevent harmful operations\nconst validationResult = validateQuery(sqlQuery);\n\nif (!validationResult.valid) {\n  return [\n    {\n      json: {\n        ...items[0].json,\n        sqlResult: {\n          success: false,\n          error: validationResult.reason,\n          message: \"SQL query validation failed\",\n          original_query: sqlQuery,\n          timestamp: new Date().toISOString()\n        },\n        // Ensure we don't proceed with SQL execution\n        shouldExecuteSql: false\n      }\n    }\n  ];\n}\n\n// If validation passes, prepare the sanitized query\nreturn [\n  {\n    json: {\n      ...items[0].json,\n      sqlResult: {\n        query: validationResult.sanitizedQuery,\n        original_query: sqlQuery,\n        isReadOnly: validationResult.isReadOnly,\n        requiresSchema: validationResult.requiresSchema,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }\n];\n\n/**\n * Validates and sanitizes SQL queries\n * @param {string} query - The SQL query to validate\n * @returns {Object} Validation result with sanitized query and metadata\n */\nfunction validateQuery(query) {\n  if (!query || typeof query !== 'string') {\n    return {\n      valid: false,\n      reason: 'Invalid query: must be a non-empty string'\n    };\n  }\n\n  const lowerQuery = query.toLowerCase().trim();\n  const isSelectQuery = lowerQuery.startsWith('select');\n  \n  // Blocked operations (case-insensitive check)\n  const blockedOperations = [\n    { pattern: /drop\\s+(table|database|schema|index|view|function|procedure)\\s+/i, reason: 'DROP operations are not allowed' },\n    { pattern: /truncate\\s+table\\s+/i, reason: 'TRUNCATE operations are not allowed' },\n    { pattern: /delete\\s+from\\s+/i, reason: 'DELETE operations are not allowed' },\n    { pattern: /alter\\s+table\\s+/i, reason: 'ALTER TABLE operations are not allowed' },\n    { pattern: /create\\s+(table|index|view|function|procedure)\\s+/i, reason: 'CREATE operations are not allowed' },\n    { pattern: /insert\\s+into\\s+/i, reason: 'INSERT operations are not allowed' },\n    { pattern: /update\\s+[^\\s]+\\s+set\\s+/i, reason: 'UPDATE operations are not allowed' },\n    { pattern: /grant\\s+/i, reason: 'GRANT operations are not allowed' },\n    { pattern: /revoke\\s+/i, reason: 'REVOKE operations are not allowed' },\n    { pattern: /\\b(exec|execute|sp_executesql)\\s*\\(/i, reason: 'Dynamic SQL execution is not allowed' }\n  ];\n\n  // Check for blocked operations\n  for (const { pattern, reason } of blockedOperations) {\n    if (pattern.test(query)) {\n      return { valid: false, reason }\n    }\n  }\n\n  // Check for potential SQL injection patterns\n  const injectionPatterns = [\n    /--|\\/\\*|\\*\\/|;\\s*--|;\\s*$|\\\\\\\\/\\*.*\\*\\\\//i,\n    /(?:union|select\\s+[^\\s]+\\s+from\\s+[^\\s]+\\s+where\\s+[^\\s]+\\s*=\\s*[^\\s]+\\s*)\\s*--/i\n  ];\n\n  for (const pattern of injectionPatterns) {\n    if (pattern.test(query)) {\n      return {\n        valid: false,\n        reason: 'Query contains potentially dangerous SQL patterns'\n      };\n    }\n  }\n\n  // Add LIMIT clause to SELECT queries if not present\n  let sanitizedQuery = query;\n  if (isSelectQuery && !/\\blimit\\s+\\d+\\s*(,\\s*\\d+\\s*)?$/i.test(lowerQuery)) {\n    // Check if there's an ORDER BY clause to preserve it\n    if (lowerQuery.includes('order by')) {\n      const orderByIndex = lowerQuery.lastIndexOf('order by');\n      const beforeOrderBy = query.substring(0, orderByIndex);\n      const orderByAndAfter = query.substring(orderByIndex);\n      sanitizedQuery = `${beforeOrderBy} ${orderByAndAfter} LIMIT 100`;\n    } else {\n      sanitizedQuery = `${query} LIMIT 100`;\n    }\n  }\n\n  // Determine if the query requires schema information\n  const requiresSchema = /\\b(?:from|join|into|update)\\s+[^\\s(]+/i.test(lowerQuery);\n\n  return {\n    valid: true,\n    sanitizedQuery,\n    isReadOnly: isSelectQuery,\n    requiresSchema,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "e5a21439-badb-4c38-bef3-efaf55efbb85", "name": "Validate SQL Query", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [140, 60]}, {"parameters": {"operation": "execute", "query": "={{ $json.sqlResult && $json.sqlResult.query }}", "additionalFields": {}}, "id": "1b8a2ea2-a7a2-45d4-96a1-b33923f28b9f", "name": "Execute SQL Query", "type": "n8n-nodes-base.postgres", "typeVersion": 2.2, "position": [340, 60], "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"functionCode": "// Format SQL query results for the agent\n\n// Check if there was a validation error\nif (items[0].json.sqlResult && items[0].json.sqlResult.success === false) {\n  const errorMessage = `SQL Query Error: ${items[0].json.sqlResult.error}`;\n  return [\n    {\n      json: {\n        ...items[0].json,\n        sqlQueryContent: errorMessage\n      }\n    }\n  ];\n}\n\n// Format successful query results\nconst queryResults = items[0].json;\nlet resultCount = 0;\nlet resultText = \"\";\n\n// Check if we have results and they're an array\nif (Array.isArray(queryResults) && queryResults.length > 0) {\n  resultCount = queryResults.length;\n  \n  // Convert results to a readable format\n  if (resultCount > 0) {\n    // Get column names from the first row\n    const columns = Object.keys(queryResults[0]);\n    \n    // Create a markdown table header\n    resultText += \"| \" + columns.join(\" | \") + \" |\\n\";\n    resultText += \"| \" + columns.map(() => \"---\").join(\" | \") + \" |\\n\";\n    \n    // Add data rows (maximum 20 rows for readability)\n    const displayRows = queryResults.slice(0, 20);\n    displayRows.forEach(row => {\n      resultText += \"| \" + columns.map(col => String(row[col] !== null ? row[col] : \"\")).join(\" | \") + \" |\\n\";\n    });\n    \n    // Add row count information\n    resultText += `\\n\\n*Query returned ${resultCount} ${resultCount === 1 ? \"row\" : \"rows\"}${resultCount > 20 ? \" (showing first 20)\" : \"\"}*`;\n  } else {\n    resultText = \"The query executed successfully but returned no results.\";\n  }\n} else if (typeof queryResults === 'object' && !Array.isArray(queryResults)) {\n  // Handle non-array results\n  resultText = \"Query executed successfully with the following result:\\n\\n\";\n  resultText += JSON.stringify(queryResults, null, 2);\n} else {\n  resultText = \"The query executed successfully but returned no results or an empty dataset.\";\n}\n\n// Store the original query for context\nconst originalQuery = items[0].json.sqlResult ? items[0].json.sqlResult.original_query : items[0].json.query;\n\nreturn [\n  {\n    json: {\n      ...items[0].json,\n      sqlQueryContent: `SQL Query: \\`${originalQuery}\\`\\n\\nResults:\\n${resultText}`\n    }\n  }\n];"}, "id": "53d4c8a7-eac0-47c3-94bb-5eebe04f051e", "name": "Format SQL Results", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [540, 60]}, {"parameters": {"operation": "select", "tableId": "information_schema.tables", "filterString": "table_schema = 'public'", "returnFields": ["table_name", "table_type"], "options": {"rowCount": 100}}, "id": "97f26c09-c6e3-4d10-ad4a-6e3dbe76e0fa", "name": "List All Tables", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-40, 220], "credentials": {"supabaseApi": {"id": "xEcy8wQAa6WTUr14", "name": "Supabase <EMAIL>"}}}, {"parameters": {"functionCode": "// Format database schema information for the agent\n\n// Get tables\nconst tables = items[0].json;\n\n// Format table list\nlet schemaText = \"## Database Schema Information\\n\\n\";\nschemaText += \"### Available Tables\\n\\n\";\n\nif (Array.isArray(tables) && tables.length > 0) {\n  schemaText += \"| Table Name | Type |\\n| --- | --- |\\n\";\n  tables.forEach(table => {\n    schemaText += `| ${table.table_name} | ${table.table_type} |\\n`;\n  });\n} else {\n  schemaText += \"No tables found in the database.\\n\";\n}\n\nreturn [\n  {\n    json: {\n      ...items[0].json,\n      schemaInformation: schemaText\n    }\n  }\n];"}, "id": "42c71f44-6f45-414c-a25c-d7c5b5c8c453", "name": "Format Schema Info", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [140, 220]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.needsSchemaContext }}", "value2": true}]}}, "id": "fdd57a70-60b5-403c-8193-8c73abe29ec2", "name": "Needs Schema Context?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [340, 220]}, {"parameters": {"functionCode": "// Prepare all context for the RAG agent\n\n// Get database-related context if it exists\nconst databaseContext = items[0].json.schemaInformation || '';\n\n// Get SQL query results if they exist\nconst sqlResults = items[0].json.sqlQueryContent || '';\n\n// Format all content for the agent\nlet agentContext = '';\n\n// Add database schema information if available\nif (databaseContext) {\n  agentContext += `${databaseContext}\\n\\n`;\n}\n\n// Add SQL query results if available\nif (sqlResults) {\n  agentContext += `${sqlResults}\\n\\n`;\n}\n\n// Add document context information\nlet documentContext = '';\nif (items[0].json.documents && Array.isArray(items[0].json.documents) && items[0].json.documents.length > 0) {\n  documentContext = items[0].json.documents.map(doc => doc.pageContent).join('\\n\\n');\n  \n  if (documentContext) {\n    agentContext += `## Relevant Documents\\n\\n${documentContext}`;\n  }\n}\n\nreturn [\n  {\n    json: {\n      ...items[0].json,\n      agentContext\n    }\n  }\n];"}, "id": "f7cad21c-5e0d-44b0-bb52-47d21d9ef27e", "name": "Prepare Agent Context", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 140]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions about a database and its contents using both RAG (Retrieval Augmented Generation) and SQL capabilities. You have access to information about the database schema, document contents, and can execute SQL queries to retrieve data directly from the database.\n\nWhen responding to user queries:\n\n1. For general knowledge questions or questions about documents in the system, use the relevant document context provided.\n\n2. If the query relates to database schema, tables, or structure, refer to the database schema information provided.\n\n3. When the query requires retrieving specific data or performing calculations on database data, analyze the SQL query results provided or suggest a possible SQL query the user could run.\n\n4. If you need more information to answer accurately, explain what additional details would help.\n\nAlways be clear about the source of your information - whether it comes from document content, database schema, or SQL query results. Include relevant code snippets, SQL queries, or data excerpts when helpful.\n\nContext information is provided below:\n\n{{ $json.agentContext }}"}}, "id": "4d032db9-ba50-4efe-9d85-4796f1f3c253", "name": "Supabase RAG Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [940, 140]}, {"parameters": {"content": "## Enhanced RAG Agent with Database Integration\n\nThis workflow provides a chatbot interface that combines:\n1. Document retrieval from vector database (RAG)\n2. Database schema understanding\n3. SQL query execution capabilities\n\nThe agent can answer questions about documents, database structure, and execute SQL queries to retrieve data.", "height": 534.3671149479392, "width": 2208.367114947939}, "id": "f8acf8dd-9afe-4770-90ad-d487fdb226f8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1040, -20]}], "connections": {"When chat message received": {"main": [[{"node": "Process Input", "type": "main", "index": 0}]]}, "Process Input": {"main": [[{"node": "Retrieve Documents", "type": "main", "index": 0}]]}, "Retrieve Documents": {"main": [[{"node": "Analyze Query In<PERSON>t", "type": "main", "index": 0}]]}, "Analyze Query Intent": {"main": [[{"node": "Should Execute SQL?", "type": "main", "index": 0}]]}, "Should Execute SQL?": {"main": [[{"node": "Prepare SQL Query", "type": "main", "index": 0}], [{"node": "List All Tables", "type": "main", "index": 0}]]}, "Prepare SQL Query": {"main": [[{"node": "Validate SQL Query", "type": "main", "index": 0}]]}, "Validate SQL Query": {"main": [[{"node": "Execute SQL Query", "type": "main", "index": 0}]]}, "Execute SQL Query": {"main": [[{"node": "Format SQL Results", "type": "main", "index": 0}]]}, "Format SQL Results": {"main": [[{"node": "Prepare Agent Context", "type": "main", "index": 0}]]}, "List All Tables": {"main": [[{"node": "Format Schema Info", "type": "main", "index": 0}]]}, "Format Schema Info": {"main": [[{"node": "Needs Schema Context?", "type": "main", "index": 0}]]}, "Needs Schema Context?": {"main": [[{"node": "Prepare Agent Context", "type": "main", "index": 0}], [{"node": "Prepare Agent Context", "type": "main", "index": 0}]]}, "Prepare Agent Context": {"main": [[{"node": "Supabase RAG Agent", "type": "main", "index": 0}]]}}}