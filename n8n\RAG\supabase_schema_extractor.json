{"nodes": [{"parameters": {"rule": {"interval": [{"field": "days", "minutesInterval": 1, "triggerAtMinutes": [0]}]}}, "id": "845aa9d7-ca21-45c5-9d88-f00cf12da31c", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [-760, 300]}, {"parameters": {"operation": "select", "tableId": "pg_catalog.pg_tables", "filterString": "schemaname = 'public'", "returnFields": ["tablename"], "options": {}}, "id": "35dd95ed-0c6c-4f93-987c-77a7eb9da5e3", "name": "Supabase - Get Tables", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-540, 300], "credentials": {"supabaseApi": {"id": "xEcy8wQAa6WTUr14", "name": "Supabase <EMAIL>"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "5e2a6d53-f33c-45cc-8246-87d76546da0c", "name": "SplitInBatches", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 2, "position": [-320, 300]}, {"parameters": {"operation": "execute", "query": "=SELECT column_name, data_type, character_maximum_length, column_default, is_nullable\nFROM information_schema.columns\nWHERE table_name = '{{$json.tablename}}' \nAND table_schema = 'public'\nORDER BY ordinal_position;", "additionalFields": {}}, "id": "9e46c6b2-ec6f-4eec-b1e7-f3a8cd4cf2e3", "name": "Supabase - Get Columns", "type": "n8n-nodes-base.postgres", "typeVersion": 2.2, "position": [-120, 300], "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"functionCode": "// Table schema description generator\nlet tableName = items[0].json.tablename;\nlet columns = items[0].json.map(col => {\n  let constraints = [];\n  if (col.is_nullable === 'NO') constraints.push('NOT NULL');\n  if (col.column_default) constraints.push(`DEFAULT ${col.column_default}`);\n  let dataType = col.data_type;\n  if (col.character_maximum_length) {\n    dataType += `(${col.character_maximum_length})`;\n  }\n  \n  return `${col.column_name} ${dataType} ${constraints.join(' ')}`;\n});\n\n// Create a detailed description of the table\nlet tableDescription = `Table: ${tableName}\\n\\n`;\ntableDescription += `Columns:\\n${columns.map(col => `- ${col}`).join('\\n')}\\n\\n`;\ntableDescription += `Description: This table contains data related to ${tableName.replace(/_/g, ' ')}. `;\ntableDescription += `It includes ${columns.length} columns and is part of the application's data model.`;\n\nreturn [{\n  json: {\n    table_name: tableName,\n    schema_description: tableDescription,\n    metadata: {\n      table_name: tableName,\n      type: 'database_schema'\n    }\n  }\n}];"}, "id": "0bbcc7f9-e73a-4e0f-9b8c-a48bd07edd20", "name": "Format Schema Description", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [80, 300]}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.schema_description }}", "options": {"metadata": {"metadataValues": [{"name": "table_name", "value": "={{ $json.table_name }}"}, {"name": "content_type", "value": "database_schema"}]}}}, "id": "9f74d4e0-9d99-4a51-bc61-a9da50a31ae3", "name": "Schema Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [280, 300]}, {"parameters": {"chunkOverlap": 100}, "id": "c1e6b3a0-6c70-4e64-8bad-8f1ee37a0bae", "name": "Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [480, 300]}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "id": "a0b34156-5b89-4ae0-9ad6-aa8ef5ac9f9e", "name": "Supabase Vectorstore - Insert Schemas", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [680, 300], "credentials": {"supabaseApi": {"id": "xEcy8wQAa6WTUr14", "name": "Supabase <EMAIL>"}}}, {"parameters": {"content": "## Database Schema Extraction and Vectorization\n\nThis workflow extracts schema information from all tables in your Supabase database and adds it to your vector store. It runs on a schedule to keep schema information up to date.", "height": 429.8746467331611, "width": 1658.8746467331612}, "id": "f0e9bf73-7e5b-4e04-a564-f9f3da1b0151", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-780, 140]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Supabase - Get Tables", "type": "main", "index": 0}]]}, "Supabase - Get Tables": {"main": [[{"node": "SplitInBatches", "type": "main", "index": 0}]]}, "SplitInBatches": {"main": [[{"node": "Supabase - Get Columns", "type": "main", "index": 0}]]}, "Supabase - Get Columns": {"main": [[{"node": "Format Schema Description", "type": "main", "index": 0}]]}, "Format Schema Description": {"main": [[{"node": "Schema Data Loader", "type": "main", "index": 0}]]}, "Schema Data Loader": {"main": [[{"node": "Character Text Splitter", "type": "main", "index": 0}]]}, "Character Text Splitter": {"main": [[{"node": "Supabase Vectorstore - Insert Schemas", "type": "main", "index": 0}]]}}}