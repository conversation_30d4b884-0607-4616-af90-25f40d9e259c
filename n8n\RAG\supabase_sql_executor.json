{"nodes": [{"parameters": {"functionCode": "// SQL Query validation and execution function\n// This ensures SQL queries are secure before execution\n\n// Get the SQL query from input\nconst sqlQuery = items[0].json.query;\n\n// Simple validation to prevent harmful operations\nconst validationResult = validateQuery(sqlQuery);\n\nif (!validationResult.valid) {\n  return [\n    {\n      json: {\n        success: false,\n        error: validationResult.reason,\n        message: \"SQL query validation failed\",\n        original_query: sqlQuery\n      }\n    }\n  ];\n}\n\n// If validation passes, return the sanitized query for execution\nreturn [\n  {\n    json: {\n      success: true,\n      query: validationResult.sanitizedQuery,\n      original_query: sqlQuery\n    }\n  }\n];\n\nfunction validateQuery(query) {\n  // Convert to lowercase for easier checks\n  const lowerQuery = query.toLowerCase();\n  \n  // Block potentially harmful operations\n  const blockedOperations = [\n    'drop table',\n    'drop database',\n    'truncate table',\n    'delete from',\n    'alter table',\n    'create table',\n    'insert into',\n    'update'\n  ];\n  \n  for (const op of blockedOperations) {\n    if (lowerQuery.includes(op)) {\n      return {\n        valid: false,\n        reason: `Operation '${op}' is not allowed for security reasons`\n      };\n    }\n  }\n  \n  // Apply query limits for safety\n  let sanitizedQuery = query;\n  \n  // Add LIMIT if not present to prevent large result sets\n  if (!lowerQuery.includes('limit') && lowerQuery.includes('select')) {\n    sanitizedQuery += ' LIMIT 1000';\n  }\n  \n  return {\n    valid: true,\n    sanitizedQuery\n  };\n}"}, "id": "e5a21439-badb-4c38-bef3-efaf55efbb85", "name": "Validate SQL Query", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-240, 420]}, {"parameters": {"operation": "execute", "query": "={{ $json.success ? $json.query : 'SELECT 1 WHERE false' }}", "additionalFields": {}}, "id": "1b8a2ea2-a7a2-45d4-96a1-b33923f28b9f", "name": "Execute SQL Query", "type": "n8n-nodes-base.postgres", "typeVersion": 2.2, "position": [-20, 420], "credentials": {"postgres": {"id": "ukDmbL8g5lWhMGd4", "name": "Postgres account"}}}, {"parameters": {"functionCode": "// Format SQL query results\n\n// Check if there was a validation error\nif (!items[0].json.success) {\n  return [\n    {\n      json: {\n        result: \"Error: \" + items[0].json.error,\n        query: items[0].json.original_query,\n        success: false\n      }\n    }\n  ];\n}\n\n// Format successful query results\nconst queryResults = items[0].json;\nlet resultCount = 0;\nlet resultText = \"\";\n\n// Check if we have results\nif (Array.isArray(queryResults) && queryResults.length > 0) {\n  resultCount = queryResults.length;\n  \n  // Convert results to a readable format\n  if (resultCount > 0) {\n    // Get column names from the first row\n    const columns = Object.keys(queryResults[0]);\n    \n    // Create a markdown table header\n    resultText += \"| \" + columns.join(\" | \") + \" |\\n\";\n    resultText += \"| \" + columns.map(() => \"---\").join(\" | \") + \" |\\n\";\n    \n    // Add data rows\n    queryResults.forEach(row => {\n      resultText += \"| \" + columns.map(col => String(row[col] || \"\")).join(\" | \") + \" |\\n\";\n    });\n    \n    // Add row count information\n    resultText += `\\n\\n*Query returned ${resultCount} ${resultCount === 1 ? \"row\" : \"rows\"}*`;\n  } else {\n    resultText = \"The query executed successfully but returned no results.\";\n  }\n} else {\n  resultText = \"The query executed successfully but returned no results or an empty dataset.\";\n}\n\nreturn [\n  {\n    json: {\n      result: resultText,\n      query: items[0].json.original_query,\n      success: true,\n      row_count: resultCount\n    }\n  }\n];"}, "id": "53d4c8a7-eac0-47c3-94bb-5eebe04f051e", "name": "Format SQL Results", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [200, 420]}, {"parameters": {"content": "## SQL Query Execution Workflow\n\nThis workflow safely executes SQL queries against your Supabase database. It includes validation to prevent harmful operations and formats results for readability.", "height": 419.0889510465309, "width": 639.0889510465311}, "id": "f86d5fa5-d2bd-47cc-a2d7-95be2abc9c17", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-260, 260]}], "connections": {"Validate SQL Query": {"main": [[{"node": "Execute SQL Query", "type": "main", "index": 0}]]}, "Execute SQL Query": {"main": [[{"node": "Format SQL Results", "type": "main", "index": 0}]]}}}