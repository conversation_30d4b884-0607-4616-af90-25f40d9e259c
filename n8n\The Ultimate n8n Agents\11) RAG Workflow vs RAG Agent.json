{"name": "RAG Workflow vs. RAG Agent", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "eb690ce9-e043-472c-8ba1-d54d365d6d17", "name": "When clicking ‘Test workflow’"}, {"parameters": {"operation": "download", "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [220, 0], "id": "16120c14-207c-46eb-b03a-5d891695f4c4", "name": "Download File", "credentials": {"googleDriveOAuth2Api": {"id": "60zdCK3Sx2Shlbb4", "name": "Google Drive account"}}}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "sample", "mode": "list", "cachedResultName": "sample"}, "options": {"pineconeNamespace": ""}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [440, 0], "id": "e57a79b3-42ad-436f-9127-7bbde8bf680d", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "kRQGVexSgzWhzJz2", "name": "PineconeApi account 4"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [360, 220], "id": "e65c6e98-3839-4624-98df-686d0b4a34c8", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"dataType": "binary", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [520, 220], "id": "7752d12d-acc0-405d-8624-27f1329a7de8", "name": "Default Data Loader"}, {"parameters": {"chunkSize": 500, "chunkOverlap": 50, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [520, 400], "id": "ec510b1b-9e0d-48a7-87bf-f69b27cd0a4c", "name": "Recursive Character Text Splitter"}, {"parameters": {"content": "# Vectorize Document (binary)", "height": 680, "width": 980}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-80, -120], "id": "cd6f58ec-9716-4d6c-b1c1-70021fcca4b7", "name": "<PERSON><PERSON>"}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1060, 240], "id": "1213b83a-6127-4c7d-aee7-c954655ef589", "name": "2.0 Flash", "credentials": {"googlePalmApi": {"id": "DW8owDXDeMHnr1rA", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {"sender": "<EMAIL>"}, "options": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [1020, 20], "id": "fc290f91-b199-44c0-9c49-4f1d2da027c1", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "knowledge_base", "toolDescription": "Use this tool to search the knowledge base to answer the user's query.", "pineconeIndex": {"__rl": true, "value": "sample", "mode": "list", "cachedResultName": "sample"}, "options": {"pineconeNamespace": ""}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [1220, 240], "id": "7119b1e7-6049-4f1c-8d5a-c8cf9674789e", "name": "Pinecone", "credentials": {"pineconeApi": {"id": "kRQGVexSgzWhzJz2", "name": "PineconeApi account 4"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1220, 400], "id": "5f27b73a-9951-453d-a536-020b30915308", "name": "Embeddings OpenAI1", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"content": "# Customer Support Agent\n", "height": 680, "width": 820, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [920, -120], "id": "c66f3c2a-d5e5-42e5-81df-95e523af616b", "name": "Sticky Note1"}, {"parameters": {"promptType": "define", "text": "=Message ID: {{ $json.id }}\n\nIncomgin Email: {{ $json.text }}", "options": {"systemMessage": "=# Overview\nYou are Mister Helpful, a customer support representative for Tech Haven. Your job is to respond to incoming customer emails with accurate information from the knowledge base.\n\n## Tools\nknowledge_base: Use this tool to search the knowledge base to get information to answer the customer's question\nReply Email: Use this tool to respond to the incoming email.\n\n## Tone\n- You are friendly and helpful. \n- You like to use emojis when applicable.\n- Sign off as \"Mister Helpful | Tech Haven\""}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1240, 20], "id": "3f5ca5c8-0098-451d-99ed-dde6e1fe9487", "name": "Customer Support Agent"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {"sender": "<EMAIL>"}, "options": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [20, 760], "id": "b14e20bb-b6cf-431c-9628-06baf9a401a5", "name": "Gmail Trigger1", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}, "disabled": true}, {"parameters": {"mode": "load", "pineconeIndex": {"__rl": true, "value": "sample", "mode": "list", "cachedResultName": "sample"}, "prompt": "={{ $json.text }}", "options": {"pineconeNamespace": ""}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [280, 700], "id": "4028fc83-eea8-489f-a49f-5892bc52135c", "name": "Search Knowledge Base", "credentials": {"pineconeApi": {"id": "kRQGVexSgzWhzJz2", "name": "PineconeApi account 4"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [260, 860], "id": "f0f92f25-3b45-455f-bb02-eed4865d631c", "name": "Embeddings OpenAI2", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "=Customer Inquiry: {{ $('Gmail Trigger1').item.json.text }}\n\nRelevant Knowledge: {{ $json.pageContent.join() }}"}, {"content": "=# Overview\nYou are Mister Helpful, a customer support representative for Tech Haven. Your job is to respond to incoming customer emails with accurate information from the knowledge base.\n\n## Rules\nYou must only answer using the relevant knowledge provided to you. Do not make up any information.\n\n## Tone\n- You are friendly and helpful. \n- You like to use emojis when applicable.\n- Sign off as \"Mister Helpful | Tech Haven\"\n\n## Output\nOutput only the email body in a clean format", "role": "system"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1080, 700], "id": "92322ecb-34ca-4cd3-ba65-b2a3c154c193", "name": "Write Email", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7c1eb62e-1913-44c3-891b-4b9258fcff1d", "leftValue": "={{ $json.score }}", "rightValue": 0.4, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [660, 760], "id": "9a7ccf48-c6a3-43a9-8479-7e4593f58472", "name": "Filter"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "document.pageContent"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [840, 760], "id": "fc9cffa0-d2e5-4a8f-8177-9d2fda937042", "name": "Aggregate"}, {"parameters": {"operation": "reply", "messageId": "={{ $('Gmail Trigger1').item.json.id }}", "emailType": "text", "message": "={{ $json.message.content }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1500, 760], "id": "45d41f13-a93c-4457-a306-d5cb2b625998", "name": "Reply to Customer", "webhookId": "5d4f41e8-1f64-41af-948e-ff8ca75b74f9", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"content": "# Customer Support AI Workflow", "height": 400, "width": 1820, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-80, 580], "id": "0bbdc300-9a64-48e9-ab89-030c7edcaae6", "name": "Sticky Note2"}, {"parameters": {"operation": "reply", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', `The message ID to reply to`, 'string') }}", "emailType": "text", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1540, 240], "id": "********-bd1a-4e99-a0bb-0c3d6c38cfcc", "name": "<PERSON><PERSON>", "webhookId": "ebb784d8-5760-4a48-a64f-b20933ea689a", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"content": "# <PERSON> | AI Automations", "height": 80, "width": 520, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [500, -260], "id": "d589e6fa-edb6-4a0d-9d6e-7c2a24dee8a6", "name": "Sticky Note3"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "2.0 Flash": {"ai_languageModel": [[{"node": "Customer Support Agent", "type": "ai_languageModel", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "Customer Support Agent", "type": "main", "index": 0}]]}, "Pinecone": {"ai_tool": [[{"node": "Customer Support Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Pinecone", "type": "ai_embedding", "index": 0}]]}, "Gmail Trigger1": {"main": [[{"node": "Search Knowledge Base", "type": "main", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Search Knowledge Base", "type": "ai_embedding", "index": 0}]]}, "Search Knowledge Base": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Write Email", "type": "main", "index": 0}]]}, "Write Email": {"main": [[{"node": "Reply to Customer", "type": "main", "index": 0}]]}, "Reply Email": {"ai_tool": [[{"node": "Customer Support Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c8011c9c-10eb-46bf-95e3-47e9ddff8c15", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "epiRR2ok8YJp5aF6", "tags": []}