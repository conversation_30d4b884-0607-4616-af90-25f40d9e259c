{"name": "Technical Analyst Agent vs. Workflow", "nodes": [{"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [340, 40], "id": "3f6333a9-9912-446d-9589-ef666d15615c", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"workflowInputs": {"values": [{"name": "ticker"}, {"name": "exchange"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-220, 400], "id": "981cd274-6f30-44dd-8883-ea1355f91abc", "name": "When Executed by Another Workflow"}, {"parameters": {"name": "chart", "description": "Call this tool to analyze a stock ticker"}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [580, 40], "id": "707fe129-da79-44f1-a0b5-dca4703c33e9", "name": "chart"}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [260, 400], "id": "14d0a851-3251-4149-9b9f-447548cdf6f0", "name": "Download Chart"}, {"parameters": {"method": "POST", "url": "https://api.chart-img.com/v2/tradingview/advanced-chart/storage", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"theme\": \"dark\",\n  \"interval\": \"1W\",\n  \"symbol\": \"{{ $json.exchange }}:{{ $json.ticker }}\",\n  \"override\": {\n    \"showStudyLastValue\": false\n  },\n  \"studies\": [\n    {\n      \"name\": \"Volume\",\n      \"forceOverlay\": true\n    },\n    {\n      \"name\": \"MACD\",\n      \"override\": {\n        \"Signal.linewidth\": 2,\n        \"Signal.color\": \"rgb(255,65,129)\"\n      }\n    }\n  ]\n}", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, 400], "id": "3f0d2657-4347-4653-8466-2a85aa3019a7", "name": "Get Chart", "credentials": {"httpHeaderAuth": {"id": "4drLLAbDm8nvnIUj", "name": "chart-img"}}}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "text": "=# Role\n\nYou are an expert financial analyst specializing in technical analysis of stock charts. Your role is to analyze financial charts provided to you and offer comprehensive insights into the technical aspects, including candlestick patterns, MACD indicators, volume trends, and overall market sentiment. You must provide a detailed breakdown of the chart, highlighting key areas of interest and actionable insights.\n\nWhen analyzing a stock chart, always include the following:\n\n1. **Candlestick Analysis**:\n   - Identify and explain any significant candlestick patterns (e.g., bullish engulfing, doji, hammer).\n   - Comment on the overall trend (bullish, bearish, or sideways).\n   - Highlight any breakout or pullback zones.\n\n2. **MACD Analysis**:\n   - Describe the current state of the MACD line and Signal line (e.g., bullish crossover, bearish crossover).\n   - Discuss the MACD histogram and its implications for momentum.\n   - Identify any divergences between the MACD and the price action.\n\n3. **Volume Analysis**:\n   - Highlight any significant changes in trading volume.\n   - Explain how volume supports or contradicts price movements.\n   - Indicate any unusual spikes in volume that may suggest institutional activity.\n\n4. **Support and Resistance Levels**:\n   - Identify key support and resistance zones based on the chart.\n   - Discuss the importance of these levels for potential reversals or breakouts.\n\n5. **Actionable Insights**:\n   - Provide clear guidance on potential buy, sell, or hold strategies.\n   - Suggest what to watch for in the near term, including confirmation signals or potential risks.\n\n6. **Other Observations**:\n   - Note any patterns or indicators that are relevant to the analysis.\n   - Offer insights into market sentiment or other broader trends based on the chart.\n\nBe clear, concise, and data-driven in your analysis. Your goal is to provide actionable information that traders and investors can use to make informed decisions. Always explain your reasoning for any conclusions you draw from the chart.\n", "inputType": "base64", "simplify": false, "options": {"maxTokens": 600}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [480, 400], "id": "ab83f84b-5a51-43cd-ae37-d48e48726bbe", "name": "OpenAI", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"operation": "sendPhoto", "file": "={{ $('Download Chart').item.json.url }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [700, 400], "id": "27900f60-22f9-490c-9a69-a15ab2498c09", "name": "Send Chart", "webhookId": "f1ad89bd-1aaa-4576-bb09-e35425346903", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "16f1c1df-4262-40fe-bfd1-47cc6c611605", "name": "response", "value": "={{ $('OpenAI').item.json.choices[0].message.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [920, 400], "id": "20c6a058-ca44-489a-a72b-627ab7222403", "name": "Response"}, {"parameters": {"promptType": "define", "text": "={{ $json.message.text }}", "hasOutputParser": true, "options": {"systemMessage": "=# Overview\nYou are a technical analyst. You will receive a stock ticker, you need to call the 'chart' tool to analyze the stock\n\n## Tool\nchart - you must send the ticker and the exchange that it's traded on to this tool"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [400, -200], "id": "b171d874-e8a0-4063-b874-e25df2d6218e", "name": "Technical Analyst Agent"}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [200, -200], "id": "c6061439-355c-457a-b0a2-46eeddb884b8", "name": "<PERSON>eg<PERSON>", "webhookId": "dbf7f0b7-5cdd-45a3-8c91-39f0665aba76", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}, "disabled": true}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [760, -200], "id": "d30fa380-394b-4d01-8597-0f14c55b9da8", "name": "Send Analysis", "webhookId": "10e5a74f-04fe-4c69-ac91-98623b7f5e7f", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"content": "# Technical Analyst Agent", "height": 500, "width": 1560, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-360, -300], "id": "153062a6-6b37-44d6-a2da-793ace8691a2", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Get Chart Workflow\n", "height": 500, "width": 1560, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-360, 220], "id": "eda911a2-46e1-427b-8c35-d23b11440b4f", "name": "Sticky Note1"}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-240, 920], "id": "00dc5428-c042-4c3d-8d64-093da157c0f3", "name": "Telegram Trigger1", "webhookId": "dbf7f0b7-5cdd-45a3-8c91-39f0665aba76", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}, "disabled": true}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "=Incoming query: {{ $json.message.text }}"}, {"content": "=# Overview\nYou are a stock expert. Your goal is to take the incoming query and output the stock ticker as well as the exchange that the stock is traded on.\n\n## Output\nOutput the following parameters separately:\n- ticker\n- exchange", "role": "system"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-80, 920], "id": "79a4743a-f15b-4347-acb2-bbda8cebe48f", "name": "Get Ticker & Exchange", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"method": "POST", "url": "https://api.chart-img.com/v2/tradingview/advanced-chart/storage", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"theme\": \"dark\",\n  \"interval\": \"1W\",\n  \"symbol\": \"{{ $json.message.content.exchange }}:{{ $json.message.content.ticker }}\",\n  \"override\": {\n    \"showStudyLastValue\": false\n  },\n  \"studies\": [\n    {\n      \"name\": \"Volume\",\n      \"forceOverlay\": true\n    },\n    {\n      \"name\": \"MACD\",\n      \"override\": {\n        \"Signal.linewidth\": 2,\n        \"Signal.color\": \"rgb(255,65,129)\"\n      }\n    }\n  ]\n}", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [260, 920], "id": "185431b6-5dfa-4dcb-8c9f-32ba4c465b04", "name": "Get Chart1", "credentials": {"httpHeaderAuth": {"id": "4drLLAbDm8nvnIUj", "name": "chart-img"}}}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 920], "id": "dd87b07b-82ca-45a2-bb81-bc560525dc0d", "name": "Download Chart1"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "text": "=# Role\n\nYou are an expert financial analyst specializing in technical analysis of stock charts. Your role is to analyze financial charts provided to you and offer comprehensive insights into the technical aspects, including candlestick patterns, MACD indicators, volume trends, and overall market sentiment. You must provide a detailed breakdown of the chart, highlighting key areas of interest and actionable insights.\n\nWhen analyzing a stock chart, always include the following:\n\n1. **Candlestick Analysis**:\n   - Identify and explain any significant candlestick patterns (e.g., bullish engulfing, doji, hammer).\n   - Comment on the overall trend (bullish, bearish, or sideways).\n   - Highlight any breakout or pullback zones.\n\n2. **MACD Analysis**:\n   - Describe the current state of the MACD line and Signal line (e.g., bullish crossover, bearish crossover).\n   - Discuss the MACD histogram and its implications for momentum.\n   - Identify any divergences between the MACD and the price action.\n\n3. **Volume Analysis**:\n   - Highlight any significant changes in trading volume.\n   - Explain how volume supports or contradicts price movements.\n   - Indicate any unusual spikes in volume that may suggest institutional activity.\n\n4. **Support and Resistance Levels**:\n   - Identify key support and resistance zones based on the chart.\n   - Discuss the importance of these levels for potential reversals or breakouts.\n\n5. **Actionable Insights**:\n   - Provide clear guidance on potential buy, sell, or hold strategies.\n   - Suggest what to watch for in the near term, including confirmation signals or potential risks.\n\n6. **Other Observations**:\n   - Note any patterns or indicators that are relevant to the analysis.\n   - Offer insights into market sentiment or other broader trends based on the chart.\n\nBe clear, concise, and data-driven in your analysis. Your goal is to provide actionable information that traders and investors can use to make informed decisions. Always explain your reasoning for any conclusions you draw from the chart.\n", "inputType": "base64", "simplify": false, "options": {"maxTokens": 600}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [620, 920], "id": "bb52ee2e-0d38-471b-b045-7095f1b315c4", "name": "Analyze Chart", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"operation": "sendPhoto", "chatId": "={{ $('Telegram Trigger1').item.json.message.from.id }}", "file": "={{ $('Download Chart1').item.json.url }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [800, 920], "id": "4b086511-9f21-4df2-96f3-d9903ff5116b", "name": "Send Chart1", "webhookId": "f1ad89bd-1aaa-4576-bb09-e35425346903", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $('Telegram Trigger1').item.json.message.from.id }}", "text": "={{ $('Analyze Chart').item.json.choices[0].message.content }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [980, 920], "id": "********-6485-470b-bce0-3882235f3a8e", "name": "Send Analysis1", "webhookId": "cc3a526b-d2be-48d3-a7fe-d30a2a8b0126", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"content": "# Technical Analysis AI Workflow", "height": 460, "width": 1560}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-360, 740], "id": "a4179826-1c0c-4c1c-bf81-638e5de61e0d", "name": "Sticky Note2"}, {"parameters": {"content": "## Must Configure to pass over Ticker and Exchange to the correct workflow", "height": 140, "width": 340}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [720, 20], "id": "76a00afc-acc4-48d9-b9de-1dc35ad4104b", "name": "Sticky Note3"}, {"parameters": {"content": "# <PERSON> | AI Automation", "height": 80, "width": 500, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [180, -440], "id": "21b0c0a7-6d4b-48c3-a7a3-95096a7fd7a6", "name": "Sticky Note4"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Technical Analyst Agent", "type": "ai_languageModel", "index": 0}]]}, "chart": {"ai_tool": [[{"node": "Technical Analyst Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Get Chart", "type": "main", "index": 0}]]}, "Get Chart": {"main": [[{"node": "Download Chart", "type": "main", "index": 0}]]}, "Download Chart": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Send Chart", "type": "main", "index": 0}]]}, "Send Chart": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Technical Analyst Agent": {"main": [[{"node": "Send Analysis", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Technical Analyst Agent", "type": "main", "index": 0}]]}, "Telegram Trigger1": {"main": [[{"node": "Get Ticker & Exchange", "type": "main", "index": 0}]]}, "Get Ticker & Exchange": {"main": [[{"node": "Get Chart1", "type": "main", "index": 0}]]}, "Get Chart1": {"main": [[{"node": "Download Chart1", "type": "main", "index": 0}]]}, "Download Chart1": {"main": [[{"node": "Analyze Chart", "type": "main", "index": 0}]]}, "Analyze Chart": {"main": [[{"node": "Send Chart1", "type": "main", "index": 0}]]}, "Send Chart1": {"main": [[{"node": "Send Analysis1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "2f3eafaa-326a-4b21-8a51-a9f45b6498f0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "LzqXt4fBNVaJtRg8", "tags": []}