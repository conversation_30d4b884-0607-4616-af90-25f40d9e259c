{"name": "Supabase/Postgres", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [180, -180], "id": "34b3244a-865c-47f5-8db9-02d38f531901", "name": "When chat message received", "webhookId": "679e356b-fcc3-4abc-ab59-8ca4ce2cc616"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [240, 40], "id": "565e20d1-c18a-48d6-b7e8-3431bea7aec5", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [400, 40], "id": "6e97eeec-0e1f-43d4-a5b7-902d2219591b", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "XXlsqGiV3cjmW1Pt", "name": "Demo 2.22.25"}}}, {"parameters": {"mode": "retrieve-as-tool", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [540, 40], "id": "08a0d280-7e7b-46f5-afb9-90e2a53d222a", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "r1eLu64ie9Tz6yOK", "name": "Demo 2.22.25"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [420, 680], "id": "1382c5f8-d9f7-428f-89bc-ab0db46786a2", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"dataType": "binary", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [620, 680], "id": "73cb37de-efff-4ad8-9cc9-0d3efd32d3de", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [720, 900], "id": "a5cabb53-afdc-4690-944a-a9b5aaa354c8", "name": "Recursive Character Text Splitter"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [20, 460], "id": "69d569c6-96b2-49e4-90d2-6012342bf7df", "name": "When clicking ‘Test workflow’"}, {"parameters": {"mode": "insert", "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [480, 460], "id": "6260736e-d12c-4fcb-9932-a1a59fd5ce10", "name": "Add to Supabase", "credentials": {"supabaseApi": {"id": "r1eLu64ie9Tz6yOK", "name": "Demo 2.22.25"}}}, {"parameters": {"operation": "download", "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [260, 460], "id": "7f635cef-8dbd-4199-8add-1d74a41d8c13", "name": "Download File", "credentials": {"googleDriveOAuth2Api": {"id": "60zdCK3Sx2Shlbb4", "name": "Google Drive account"}}}, {"parameters": {"options": {"systemMessage": "You are a helpful assistant"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [400, -180], "id": "403e74be-55e9-41a3-abdc-0440fe4db6e6", "name": "RAG Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [600, 200], "id": "60f415b8-d70d-484b-a85a-a251fcf5d22f", "name": "Embeddings", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"content": "# Loading Binary Data from Google Drive", "height": 760, "width": 1140}, "type": "n8n-nodes-base.stickyNote", "position": [-120, 340], "typeVersion": 1, "id": "226db82e-0ad5-45f8-87c2-b1d16e900eb2", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# RAG Agent with Memory\n", "height": 660, "width": 1140, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-120, -320], "typeVersion": 1, "id": "711de3d3-90ee-489b-900b-1ddb31699f12", "name": "Sticky Note1"}, {"parameters": {"content": "# <PERSON> | AI Automation", "height": 100, "width": 500, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [220, -460], "typeVersion": 1, "id": "3fe7aa5f-709d-48d6-ac79-c15d06ff427a", "name": "Sticky Note2"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "RAG Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "RAG Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG Agent", "type": "ai_memory", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "RAG Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Add to Supabase", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Add to Supabase", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Add to Supabase", "type": "main", "index": 0}]]}, "Embeddings": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a35134c3-6ad7-42f0-99de-92358c20b49d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "UoNKMhAdg0Bh95sZ", "tags": []}