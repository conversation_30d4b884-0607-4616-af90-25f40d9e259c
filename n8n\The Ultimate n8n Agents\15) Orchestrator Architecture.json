{"name": "When to use a multi-agent system", "nodes": [{"parameters": {"sendTo": "={{ $fromAI(\"emailAddress\") }}", "subject": "={{ $fromAI(\"subject\") }}", "message": "={{ $fromAI(\"emailBody\") }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-400, 320], "id": "aaaf098b-1be3-404d-b244-7591154aad15", "name": "Send Email", "webhookId": "86c8c4b1-13bb-4ebe-acb9-30e1d7082d55", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"operation": "getAll", "limit": "={{ $fromAI(\"limit\",\"how many emails the user wants\") }}", "simple": false, "filters": {"sender": "={{ $fromAI(\"sender\",\"who the emails are from\") }}"}, "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-280, 460], "id": "a44ba6d1-b5bb-4c8c-8ca9-6a7b35a3cab4", "name": "Get Emails", "webhookId": "af4b3298-9037-44b0-aa12-2acbfbb5e66f", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"resource": "draft", "subject": "={{ $fromAI(\"subject\") }}", "emailType": "html", "message": "={{ $fromAI(\"emailBody\") }}", "options": {"threadId": "={{ $fromAI(\"threadID\") }}", "sendTo": "={{ $fromAI(\"emailAddress\") }}"}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-400, 460], "id": "ce3a2b44-a8ee-4d3a-8275-ac22a9abfc08", "name": "Create Draft", "webhookId": "17016bce-d7d7-428a-a56c-f6ea122db8be", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $fromAI(\"ID\",\"the message ID\") }}", "message": "={{ $fromAI(\"emailBody\") }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-280, 320], "id": "404e2d40-d836-4670-918f-b182ad3f870f", "name": "Email Reply", "webhookId": "114785e6-a859-432b-81b4-c490c1c35b1c", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"resource": "label", "returnAll": true}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-160, 460], "id": "4d796eaa-9aa6-411a-87b8-2a0935c02585", "name": "Get Labels", "webhookId": "9e08b59e-792d-4566-83f1-9263c9ad86ae", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $fromAI(\"ID\",\"the ID of the message\") }}", "labelIds": "={{ $fromAI(\"labelID\") }}"}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-160, 320], "id": "932de9e4-750f-40d6-86e3-1b4fde0897c9", "name": "Label Emails", "webhookId": "0e951529-2e6d-40bf-ac40-fc0947e242e2", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"operation": "mark<PERSON><PERSON>n<PERSON>", "messageId": "={{ $fromAI(\"messageID\") }}"}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-520, 460], "id": "d9af99de-7795-4515-9dc0-5e2923916980", "name": "<PERSON>", "webhookId": "a35af9d8-f67d-4ff9-803f-59ec6356e795", "credentials": {"gmailOAuth2": {"id": "KY7391f1ZHRL52Nu", "name": "Demo 4/2"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-520, 320], "id": "31cfb976-854b-4a4f-8cc3-798033d7a495", "name": "4.1-mini", "credentials": {"openAiApi": {"id": "WnxUhaEPMn5hIsEp", "name": "Demo 4/2"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.query }}", "options": {"systemMessage": "=# Overview\nYou are an email management assistant. All emails must be formatted professionally in HTML and signed off as \"Nate.\" \n\n**Email Management Tools**   \n   - Use \"Send Email\" to send emails.  \n   - Use \"Create Draft\" if the user asks for a draft.  \n   - Use \"Get Emails\" to retrieve emails when requested.\n   - Use \"Get Labels\" to retrieve labels.\n   - Use \"Mark Unread\" to mark an email as unread. You must use \"Get Emails\" first so you have the message ID of the email to flag.\n   - Use \"Label Email\" to flag an email. You must use \"Get Emails\" first so you have the message ID of the email to flag. Then you must use \"Get Labels\" so you have the label ID.\n   - Use \"Email Reply\" to reply to an email. You must use \"Get Emails\" first so you have the message ID of the email to reply to.\n\n## Final Notes\n- Here is the current date/time: {{ $now }}"}}, "id": "6a9ab835-465b-45b2-8d18-054236ba8203", "name": "Email Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-440, 120], "onError": "continueErrorOutput"}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ $fromAI(\"eventStart\") }}", "end": "={{ $fromAI(\"eventEnd\") }}", "additionalFields": {"attendees": ["={{ $fromAI(\"eventAttendeeEmail\") }}"], "summary": "={{ $fromAI(\"eventTitle\") }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [320, 460], "id": "cf38614f-03ee-40c6-bcde-58113720b4b7", "name": "Create Event with <PERSON><PERSON><PERSON>", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ $fromAI(\"eventStart\") }}", "end": "={{ $fromAI(\"eventEnd\") }}", "additionalFields": {"attendees": [], "summary": "={{ $fromAI(\"eventTitle\") }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [200, 460], "id": "faab2339-9c12-40af-8239-54183e583e46", "name": "Create Event", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "timeMin": "={{ $fromAI(\"dayBefore\",\"the day before the date the user requested\") }}", "timeMax": "={{ $fromAI(\"dayAfter\",\"the day after the date the user requested\") }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [80, 460], "id": "e7e5238e-3c96-4037-b156-24c7ad0fddb1", "name": "Get Events", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"operation": "delete", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ $fromAI(\"eventID\") }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [320, 320], "id": "f7dd304d-4e35-4ebe-adb5-83a0d9a12209", "name": "Delete Event", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"operation": "update", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ $fromAI(\"eventID\") }}", "updateFields": {"end": "={{ $fromAI(\"endTime\") }}", "start": "={{ $fromAI(\"startTime\") }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [200, 320], "id": "c103fa1a-b420-4033-995c-409ae475797a", "name": "Update Event", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.query }}", "options": {"systemMessage": "=# Overview\nYou are a calendar assistant. Your responsibilities include creating, getting, and deleting events in the user's calendar.\n\n**Calendar Management Tools**  \n   - Use \"Create Event with Attendee\" when an event includes a participant.  \n   - Use \"Create Event\" for solo events.   \n   - Use \"Get Events\" to fetch calendar schedules when requested.\n   - Use \"Delete Event\" to delete an event. You must use \"Get Events\" first to get the ID of the event to delete.\n   - Use \"Update Event\" to update an event. You must use \"Get Events\" first to get the ID of the event to update.\n\n## Final Notes\nHere is the current date/time: {{ $now }}\nIf a duration for an event isn't specified, assume it will be one hour."}}, "id": "c6361416-9c1c-4f2c-bd24-28d88f8ef134", "name": "Calendar Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [100, 120], "onError": "continueErrorOutput"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [80, 320], "id": "97455289-21bc-44a7-9d14-8b819fbbf0e9", "name": "4.1-mini1", "credentials": {"openAiApi": {"id": "WnxUhaEPMn5hIsEp", "name": "Demo 4/2"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appK0rbtvf9e7vt6w", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://airtable.com/appK0rbtvf9e7vt6w"}, "table": {"__rl": true, "value": "tbl08JGCfUK1RhXsG", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://airtable.com/appK0rbtvf9e7vt6w/tbl08JGCfUK1RhXsG"}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [760, 340], "id": "11399dfe-2a54-475c-b963-e5e00d464bad", "name": "Get Contacts", "credentials": {"airtableTokenApi": {"id": "UlAGE0msyITVkoCN", "name": "<PERSON>"}}}, {"parameters": {"operation": "upsert", "base": {"__rl": true, "value": "appK0rbtvf9e7vt6w", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://airtable.com/appK0rbtvf9e7vt6w"}, "table": {"__rl": true, "value": "tbl08JGCfUK1RhXsG", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://airtable.com/appK0rbtvf9e7vt6w/tbl08JGCfUK1RhXsG"}, "columns": {"mappingMode": "defineBelow", "value": {"name": "={{ $fromAI(\"name\") }}", "email": "={{ $fromAI(\"emailAddress\") }}", "phoneNumber": "={{ $fromAI(\"phoneNumber\") }}"}, "matchingColumns": ["name"], "schema": [{"id": "name", "displayName": "name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "phoneNumber", "displayName": "phoneNumber", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [880, 340], "id": "45b4691f-fa57-4c1e-97c0-b2e8c6467462", "name": "Add or Update Contact", "credentials": {"airtableTokenApi": {"id": "UlAGE0msyITVkoCN", "name": "<PERSON>"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.query }}", "options": {"systemMessage": "=# Overview\nYou are a contact management assistant. Your responsibilities include looking up contacts, adding new contacts, or updating a contact's information.\n\n**Contact Management**  \n   - Use \"Get Contacts\" to retrieve contact information. \n   - Use \"Add or Update Contact\" to store new contact information or modify existing entries. "}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [660, 120], "id": "ab3c2824-d2b4-4af9-93a2-45f782bbbcfc", "name": "Contact Agent1", "onError": "continueErrorOutput"}, {"parameters": {"toolDescription": "Use this tool to search the internet", "method": "POST", "url": "https://api.tavily.com/search", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"api_key\": \"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\",\n    \"query\": \"{searchTerm}\",\n    \"search_depth\": \"basic\",\n    \"include_answer\": true,\n    \"topic\": \"news\",\n    \"include_raw_content\": true,\n    \"max_results\": 3\n} ", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user has requested to write a blog about", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1360, 340], "id": "44a49b58-6cea-486d-a547-d61828d97930", "name": "Tavily1"}, {"parameters": {"promptType": "define", "text": "={{ $json.query}}", "options": {"systemMessage": "=# Overview\nYou are a skilled AI blog writer specializing in engaging, well-structured, and informative content. Your writing style is clear, compelling, and tailored to the target audience. You optimize for readability, SEO, and value, ensuring blogs are well-researched, original, and free of fluff.\n\n## Tools\nTavily - Use this to search the web about the requested topic for the blog post.\n\n## Blog Requirements\nFormat all blog content in HTML, using proper headings (<h1>, <h2>), paragraphs (<p>), bullet points (<ul><li>), and links (<a href=\"URL\">) for citations. All citations from the Tavily tool must be preserved, with clickable hyperlinks so readers can access the original sources.\n\nMaintain a natural, human-like tone, use varied sentence structures, and include relevant examples or data when needed. Structure content for easy reading with concise paragraphs and logical flow. Always ensure factual accuracy and align the tone with the intended brand or purpose.\""}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1200, 120], "id": "d076ebdd-03ba-4cff-abde-b61a91136575", "name": "Content Creator Agent1", "onError": "continueErrorOutput"}, {"parameters": {"model": "anthropic/claude-3.7-sonnet", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1220, 340], "id": "80ab2bc9-014a-4a06-9296-73e2d2a499dc", "name": "Claude 3.7", "credentials": {"openRouterApi": {"id": "fpo6OUh9TcHg29jk", "name": "OpenRouter account"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [640, 340], "id": "78aa0eb0-0f3e-49a9-ae29-afb6d4fa7d08", "name": "Gemini 2.0 Flash", "credentials": {"googlePalmApi": {"id": "DW8owDXDeMHnr1rA", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "=# Overview\nYou are the ultimate personal assistant. Your job is to send the user's query to the correct tool. You should never be writing emails, or creating even summaries, you just need to call the correct tool.\n\n## Tools\n- Think: Use this to think deeply or if you get stuck\n- emailAgent: Use this tool to take action in email\n- calendarAgent: Use this tool to take action in calendar\n- contactAgent: Use this tool to get, update, or add contacts\n- contentCreator: Use this tool to create blog posts\n- Tavily: Use this tool to search the web\n\n## Rules\n- Some actions require you to look up contact information first. For the following actions, you must get email address and send that to the agent who needs it:\n  - sending emails\n  - drafting emails\n  - creating calendar event with attendee\n\n## Instructions\n1) Call the neccessary tools based on the user request\n2) Use the \"Think\" tool to verify you took the right steps. This tool should be called every time.\n\n\n## Examples\n1) \n- Input: send an email to nate herkelman asking him what time he wants to leave\n  - Action: Use contactAgent to get nate herkelman's email\n  - Action: Use emailAgent to send the email. You will pass the tool a query like \"send nate herkelman an email to ask what time he wants to leave. here is his email: [email address]\n- Output: The email has been sent to <PERSON>. Anything else I can help you with?\n\n\n## Final Reminders\nHere is the current date/time: {{ $now }}\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [340, -320], "id": "fb68ab7d-2fb5-4328-ac02-6221e59e83a7", "name": "Ultimate Assistant"}, {"parameters": {"name": "emailAgent", "description": "Call this tool for any email actions.", "workflowId": {"__rl": true, "value": "C3hLlOS4O6ZJtVFy", "mode": "list", "cachedResultName": "🤖Email Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-160, -40], "id": "bbf182fe-a4fe-46bd-8281-0bb5a17946b6", "name": "Email Agent"}, {"parameters": {"name": "contactAgent", "description": "Call this tool for any contact related actions.", "workflowId": {"__rl": true, "value": "IsSUyrla7wc1cDLE", "mode": "list", "cachedResultName": "🤖Contact Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [960, -40], "id": "d03afe0c-d226-4cd9-bc7c-ce298c789fd1", "name": "Contact Agent"}, {"parameters": {"name": "contentCreator", "description": "Call this tool to create blog posts.", "workflowId": {"__rl": true, "value": "WWSu94V939ATcqvi", "mode": "list", "cachedResultName": "🤖Content Creator Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [1520, -40], "id": "7c3cb708-a04c-4494-b93f-14bb4bed4bd5", "name": "Content Creator Agent"}, {"parameters": {"toolDescription": "Use this tool to search the internet", "method": "POST", "url": "https://api.tavily.com/search", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"api_key\": \"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\",\n    \"query\": \"{searchTerm}\",\n    \"search_depth\": \"basic\",\n    \"include_answer\": true,\n    \"topic\": \"news\",\n    \"include_raw_content\": true,\n    \"max_results\": 3\n} ", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user has requested to search the internet for", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [-980, 100], "id": "827dd253-ab59-43e0-a243-926c3add4592", "name": "<PERSON><PERSON>"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [-860, 100], "id": "4d400e8e-2536-427a-a095-2f5c0f1a3e1d", "name": "Calculator"}, {"parameters": {"name": "calendarAgent", "description": "Call this tool for any calendar action.", "workflowId": {"__rl": true, "value": "0NtlJ41IozGhtFa6", "mode": "list", "cachedResultName": "🤖Calendar Agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [400, -40], "id": "35a8cb4f-0a5e-4ca2-a365-997a7a314e3e", "name": "Calendar Agent"}, {"parameters": {"content": "# Parent Agent\n\n", "height": 260, "width": 420, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [260, -420], "typeVersion": 1, "id": "4fd1e90b-f88a-422b-8d41-ac7d743630c5", "name": "<PERSON><PERSON>"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [-740, 100], "id": "aa2eac2b-989a-47f4-ad10-93d4876c052b", "name": "Think"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-860, -40], "id": "73f888fe-1949-426d-9530-201090124e99", "name": "GPT 4.1", "credentials": {"openAiApi": {"id": "WnxUhaEPMn5hIsEp", "name": "Demo 4/2"}}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "eb3b68e0-3621-42d9-a99e-72b142b359f6", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-460, -320], "webhookId": "99eab1a0-569d-4f0f-a49e-578a02abfe63", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "fe7ecc99-e1e8-4a5e-bdd6-6fce9757b234", "name": "text", "value": "={{ $json.message.text }}", "type": "string"}]}, "options": {}}, "id": "14e7877b-7222-4e3c-983c-03915f30096d", "name": "Set 'Text'", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-80, -300]}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "id": "7acd6b12-2e21-41b8-8b74-18fb6f8c04ab", "name": "Download Voice File", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-140, -460], "webhookId": "c0b376b1-7b63-41b6-8063-0b3096b8333d", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "id": "961b8481-6845-444d-993d-77f3f35881a3", "name": "Transcribe Audio", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [20, -460], "credentials": {"openAiApi": {"id": "WnxUhaEPMn5hIsEp", "name": "Demo 4/2"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8c844924-b2ed-48b0-935c-c66a8fd0c778", "leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}]}, "options": {}}, "id": "abbbd11b-6b76-4121-9c50-6da8d479f531", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-320, -320]}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "id": "3a662d41-e24a-4d9d-8e60-520264426cec", "name": "Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [760, -320], "webhookId": "682ca5ab-d30c-4ea5-bb25-c2f26206e761", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-740, -40], "id": "b9d7ca1c-7261-4576-b80d-ecc2a2b97ee3", "name": "Simple Memory"}, {"parameters": {"content": "# Benefits of Multi Agent System\n\n- More reusable components\n\n- Model flexibility (different models per agent)\n\n- Easier debugging and maintenance\n\n- Clearer prompt logic and better testability\n\n- Foundation for multi-turn agents or agent memory", "height": 320, "width": 540, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1660, -60], "id": "f7dafb94-ef2d-427a-a966-552491ff83f7", "name": "Sticky Note3"}, {"parameters": {"content": "# Email Agent", "height": 700, "width": 540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-580, -60], "id": "2eaa2981-7e0c-483c-8c89-79623e534879", "name": "Sticky Note1"}, {"parameters": {"content": "# Calendar Agent", "height": 700, "width": 540, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, -60], "id": "8ed65853-6335-4048-8fd5-21634f17f67a", "name": "Sticky Note2"}, {"parameters": {"content": "# Contact Agent", "height": 700, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [540, -60], "id": "abbca871-1fcc-4558-881d-4203d4d960f8", "name": "Sticky Note4"}, {"parameters": {"content": "# Content Creator Agent", "height": 700, "width": 540, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1100, -60], "id": "6aa666df-b57b-42b0-83ef-e5a73f3ac245", "name": "Sticky Note5"}, {"parameters": {"content": "# 📚 Setup Guide  \n*Author:* [<PERSON>](https://www.youtube.com/@nateherk)\n\n## Instructions\n1. Take the **Email Agent** and place it into a new workflow.  \n2. Link the **Email Agent tool** to that new workflow you created.  \n3. In that new workflow, start it with the **\"Execute Workflow\" trigger** (`When workflow is executed by another`).  \n4. Repeat steps 1–3 for the **Calendar Agent**, **Contact Agent**, and **Content Creator Agent**.  \n5. Ensure all agents are powered by a **Chat Model** (e.g., OpenRouter, Google, or OpenAI).  \n6. Connect all necessary credentials:\n   - ✅ Chat model credentials  \n   - ✅ Tavily API key  \n   - ✅ Google account  \n   - ✅ Airtable  \n   - ✅ Telegram  \n---\n\nStay organized, test each agent individually, and you’ll be up and running in no time!\n", "height": 460, "width": 800}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-580, -1040], "id": "7c196326-b274-4c99-a016-033f6d2b2ca6", "name": "Sticky Note6"}], "pinData": {}, "connections": {"Send Email": {"ai_tool": [[{"node": "Email Agent1", "type": "ai_tool", "index": 0}]]}, "Get Emails": {"ai_tool": [[{"node": "Email Agent1", "type": "ai_tool", "index": 0}]]}, "Create Draft": {"ai_tool": [[{"node": "Email Agent1", "type": "ai_tool", "index": 0}]]}, "Email Reply": {"ai_tool": [[{"node": "Email Agent1", "type": "ai_tool", "index": 0}]]}, "Get Labels": {"ai_tool": [[{"node": "Email Agent1", "type": "ai_tool", "index": 0}]]}, "Label Emails": {"ai_tool": [[{"node": "Email Agent1", "type": "ai_tool", "index": 0}]]}, "Mark Unread": {"ai_tool": [[{"node": "Email Agent1", "type": "ai_tool", "index": 0}]]}, "4.1-mini": {"ai_languageModel": [[{"node": "Email Agent1", "type": "ai_languageModel", "index": 0}]]}, "Email Agent1": {"main": [[], []]}, "Create Event with Attendee": {"ai_tool": [[{"node": "Calendar Agent1", "type": "ai_tool", "index": 0}]]}, "Create Event": {"ai_tool": [[{"node": "Calendar Agent1", "type": "ai_tool", "index": 0}]]}, "Get Events": {"ai_tool": [[{"node": "Calendar Agent1", "type": "ai_tool", "index": 0}]]}, "Delete Event": {"ai_tool": [[{"node": "Calendar Agent1", "type": "ai_tool", "index": 0}]]}, "Update Event": {"ai_tool": [[{"node": "Calendar Agent1", "type": "ai_tool", "index": 0}]]}, "Calendar Agent1": {"main": [[], []]}, "4.1-mini1": {"ai_languageModel": [[{"node": "Calendar Agent1", "type": "ai_languageModel", "index": 0}]]}, "Get Contacts": {"ai_tool": [[{"node": "Contact Agent1", "type": "ai_tool", "index": 0}]]}, "Add or Update Contact": {"ai_tool": [[{"node": "Contact Agent1", "type": "ai_tool", "index": 0}]]}, "Tavily1": {"ai_tool": [[{"node": "Content Creator Agent1", "type": "ai_tool", "index": 0}]]}, "Claude 3.7": {"ai_languageModel": [[{"node": "Content Creator Agent1", "type": "ai_languageModel", "index": 0}]]}, "Gemini 2.0 Flash": {"ai_languageModel": [[{"node": "Contact Agent1", "type": "ai_languageModel", "index": 0}]]}, "Ultimate Assistant": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Email Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Contact Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Content Creator Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Tavily": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Calendar Agent": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "Ultimate Assistant", "type": "ai_tool", "index": 0}]]}, "GPT 4.1": {"ai_languageModel": [[{"node": "Ultimate Assistant", "type": "ai_languageModel", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Set 'Text'": {"main": [[{"node": "Ultimate Assistant", "type": "main", "index": 0}]]}, "Download Voice File": {"main": [[{"node": "Transcribe Audio", "type": "main", "index": 0}]]}, "Transcribe Audio": {"main": [[{"node": "Ultimate Assistant", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Download Voice File", "type": "main", "index": 0}], [{"node": "Set 'Text'", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Ultimate Assistant", "type": "ai_memory", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "11a08d4d-dfcb-4695-8fb8-5d3062aa6352", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "oT6i0tNmia5Aq5KD", "tags": []}