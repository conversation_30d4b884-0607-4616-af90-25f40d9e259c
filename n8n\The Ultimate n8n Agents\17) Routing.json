{"name": "Routing", "nodes": [{"parameters": {"content": "# Routing\n✅ Optimized Response Handling – Ensures the right AI agent processes the query instead of a single generalist AI.\n\n✅ Scalable & Modular – New AI agents can be added to handle additional topics.\n\n✅ Faster & More Efficient – Reduces unnecessary LLM calls and improves response speed.\n\n✅ Human Escalation for Critical Issues – Ensures urgent problems are handled by real support staff.\n\n✅ Better User Experience – More accurate and relevant responses tailored to the customer’s needs.", "height": 280, "width": 880, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-620, -100], "typeVersion": 1, "id": "5adbe3b1-bb51-4b62-9405-c9e4d52d1237", "name": "<PERSON><PERSON>"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {"labelIds": []}, "options": {}}, "id": "4e23a1e6-e451-4962-b62b-f4c587f1cf24", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.1, "position": [-200, 320], "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $json.id }}"}, "id": "6e285303-cb30-4269-ba5b-e6b7731700d3", "name": "High Priority", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [400, -20], "webhookId": "c2817f4a-2f52-4ae7-8cd0-16ee702b7c22", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $json.id }}"}, "id": "16b2affa-62cd-44b8-90f4-3f56137408a7", "name": "Customer Support", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [400, 200], "webhookId": "00c21fcb-fdf2-47d1-96fd-f61c83181bab", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $json.id }}"}, "id": "57e7793b-1c79-407b-a88c-6b32523ad3fb", "name": "Promotion", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [400, 440], "webhookId": "1e06eea2-bead-419e-b14c-c2ccabfa736a", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $json.id }}"}, "id": "5e4850b5-4924-4a2d-bedf-524e2f434780", "name": "Finance/Billing", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [400, 640], "webhookId": "f3181ea4-b320-4e02-8518-47911e4978ac", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"inputText": "={{ $json.text }}", "categories": {"categories": [{"category": "High Priority", "description": "Emails requiring immediate attention or action, typically from key stakeholders, clients, or decision-makers. These emails often contain time-sensitive requests, deadlines, or escalated issues. Keywords: urgent, ASAP, immediate, deadline, action required, high priority"}, {"category": "Customer Support", "description": "Emails related to ongoing communication with current clients or customers, including service requests, feedback, support tickets, and inquiries. Keywords: request, inquiry, support, question, follow-up, feedback"}, {"category": "Promotions", "description": "Emails related to marketing campaigns, promotional offers, newsletters, or business updates from partners. Typically these emails contain content aimed at engaging your audience or updating on promotions. Keywords: newsletter, promotion, offer, sale, campaign, marketing, launch"}, {"category": "Finance/Billing", "description": " Description: Emails related to financial matters, such as invoices, billing statements, payment reminders, or expense reports. Anything involving transactions or accounting should fall under this label.  Keywords: invoice, payment, billing, receipt, financial, expense, account"}]}, "options": {"systemPromptTemplate": "Please classify the text provided by the user into one of the following categories: {categories}, and use the provided formatting instructions below. Don't explain, and only output the json."}}, "id": "93deba51-0a68-489e-addb-ea7a44293532", "name": "Email Classifier", "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1, "position": [0, 320]}, {"parameters": {"options": {}}, "id": "9d01c728-050a-4aa0-ba31-ad6665473346", "name": "4o mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [40, 620], "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "=Email from: {{ $('Gmail Trigger').item.json.to.value[0].name }}\n\nHere is the Email: \n{{ $('Gmail Trigger').item.json.text }}", "options": {"systemMessage": "=# Overview\nYou are an agent in charge of high priority emails. Your job is to summarize incoming emails and escalate them to the human.\n\n## Tools\nTelegram - Use this tool to escalate to a human. Send over the summary of the email."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [620, -20], "id": "a74a5e3f-4046-4a13-94eb-fc2ee0cc8865", "name": "High Priority Agent"}, {"parameters": {"text": "=Urgent Email: \n{{ $fromAI(\"summary\") }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegramTool", "typeVersion": 1.2, "position": [1040, 260], "id": "0c672fad-26f8-4fae-8b19-811d722e26ee", "name": "Telegram", "webhookId": "4a005847-6ed4-48c6-99bd-d09e9d7271af", "credentials": {"telegramApi": {"id": "9jQWan3cOz3tE62s", "name": "Telegram account 2"}}}, {"parameters": {"promptType": "define", "text": "=Email from: {{ $('Gmail Trigger').item.json.to.value[0].name }}\n\nEmail: {{ $('Gmail Trigger').item.json.text }}", "options": {"systemMessage": "=# Overview\nYou are a customer support representitive named <PERSON>. Your job is to respond to customer inquiries in a friendly and professional manner. \n\n## Tools\nEmail Draft - Use this to send an email reply to a customer. Always sign off as <PERSON> from ABC Corp."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [620, 200], "id": "23bfd8f4-1976-4520-948b-659421b0b1f5", "name": "Customer Support Agent"}, {"parameters": {"promptType": "define", "text": "=Email from: {{ $('Gmail Trigger').item.json.to.value[0].name }}\n\nEmail: {{ $('Gmail Trigger').item.json.text }}", "options": {"systemMessage": "=# Overview\nYou are in charge of promotional opportunities. Your job is to respond to inquiries in a friendly and professional manner. \n\n## Tools\nEmail Draft - Use this to send an email reply to a customer. Always sign off as <PERSON> from ABC Corp."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [620, 440], "id": "6d93ee1c-777b-44a7-ae88-c34f1283834d", "name": "Promotion Agent"}, {"parameters": {"promptType": "define", "text": "=Email from: {{ $('Gmail Trigger').item.json.to.value[0].name }}\n\nEmail: {{ $('Gmail Trigger').item.json.text }}", "options": {"systemMessage": "=# Overview\nYou are in charge of finacnes. Your job is to respond to inquiries in a friendly and professional manner. \n\n## Tools\nEmail Draft - Use this to send an email reply to a customer. Always sign off as <PERSON> from ABC Corp."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [620, 640], "id": "172b5671-a85d-4c8a-bd6a-2fb2b94284b4", "name": "Finance Agent"}, {"parameters": {"resource": "draft", "subject": "={{ $fromAI(\"subject\") }}", "message": "={{ $fromAI(\"emailBody\") }}", "options": {"threadId": "={{ $('Gmail Trigger').item.json.threadId }}"}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1040, 520], "id": "2c7e251a-e102-4c41-8d35-730357196f46", "name": "Email Draft", "webhookId": "5d8537bc-efa2-4889-82bb-cc44dc383ace", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}], "pinData": {}, "connections": {"Gmail Trigger": {"main": [[{"node": "Email Classifier", "type": "main", "index": 0}]]}, "High Priority": {"main": [[{"node": "High Priority Agent", "type": "main", "index": 0}]]}, "Customer Support": {"main": [[{"node": "Customer Support Agent", "type": "main", "index": 0}]]}, "Promotion": {"main": [[{"node": "Promotion Agent", "type": "main", "index": 0}]]}, "Finance/Billing": {"main": [[{"node": "Finance Agent", "type": "main", "index": 0}]]}, "Email Classifier": {"main": [[{"node": "High Priority", "type": "main", "index": 0}], [{"node": "Customer Support", "type": "main", "index": 0}], [{"node": "Promotion", "type": "main", "index": 0}], [{"node": "Finance/Billing", "type": "main", "index": 0}]]}, "4o mini": {"ai_languageModel": [[{"node": "Email Classifier", "type": "ai_languageModel", "index": 0}, {"node": "High Priority Agent", "type": "ai_languageModel", "index": 0}, {"node": "Customer Support Agent", "type": "ai_languageModel", "index": 0}, {"node": "Promotion Agent", "type": "ai_languageModel", "index": 0}, {"node": "Finance Agent", "type": "ai_languageModel", "index": 0}]]}, "Telegram": {"ai_tool": [[{"node": "High Priority Agent", "type": "ai_tool", "index": 0}]]}, "Email Draft": {"ai_tool": [[{"node": "Customer Support Agent", "type": "ai_tool", "index": 0}, {"node": "Finance Agent", "type": "ai_tool", "index": 0}, {"node": "Promotion Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "90017b0c-7997-4d13-a310-86ed7425dfd5", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "p9tPjNO9qaMomqOT", "tags": []}