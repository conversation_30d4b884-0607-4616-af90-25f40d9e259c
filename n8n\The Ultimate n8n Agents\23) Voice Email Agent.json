{"name": "Voice Email Agent", "nodes": [{"parameters": {"model": "gpt-4o", "options": {}}, "id": "6171e8bc-f97a-43e7-b01f-d1feb9dddff0", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [640, 500], "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "id": "a076dd15-8cb6-4384-9706-30b812058125", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1080, 280]}, {"parameters": {"documentId": {"__rl": true, "value": "19MEAwqL6LOhB9VNXW8TSDTBd1U8s0L77uWHpIhRieEA", "mode": "list", "cachedResultName": "Contact Database", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19MEAwqL6LOhB9VNXW8TSDTBd1U8s0L77uWHpIhRieEA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19MEAwqL6LOhB9VNXW8TSDTBd1U8s0L77uWHpIhRieEA/edit#gid=0"}, "options": {}}, "id": "0ac9694a-bad1-4739-9049-e02485c42f84", "name": "contactData", "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.5, "position": [820, 500], "credentials": {"googleSheetsOAuth2Api": {"id": "DKJyEjZyGh00GFMv", "name": "Google Sheets account"}}}, {"parameters": {"sendTo": "={{ $fromAI(\"emailAddress\") }}", "subject": "={{ $fromAI(\"subject\") }}", "emailType": "text", "message": "={{ $fromAI(\"emailBody\") }}", "options": {"appendAttribution": false}}, "id": "62ef25c5-7930-499f-b2f0-6250d9a94752", "name": "sendEmail", "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [940, 500], "webhookId": "0cd83f42-b3d7-4819-89d5-36038b54f9c1", "credentials": {"gmailOAuth2": {"id": "PblbJue2OKr80hIY", "name": "Gmail account"}}}, {"parameters": {"httpMethod": "POST", "path": "n8n", "responseMode": "responseNode", "options": {}}, "id": "069dd641-f2fd-4d99-a218-e859a4925ed0", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [480, 280], "webhookId": "44fd9c8a-250a-4c05-9035-91bf6f85d015"}, {"parameters": {"promptType": "define", "text": "=Email for: {{ $json.body.to }}\nEmail content: {{ $json.body.emailContent }}", "options": {"systemMessage": "=# Overview  \nYou are an AI agent responsible for drafting and sending professional emails based on user instructions. You have access to two tools: `contactData` to find email addresses and `sendEmail` to compose and send emails. Your objective is to identify the recipient's contact information, draft a professional email, and sign off as \"[Your Name]\" before sending.\n\n---\n\n## Tools  \n- **contactData**: Retrieves the recipient's email address based on their name.  \n  **Input**: Name (e.g., \"<PERSON>\").  \n  **Output**: Email address (e.g., \"<EMAIL>\").  \n\n- **sendEmail**: Sends an email with a subject and body.  \n  **Input**:  \n  - `recipient`: Email address (e.g., \"<EMAIL>\").  \n  - `subject`: Email subject (e.g., \"Meeting Request\").  \n  - `body`: Email body (e.g., \"<PERSON> <PERSON>, meeting is set for Tuesday.\").  "}}, "id": "32501e02-fb95-4638-827e-0df51bd8d481", "name": "Email Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [700, 280]}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Email Agent", "type": "ai_languageModel", "index": 0}]]}, "contactData": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "sendEmail": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "Webhook": {"main": [[{"node": "Email Agent", "type": "main", "index": 0}]]}, "Email Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0540261d-c884-4822-80bf-a99a0fc2691a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "KkRmFJwEEifSshXi", "tags": []}