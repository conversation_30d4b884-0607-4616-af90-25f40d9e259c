{"name": "Invoice Workflow SBS", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "event": "fileCreated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [0, 0], "id": "ec8a9bb2-628e-4416-9a1a-ffc0eb9fdf3b", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "60zdCK3Sx2Shlbb4", "name": "Google Drive account"}}}, {"parameters": {"operation": "pdf", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [420, 0], "id": "505544e2-0db7-4218-bb27-9fb60c9235cc", "name": "Extract from File"}, {"parameters": {"text": "={{ $json.text }}", "attributes": {"attributes": [{"name": "Invoice Number", "description": "The number of the invoice", "required": true}, {"name": "Client Name", "description": "the name of the client", "required": true}, {"name": "Client Email", "description": "the email of the client", "required": true}, {"name": "Client Address", "description": "the address of the client", "required": true}, {"name": "Client Phone", "description": "the phone number of the client", "required": true}, {"name": "Total Amount", "description": "the total amount of the invoice", "required": true}, {"name": "Invoice Date", "type": "date", "description": "the date the invoice was sent", "required": true}, {"name": "Due Date", "type": "date", "description": "the date the invoice is due", "required": true}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [640, 0], "id": "********-dff0-47f1-a50c-0faddc034939", "name": "Information Extractor"}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [560, 220], "id": "19e8549e-deab-4b51-8aeb-aef7bcd77397", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "DW8owDXDeMHnr1rA", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "mode": "list", "value": ""}, "sheetName": {"__rl": true, "value": "", "mode": "list", "cachedResultName": "", "cachedResultUrl": ""}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1000, 0], "id": "867b0493-cfb1-4fd9-8fa3-3a69e525d274", "name": "Update DB", "credentials": {"googleSheetsOAuth2Api": {"id": "wwE70mh6N2QEfZRL", "name": "Google Sheets account 3"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [220, 0], "id": "db5da1c9-8887-401b-86b4-b10ffe284c9e", "name": "Download Binary", "credentials": {"googleDriveOAuth2Api": {"id": "60zdCK3Sx2Shlbb4", "name": "Google Drive account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Invoice Number: {{ $json['Invoice Number'] }}\n\nClient Name: {{ $json['Client Name'] }}\n\nClient Email: {{ $json['Client Email'] }}\n\nTotal Amount: {{ $json['Total Amount'] }}\n\nInvoice Date: {{ $json['Invoice Date'] }}\n\nDue Date: {{ $json['Due Date'] }}"}, {"content": "=# Overview\nYou are an email expert for Green Grass Corp named <PERSON><PERSON>. You will receive invoice information. Your job is to notify the internal billing team that an invoice was received/sent.\n\n## Email\nInform the billing team of the invoice. Let them know we have also updated this in the Invoice Database, and they can view it here: https://docs.google.com/spreadsheets/d/14FtgVUjy0tw4EQOB2T8wuCYOe6CELtt3UTjn-GsuDWY/edit?gid=0#gid=0\n\n## Output\nOutput the following parameters separately:\nSubject\nEmail", "role": "system"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1220, 0], "id": "683f0a6c-c168-45c7-a87f-53c37478abd3", "name": "Create Email", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"sendTo": "<EMAIL>", "subject": "={{ $json.message.content.Subject }}", "emailType": "text", "message": "={{ $json.message.content.Email }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1580, 0], "id": "0d962eb7-92e9-42b2-bdde-c20d0af115f4", "name": "Send Email", "webhookId": "da2aa524-25e5-47e2-bd1a-fe7a97edf8a7", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1800, 0], "id": "bb07ccfe-eadb-4ea1-b9bd-8f0d17e40b28", "name": "No Operation, do nothing"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Download Binary", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Update DB", "type": "main", "index": 0}]]}, "Download Binary": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Update DB": {"main": [[{"node": "Create Email", "type": "main", "index": 0}]]}, "Create Email": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Send Email": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "dcf7057f-3b65-4d56-8d0d-a16d6fb8f37b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "fIRsJjN1p2ErPKGA", "tags": []}