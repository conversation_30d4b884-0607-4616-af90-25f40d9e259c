{"nodes": [{"parameters": {"modelName": "models/text-embedding-004"}, "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "typeVersion": 1, "position": [1820, 1240], "id": "13704ef4-6669-4869-bfa6-6554ded6365b", "name": "Embeddings Google Gemini", "credentials": {"googlePalmApi": {"id": "npqTjEkXj4pfPb9E", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [780, 1060], "id": "9b6c5cda-a8b8-4f02-a569-34a8a10d172a", "name": "When chat message received", "webhookId": "035f267b-8479-4dcb-9f13-e2c84d30f6c5"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "1b4b5e9e-ad56-4c86-9f3c-1b30cdc54bbc", "name": "Format Response", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1880, 820], "disabled": true}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "exobank_kb", "toolDescription": "Find information for given chatinput from AI", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.1, "position": [1720, 1020], "id": "2ba77276-ce1f-448c-99ad-ebc52a7b7088", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "Wf6sNeCGVgT4AWJB", "name": "Supabase xbank"}}}, {"parameters": {"options": {"systemMessage": "# AI SQL Assistant System Message\n\n## Role and Capabilities\nYou are an advanced SQL assistant integrated with a Supabase PostgreSQL database. Your primary functions are:\n\n1. **Database Interaction**:\n   - List all available tables in the database\n   - View table structures and sample data\n   - Execute SQL queries with proper validation\n   - Verify table and field existence before querying\n   - Handle variations in table/field naming\n\n2. **Knowledge Base Integration**:\n   - Access company information from the Supabase vector store\n   - Retrieve relevant knowledge base data when needed\n   - Use the 'exobank_kb' tool for company-related queries\n\n3. **Context Awareness**:\n   - Default timezone: Asia/Kathmandu\n   - Default currency: Rs. (Nepalese Rupee)\n   - Maintain conversation context using Postgres chat memory\n\n## Workflow Instructions\n\n### Before Executing Queries:\n1. Verify if the requested table exists\n2. Check field names and data types\n3. Suggest corrections for potential typos\n4. Confirm with the user if multiple tables match the criteria\n\n### When Handling Queries:\n1. Always validate SQL syntax before execution\n2. Use parameterized queries when possible\n3. Limit result sets to reasonable sizes\n4. Provide clear explanations of query results\n5. Format results in a readable manner\n\n### For Company Data:\n1. First, check the Supabase vector store using 'exobank_kb'\n2. If information is not found, suggest querying the database\n3. Combine information from both sources when relevant\n\n## Error Handling:\n- Clearly explain any SQL errors in simple terms\n- Suggest alternative queries when possible\n- Provide guidance on how to correct invalid queries\n- Handle timezone and currency conversions automatically\n\n## Security:\n- Never expose sensitive information\n- Validate all user inputs\n- Follow the least privilege principle for database access\n\n## Response Format:\n- Use clear, concise language\n- Format SQL queries with proper syntax highlighting\n- Present data in well-structured tables when appropriate\n- Include relevant metadata (execution time, row count, etc.)"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1320, 820], "id": "61b7fd1b-5135-41cd-994b-26738ec204a7", "name": "AI Agent"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1300, 1080], "id": "731f1bf8-814b-49f4-a7d3-8115c43588ed", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "axIwG8YWmFw19OtH", "name": "Postgres xbank"}}}, {"parameters": {"model": "meta-llama/llama-4-maverick-17b-128e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [1160, 1040], "id": "c969b8cd-4e59-47fd-87ec-aa18516e8665", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "PUE4PqELQgpM61IW", "name": "Groq account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "{{ $fromAI('sqlQuery') }}", "options": {"queryReplacement": "sqlQuery"}}, "id": "fafaeff2-6b59-43db-bf33-c6edc32eb385", "name": "Execute Query", "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [1520, 1200], "credentials": {"postgres": {"id": "axIwG8YWmFw19OtH", "name": "Postgres xbank"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM {{ $fromAI('tableName') }} LIMIT 10;", "options": {"queryReplacement": "tableName"}}, "id": "819300da-e163-400b-9010-c717b78fa0e3", "name": "View Table", "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [1580, 1060], "credentials": {"postgres": {"id": "axIwG8YWmFw19OtH", "name": "Postgres xbank"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';", "options": {}}, "id": "65d05ac9-a1ee-4215-a771-25954cc68fb1", "name": "List Tables", "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [1420, 1100], "credentials": {"postgres": {"id": "axIwG8YWmFw19OtH", "name": "Postgres xbank"}}}, {"parameters": {"assignments": {"assignments": [{"id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "chatInput", "value": "={{ $json.body?.message || $json.chatInput || '' }}", "type": "string"}, {"id": "b2c3d4e5-f6a7-8901-bcde-f12345678901", "name": "sessionId", "value": "={{ $json.sessionId || $json.body?.sessionId || 'default-session' }}", "type": "string"}]}, "options": {}}, "id": "c8d6f43b-19a9-40e6-9769-0d0b40158b7c", "name": "Prepare Chat Input", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1120, 820]}, {"parameters": {"options": {}}, "id": "1b5b01c2-4ecb-46ba-ba79-0a76575ef608", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [2160, 820]}, {"parameters": {"httpMethod": "POST", "path": "chat-message", "responseMode": "responseNode", "options": {}}, "id": "0e657775-12ea-4b7b-b041-c817c69ae556", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [680, 820], "webhookId": "chat-webhook-test"}], "connections": {"Embeddings Google Gemini": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Prepare Chat Input", "type": "main", "index": 0}]]}, "Format Response": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Execute Query": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "View Table": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "List Tables": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Prepare Chat Input": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Chat Webhook": {"main": [[{"node": "Prepare Chat Input", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d68f2c6afa32a95329d5cbfc0152c7846bb3311f9635b7c91e69b7415f5c0be"}}