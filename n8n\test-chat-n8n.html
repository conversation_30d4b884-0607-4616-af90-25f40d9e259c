<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>n8n Chatbot</title>
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <style>
    :root {
      --primary: #5a67d8;
      --primary-dark: #434190;
      --accent: #7f9cf5;
      --bot-bg: #f1f5f9;
      --user-bg: #5a67d8;
      --user-text: #fff;
      --bot-text: #222;
      --border-radius: 18px;
      --shadow: 0 8px 32px rgba(90,103,216,0.10);
      --thinking-dot-size: 10px;
    }
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Arial, sans-serif;
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      min-height: 100vh;
    }
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    .chat-container {
      width: 100%;
      max-width: 420px;
      height: 90vh;
      max-height: 700px;
      background: #fff;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: relative;
    }
    .chat-header {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 18px 20px;
      background: linear-gradient(90deg, var(--primary), var(--accent));
      color: #fff;
      font-size: 1.2rem;
      font-weight: 600;
      letter-spacing: 0.03em;
      box-shadow: 0 2px 8px rgba(90,103,216,0.07);
    }
    .chat-header .logo {
      width: 32px;
      height: 32px;
      background: #fff2;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      font-weight: bold;
      box-shadow: 0 2px 6px rgba(90,103,216,0.08);
    }
    .messages {
      flex: 1;
      padding: 18px 14px 12px 14px;
      overflow-y: auto;
      background: #f8fafc;
      display: flex;
      flex-direction: column;
      gap: 8px;
      scroll-behavior: smooth;
    }
    .message {
      margin-bottom: 0;
      display: flex;
      align-items: flex-end;
    }
    .message.user { justify-content: flex-end; }
    .bubble {
      max-width: 80%;
      padding: 12px 16px;
      border-radius: var(--border-radius);
      position: relative;
      word-break: break-word;
      box-shadow: 0 1px 8px rgba(90,103,216,0.04);
      font-size: 1rem;
      line-height: 1.5;
      transition: background 0.2s;
    }
    .bubble.user {
      background: var(--user-bg);
      color: var(--user-text);
      border-bottom-right-radius: 6px;
      margin-left: 40px;
      align-self: flex-end;
    }
    .bubble.bot {
      background: var(--bot-bg);
      color: var(--bot-text);
      border-bottom-left-radius: 6px;
      margin-right: 40px;
      align-self: flex-start;
    }
    .bubble.bot p { margin: 0 0 10px 0; }
    .bubble.bot ul, .bubble.bot ol { margin: 0 0 10px 20px; padding: 0; }
    .bubble.bot pre { background: #f0f0f0; padding: 8px; border-radius: 4px; overflow-x: auto; }
    .bubble.bot code { font-family: monospace; background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
    .input-area {
      display: flex;
      border-top: 1px solid #e5e7eb;
      background: #f1f5f9;
      padding: 8px 10px;
      align-items: center;
      gap: 8px;
    }
    .input-area input {
      flex: 1;
      padding: 13px 14px;
      border: none;
      border-radius: 22px;
      font-size: 1rem;
      background: #fff;
      outline: none;
      box-shadow: 0 1px 4px rgba(90,103,216,0.04);
      transition: box-shadow 0.2s;
    }
    .input-area input:focus {
      box-shadow: 0 2px 12px rgba(90,103,216,0.10);
    }
    .input-area button {
      padding: 0 22px;
      font-size: 1rem;
      border: none;
      border-radius: 22px;
      background: linear-gradient(90deg, var(--primary), var(--accent));
      color: #fff;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.2s, box-shadow 0.2s;
      box-shadow: 0 1px 4px rgba(90,103,216,0.07);
      height: 44px;
    }
    .input-area button:disabled {
      background: #cbd5e1;
      color: #fff;
      cursor: not-allowed;
    }
    /* Thinking Animation */
    .thinking-animation {
      display: none;
      padding: 12px 16px;
      background: var(--bot-bg);
      border-radius: var(--border-radius);
      border-bottom-left-radius: 6px;
      margin-right: 40px;
      align-self: flex-start;
      max-width: 80px;
    }
    
    .thinking-dots {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .thinking-dot {
      width: var(--thinking-dot-size);
      height: var(--thinking-dot-size);
      background-color: var(--primary);
      border-radius: 50%;
      opacity: 0.6;
      animation: thinking-animation 1.4s infinite ease-in-out;
    }
    
    .thinking-dot:nth-child(1) {
      animation-delay: 0s;
    }
    
    .thinking-dot:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    .thinking-dot:nth-child(3) {
      animation-delay: 0.4s;
    }
    
    @keyframes thinking-animation {
      0%, 100% {
        transform: translateY(0);
        opacity: 0.6;
      }
      50% {
        transform: translateY(-6px);
        opacity: 1;
      }
    }
    
    /* Responsive Design */
    @media (max-width: 600px) {
      body {
        padding: 0 12px;
        box-sizing: border-box;
      }
      .chat-container {
        max-width: 100%;
        width: 100%;
        height: 90vh;
        max-height: 100vh;
        border-radius: 12px;
        margin: 15px auto;
      }
      .chat-header {
        font-size: 1rem;
        padding: 14px 10px;
      }
      .messages {
        padding: 12px 10px 8px 10px;
      }
      .bubble {
        font-size: 0.98rem;
        padding: 10px 12px;
        max-width: 85%;
      }
      .message {
        display: flex;
        justify-content: center;
      }
      .message.user {
        justify-content: flex-end;
      }
      .message.bot {
        justify-content: flex-start;
      }
      .input-area {
        padding: 6px 4px;
      }
      .input-area input {
        font-size: 0.97rem;
        padding: 11px 10px;
      }
      .input-area button {
        font-size: 0.97rem;
        padding: 0 13px;
        height: 38px;
      }
      .thinking-animation {
        padding: 10px 12px;
        max-width: 60px;
      }
      :root {
        --thinking-dot-size: 8px;
      }
    }
  </style>
</head>
  <body>
    <div class="chat-container">
      <div class="chat-header">
        <span class="logo">🤖</span> n8n Chatbot
      </div>
      <div id="messages" class="messages"></div>
      <div class="input-area">
        <input id="input" type="text" placeholder="Type a message..." />
        <button id="sendBtn">Send</button>
      </div>
    </div>

  <script>
    const prodUrl = 'https://864f-2400-1a00-bda0-c0e1-1c5-d54d-c462-63d7.ngrok-free.app/webhook/chat-message';
    const testUrl = 'https://864f-2400-1a00-bda0-c0e1-1c5-d54d-c462-63d7.ngrok-free.app/webhook-test/chat-message';
    const apiUrl = prodUrl; // switch to testUrl if needed
    const sessionId = 'session-' + Date.now();

    const messagesEl = document.getElementById('messages');
    const inputEl = document.getElementById('input');
    const sendBtn = document.getElementById('sendBtn');

    function appendMessage(text, sender) {
      const wrapper = document.createElement('div');
      wrapper.classList.add('message', sender);
      const bubble = document.createElement('div');
      bubble.classList.add('bubble', sender);
      
      if (sender === 'bot') {
        // Use marked.js to parse markdown for bot messages
        bubble.innerHTML = marked.parse(text);
      } else {
        // For user messages, just use text
        bubble.textContent = text;
      }
      
      wrapper.appendChild(bubble);
      messagesEl.appendChild(wrapper);
      messagesEl.scrollTop = messagesEl.scrollHeight;
    }

    function createThinkingAnimation() {
      const wrapper = document.createElement('div');
      wrapper.className = 'message bot';
      wrapper.id = 'thinking-wrapper';
      
      const animation = document.createElement('div');
      animation.className = 'thinking-animation';
      animation.style.display = 'block';
      
      const dots = document.createElement('div');
      dots.className = 'thinking-dots';
      
      for (let i = 0; i < 3; i++) {
        const dot = document.createElement('div');
        dot.className = 'thinking-dot';
        dots.appendChild(dot);
      }
      
      animation.appendChild(dots);
      wrapper.appendChild(animation);
      messagesEl.appendChild(wrapper);
      messagesEl.scrollTop = messagesEl.scrollHeight;
      return wrapper;
    }
    
    function removeThinkingAnimation() {
      const thinkingWrapper = document.getElementById('thinking-wrapper');
      if (thinkingWrapper) {
        thinkingWrapper.remove();
      }
    }
    
    async function sendMessage() {
      const userInput = inputEl.value.trim();
      if (!userInput) return;
      
      appendMessage(userInput, 'user');
      inputEl.value = '';
      sendBtn.disabled = true;
      
      // Show thinking animation after user message
      const thinkingWrapper = createThinkingAnimation();
      messagesEl.scrollTop = messagesEl.scrollHeight;
      
      try {
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ message: userInput, sessionId })
        });
        const data = await response.json();
        const botReply = data?.body?.reply || data.reply || JSON.stringify(data);
        // Remove thinking animation before showing response
        removeThinkingAnimation();
        appendMessage(botReply, 'bot');
      } catch (err) {
        console.error(err);
        // Remove thinking animation before showing error
        removeThinkingAnimation();
        appendMessage('Error: Unable to reach server.', 'bot');
      } finally {
        sendBtn.disabled = false;
      }
    }

    sendBtn.addEventListener('click', sendMessage);
    inputEl.addEventListener('keypress', e => { if (e.key === 'Enter') sendMessage(); });
  </script>
</body>
</html>
