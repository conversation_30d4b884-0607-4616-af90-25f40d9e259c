#!/usr/bin/env python3
"""
Entry point for running the Pydantic AI application.
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

try:
    from app.config import get_settings, validate_settings
    from app.main import app
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Validate settings
    settings = get_settings()
    
    print(f"Starting {settings.app_name} v{settings.app_version}")
    print(f"Debug mode: {settings.debug}")
    print(f"Server will run on {settings.host}:{settings.port}")
    
    # Validate configuration
    try:
        validate_settings()
        print("✓ Configuration validated successfully")
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("\nPlease ensure you have:")
        print("1. Created a .env file (copy from .env.example)")
        print("2. Set your OPENAI_API_KEY in the .env file")
        print("3. Installed all dependencies: pip install -r requirements.txt")
        sys.exit(1)
    
    if __name__ == "__main__":
        import uvicorn
        
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level="info" if not settings.debug else "debug"
        )

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\nPlease ensure you have installed all dependencies:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ Startup error: {e}")
    sys.exit(1)
