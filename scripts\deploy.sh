#!/bin/bash

# AI Brain Foundation Deployment Script
# Supports development, staging, and production deployments

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEFAULT_ENV="development"
DEFAULT_VERSION="latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
AI Brain Foundation Deployment Script

Usage: $0 [OPTIONS] COMMAND

Commands:
    build       Build Docker images
    deploy      Deploy the application
    start       Start services
    stop        Stop services
    restart     Restart services
    logs        Show service logs
    status      Show service status
    clean       Clean up containers and images
    migrate     Run database migrations
    test        Run tests in containers

Options:
    -e, --env ENV           Environment (development|staging|production) [default: development]
    -v, --version VERSION   Application version [default: latest]
    -f, --force            Force rebuild/redeploy
    -h, --help             Show this help message

Examples:
    $0 -e development deploy
    $0 -e production -v 1.2.0 deploy
    $0 logs app
    $0 migrate
EOF
}

# Parse command line arguments
ENVIRONMENT="$DEFAULT_ENV"
VERSION="$DEFAULT_VERSION"
FORCE=false
COMMAND=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        build|deploy|start|stop|restart|logs|status|clean|migrate|test)
            COMMAND="$1"
            shift
            break
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    log_error "Invalid environment: $ENVIRONMENT"
    log_error "Must be one of: development, staging, production"
    exit 1
fi

# Set environment variables
export ENVIRONMENT
export APP_VERSION="$VERSION"
export BUILD_TARGET="production"

# Load environment-specific configuration
ENV_FILE="$PROJECT_ROOT/config/${ENVIRONMENT}.env"
if [[ -f "$ENV_FILE" ]]; then
    log_info "Loading environment configuration from $ENV_FILE"
    set -a
    source "$ENV_FILE"
    set +a
else
    log_warning "Environment file not found: $ENV_FILE"
fi

# Set Docker Compose profiles based on environment
case "$ENVIRONMENT" in
    development)
        export COMPOSE_PROFILES="monitoring"
        export BUILD_TARGET="development"
        export DEV_VOLUME_MOUNT="$PROJECT_ROOT"
        export DEV_VOLUME_OPTIONS="rw"
        ;;
    staging)
        export COMPOSE_PROFILES="monitoring"
        ;;
    production)
        export COMPOSE_PROFILES="production,monitoring"
        ;;
esac

# Docker Compose command
DOCKER_COMPOSE="docker-compose -f $PROJECT_ROOT/docker-compose.yml"

# Command functions
cmd_build() {
    log_info "Building Docker images for $ENVIRONMENT environment..."
    
    if [[ "$FORCE" == "true" ]]; then
        log_info "Force rebuild enabled - removing existing images"
        $DOCKER_COMPOSE build --no-cache
    else
        $DOCKER_COMPOSE build
    fi
    
    log_success "Build completed"
}

cmd_deploy() {
    log_info "Deploying AI Brain Foundation to $ENVIRONMENT environment..."
    
    # Pre-deployment checks
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "Running pre-deployment checks for production..."
        
        # Check required environment variables
        required_vars=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY" "JWT_SECRET_KEY")
        for var in "${required_vars[@]}"; do
            if [[ -z "${!var}" ]]; then
                log_error "Required environment variable not set: $var"
                exit 1
            fi
        done
        
        # Confirm production deployment
        read -p "Are you sure you want to deploy to PRODUCTION? (yes/no): " confirm
        if [[ "$confirm" != "yes" ]]; then
            log_info "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Build images
    cmd_build
    
    # Run database migrations
    cmd_migrate
    
    # Deploy services
    log_info "Starting services..."
    $DOCKER_COMPOSE up -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    if cmd_status; then
        log_success "Deployment completed successfully"
        log_info "Application is available at: http://localhost:${APP_PORT:-8000}"
        
        if [[ "$ENVIRONMENT" != "production" ]]; then
            log_info "Monitoring available at:"
            log_info "  - Grafana: http://localhost:${GRAFANA_PORT:-3001}"
            log_info "  - Prometheus: http://localhost:${PROMETHEUS_PORT:-9090}"
            log_info "  - Jaeger: http://localhost:${JAEGER_UI_PORT:-16686}"
        fi
    else
        log_error "Deployment failed - some services are not healthy"
        cmd_logs
        exit 1
    fi
}

cmd_start() {
    log_info "Starting services..."
    $DOCKER_COMPOSE up -d
    log_success "Services started"
}

cmd_stop() {
    log_info "Stopping services..."
    $DOCKER_COMPOSE down
    log_success "Services stopped"
}

cmd_restart() {
    log_info "Restarting services..."
    $DOCKER_COMPOSE restart
    log_success "Services restarted"
}

cmd_logs() {
    if [[ -n "$1" ]]; then
        log_info "Showing logs for service: $1"
        $DOCKER_COMPOSE logs -f "$1"
    else
        log_info "Showing logs for all services"
        $DOCKER_COMPOSE logs -f
    fi
}

cmd_status() {
    log_info "Checking service status..."
    
    # Check if services are running
    if ! $DOCKER_COMPOSE ps | grep -q "Up"; then
        log_error "No services are running"
        return 1
    fi
    
    # Check health status
    unhealthy_services=$($DOCKER_COMPOSE ps --format "table {{.Name}}\t{{.Status}}" | grep -v "healthy" | grep "Up" | wc -l)
    
    if [[ "$unhealthy_services" -gt 0 ]]; then
        log_warning "Some services are not healthy:"
        $DOCKER_COMPOSE ps
        return 1
    else
        log_success "All services are healthy"
        $DOCKER_COMPOSE ps
        return 0
    fi
}

cmd_clean() {
    log_warning "This will remove all containers, images, and volumes. Are you sure? (yes/no)"
    read -r confirm
    
    if [[ "$confirm" == "yes" ]]; then
        log_info "Cleaning up containers and images..."
        $DOCKER_COMPOSE down -v --rmi all --remove-orphans
        docker system prune -f
        log_success "Cleanup completed"
    else
        log_info "Cleanup cancelled"
    fi
}

cmd_migrate() {
    log_info "Running database migrations..."
    
    # Ensure database is running
    $DOCKER_COMPOSE up -d postgres
    sleep 5
    
    # Run migrations
    $DOCKER_COMPOSE exec -T app alembic upgrade head
    
    log_success "Database migrations completed"
}

cmd_test() {
    log_info "Running tests in containers..."
    
    # Build test image
    docker build -t ai-brain-test --target development .
    
    # Run tests
    docker run --rm \
        --network ai-brain_ai-brain-network \
        -e ENVIRONMENT=testing \
        -e DATABASE_URL="********************************************/ai_brain_test" \
        ai-brain-test \
        pytest tests/ -v
    
    log_success "Tests completed"
}

# Main execution
case "$COMMAND" in
    build)
        cmd_build
        ;;
    deploy)
        cmd_deploy
        ;;
    start)
        cmd_start
        ;;
    stop)
        cmd_stop
        ;;
    restart)
        cmd_restart
        ;;
    logs)
        cmd_logs "$@"
        ;;
    status)
        cmd_status
        ;;
    clean)
        cmd_clean
        ;;
    migrate)
        cmd_migrate
        ;;
    test)
        cmd_test
        ;;
    "")
        log_error "No command specified"
        show_help
        exit 1
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        show_help
        exit 1
        ;;
esac
