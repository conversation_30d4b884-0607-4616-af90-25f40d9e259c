-- Initialize AI Backend Database
-- This script sets up the basic database structure for the AI Backend

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS ai_backend;
CREATE SCHEMA IF NOT EXISTS admin_dashboard;

-- Set default schema
SET search_path TO ai_backend, public;

-- Create basic tables for the foundation

-- Model configurations table
CREATE TABLE IF NOT EXISTS model_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    provider VARCHAR(100) NOT NULL,
    price_input_per_1k DECIMAL(10, 6) DEFAULT 0.0,
    price_output_per_1k DECIMAL(10, 6) DEFAULT 0.0,
    max_context_tokens INTEGER DEFAULT 4096,
    tier VARCHAR(50) DEFAULT 'free',
    is_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Provider configurations table
CREATE TABLE IF NOT EXISTS providers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    base_url VARCHAR(500),
    is_enabled BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 1,
    rate_limit_rpm INTEGER,
    rate_limit_tpm INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Routing policies table
CREATE TABLE IF NOT EXISTS routing_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_class VARCHAR(50) NOT NULL,
    score_threshold DECIMAL(3, 2) NOT NULL,
    preferred_models TEXT[], -- Array of model names
    fallback_models TEXT[], -- Array of fallback model names
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage logs table
CREATE TABLE IF NOT EXISTS usage_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id VARCHAR(255),
    model_used VARCHAR(255) NOT NULL,
    provider_used VARCHAR(100) NOT NULL,
    tokens_input INTEGER DEFAULT 0,
    tokens_output INTEGER DEFAULT 0,
    cost_usd DECIMAL(10, 6) DEFAULT 0.0,
    latency_ms INTEGER,
    task_class VARCHAR(50),
    complexity_score DECIMAL(3, 2),
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_model_configs_provider ON model_configs(provider);
CREATE INDEX IF NOT EXISTS idx_model_configs_enabled ON model_configs(is_enabled);
CREATE INDEX IF NOT EXISTS idx_providers_enabled ON providers(is_enabled);
CREATE INDEX IF NOT EXISTS idx_routing_policies_task_class ON routing_policies(task_class);
CREATE INDEX IF NOT EXISTS idx_usage_logs_created_at ON usage_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_usage_logs_model_provider ON usage_logs(model_used, provider_used);

-- Insert default data
INSERT INTO providers (name, is_enabled, priority) VALUES
    ('openai', true, 1),
    ('anthropic', true, 2),
    ('google', true, 3),
    ('groq', true, 4),
    ('openrouter', true, 5),
    ('deepseek', true, 6)
ON CONFLICT (name) DO NOTHING;

INSERT INTO model_configs (name, provider, price_input_per_1k, price_output_per_1k, max_context_tokens, tier) VALUES
    ('gpt-4o-mini', 'openai', 0.15, 0.60, 128000, 'paid'),
    ('gpt-4o', 'openai', 5.00, 15.00, 128000, 'paid'),
    ('claude-3-5-sonnet-latest', 'anthropic', 3.00, 15.00, 200000, 'paid'),
    ('claude-3-5-haiku-latest', 'anthropic', 1.00, 5.00, 200000, 'paid'),
    ('gemini-2.0-flash', 'google', 0.00, 0.00, 1000000, 'free'),
    ('gemini-1.5-flash', 'google', 0.075, 0.30, 1000000, 'free'),
    ('llama-3.3-70b-versatile', 'groq', 0.00, 0.00, 32768, 'free'),
    ('llama-3.1-70b-versatile', 'groq', 0.59, 0.79, 32768, 'paid'),
    ('deepseek-chat', 'deepseek', 0.14, 0.28, 32768, 'paid'),
    ('deepseek/deepseek-chat', 'openrouter', 0.00, 0.00, 32768, 'free')
ON CONFLICT (name) DO NOTHING;

INSERT INTO routing_policies (task_class, score_threshold, preferred_models, fallback_models) VALUES
    ('very_easy', 0.2, ARRAY['gemini-2.0-flash', 'llama-3.3-70b-versatile'], ARRAY['gpt-4o-mini']),
    ('easy', 0.4, ARRAY['gemini-1.5-flash', 'llama-3.3-70b-versatile'], ARRAY['gpt-4o-mini', 'claude-3-5-haiku-latest']),
    ('medium', 0.6, ARRAY['llama-3.1-70b-versatile', 'deepseek-chat'], ARRAY['gpt-4o', 'claude-3-5-sonnet-latest']),
    ('hard', 0.8, ARRAY['gpt-4o', 'claude-3-5-sonnet-latest'], ARRAY['gpt-4o', 'claude-3-5-sonnet-latest']),
    ('very_hard', 0.9, ARRAY['gpt-4o', 'claude-3-5-sonnet-latest'], ARRAY['gpt-4o', 'claude-3-5-sonnet-latest'])
ON CONFLICT DO NOTHING;
