#!/usr/bin/env python3
"""
Supabase setup script for the Pydantic AI Backend.

This script helps you set up Supabase integration by:
1. Validating configuration
2. Creating required database tables
3. Setting up storage buckets
4. Testing the connection
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config import get_settings
from app.services.supabase_client import get_supabase_service
from app.services.auth_service import get_auth_service
from app.services.database_service import get_database_service
from app.services.storage_service import get_storage_service


def print_header(title: str):
    """Print a formatted header."""
    print(f"\n{'='*50}")
    print(f" {title}")
    print(f"{'='*50}")


def print_step(step: str, status: str = ""):
    """Print a step with optional status."""
    if status:
        print(f"  {step}... {status}")
    else:
        print(f"  {step}...")


def print_success(message: str):
    """Print a success message."""
    print(f"  ✅ {message}")


def print_error(message: str):
    """Print an error message."""
    print(f"  ❌ {message}")


def print_warning(message: str):
    """Print a warning message."""
    print(f"  ⚠️  {message}")


async def validate_configuration():
    """Validate Supabase configuration."""
    print_header("Validating Configuration")
    
    try:
        settings = get_settings()
        
        # Check if Supabase is configured
        if not settings.is_supabase_configured():
            print_error("Supabase is not configured!")
            print("  Please set the following environment variables:")
            print("  - SUPABASE_URL")
            print("  - SUPABASE_ANON_KEY")
            print("  - SUPABASE_SERVICE_ROLE_KEY (optional)")
            return False
        
        print_success("Environment variables are set")
        
        # Test Supabase connection
        supabase_service = get_supabase_service()
        health = supabase_service.health_check()
        
        if health["status"] == "healthy":
            print_success("Supabase connection is working")
            return True
        else:
            print_error(f"Supabase connection failed: {health.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print_error(f"Configuration validation failed: {e}")
        return False


async def create_database_tables():
    """Create required database tables."""
    print_header("Creating Database Tables")

    try:
        # SQL for creating tables
        create_tables_sql = """
        -- Create conversations table for AI chat history
        CREATE TABLE IF NOT EXISTS conversations (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            title TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            metadata JSONB DEFAULT '{}'::jsonb
        );

        -- Create messages table for conversation messages
        CREATE TABLE IF NOT EXISTS messages (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
            role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            metadata JSONB DEFAULT '{}'::jsonb
        );

        -- Enable Row Level Security
        ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
        ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

        -- Create policies for conversations
        DROP POLICY IF EXISTS "Users can view their own conversations" ON conversations;
        CREATE POLICY "Users can view their own conversations" ON conversations
            FOR SELECT USING (auth.uid() = user_id);

        DROP POLICY IF EXISTS "Users can insert their own conversations" ON conversations;
        CREATE POLICY "Users can insert their own conversations" ON conversations
            FOR INSERT WITH CHECK (auth.uid() = user_id);

        DROP POLICY IF EXISTS "Users can update their own conversations" ON conversations;
        CREATE POLICY "Users can update their own conversations" ON conversations
            FOR UPDATE USING (auth.uid() = user_id);

        DROP POLICY IF EXISTS "Users can delete their own conversations" ON conversations;
        CREATE POLICY "Users can delete their own conversations" ON conversations
            FOR DELETE USING (auth.uid() = user_id);

        -- Create policies for messages
        DROP POLICY IF EXISTS "Users can view messages in their conversations" ON messages;
        CREATE POLICY "Users can view messages in their conversations" ON messages
            FOR SELECT USING (
                conversation_id IN (
                    SELECT id FROM conversations WHERE user_id = auth.uid()
                )
            );

        DROP POLICY IF EXISTS "Users can insert messages in their conversations" ON messages;
        CREATE POLICY "Users can insert messages in their conversations" ON messages
            FOR INSERT WITH CHECK (
                conversation_id IN (
                    SELECT id FROM conversations WHERE user_id = auth.uid()
                )
            );
        """

        print_step("Preparing database tables SQL")

        # Try to execute SQL if we have service role key
        supabase_service = get_supabase_service()
        settings = get_settings()

        if settings.supabase_service_role_key and supabase_service.is_available:
            print_step("Attempting to execute SQL with service role key")
            try:
                # Create a client with service role key for admin operations
                from supabase import create_client
                admin_client = create_client(settings.supabase_url, settings.supabase_service_role_key)

                # Execute the SQL
                result = admin_client.rpc('exec_sql', {'sql': create_tables_sql})
                print_success("Database tables created successfully!")
                return True

            except Exception as e:
                print_warning(f"Could not execute SQL automatically: {e}")
                print_warning("Please execute the SQL manually in your Supabase SQL editor")
        else:
            print_warning("Service role key not available. Please execute SQL manually.")

        # Save SQL to file for easy access
        sql_file_path = "supabase_setup.sql"
        with open(sql_file_path, "w") as f:
            f.write(create_tables_sql)

        print_warning(f"SQL saved to {sql_file_path}")
        print_warning("Please execute this SQL in your Supabase SQL editor:")
        print("\n" + create_tables_sql)

        print_success("Table creation SQL provided")
        return True

    except Exception as e:
        print_error(f"Database table creation failed: {e}")
        return False


async def setup_storage_buckets():
    """Set up storage buckets."""
    print_header("Setting Up Storage Buckets")
    
    try:
        storage_service = get_storage_service()
        
        print_step("Creating 'files' bucket")
        
        # Try to create the default bucket
        success = await storage_service.create_bucket("files", public=False)
        
        if success:
            print_success("Files bucket created successfully")
        else:
            print_warning("Files bucket may already exist")
        
        # List existing buckets
        print_step("Listing existing buckets")
        buckets = await storage_service.list_buckets()
        
        print("  Available buckets:")
        for bucket in buckets:
            print(f"    - {bucket.get('name', 'Unknown')} ({'public' if bucket.get('public') else 'private'})")
        
        return True
        
    except Exception as e:
        print_error(f"Storage bucket setup failed: {e}")
        return False


async def test_integration():
    """Test the complete integration."""
    print_header("Testing Integration")
    
    try:
        # Test authentication service
        print_step("Testing authentication service")
        auth_service = get_auth_service()
        print_success("Authentication service is available")
        
        # Test database service
        print_step("Testing database service")
        db_service = get_database_service()
        print_success("Database service is available")
        
        # Test storage service
        print_step("Testing storage service")
        storage_service = get_storage_service()
        print_success("Storage service is available")
        
        print_success("All services are working correctly!")
        return True
        
    except Exception as e:
        print_error(f"Integration test failed: {e}")
        return False


async def main():
    """Main setup function."""
    print("🚀 Supabase Setup Script for Pydantic AI Backend")
    print("This script will help you set up Supabase integration.")
    
    # Step 1: Validate configuration
    if not await validate_configuration():
        print("\n❌ Setup failed. Please fix the configuration issues and try again.")
        return False
    
    # Step 2: Create database tables
    if not await create_database_tables():
        print("\n❌ Database setup failed. Please check the errors above.")
        return False
    
    # Step 3: Set up storage buckets
    if not await setup_storage_buckets():
        print("\n❌ Storage setup failed. Please check the errors above.")
        return False
    
    # Step 4: Test integration
    if not await test_integration():
        print("\n❌ Integration test failed. Please check the errors above.")
        return False
    
    # Success!
    print_header("Setup Complete!")
    print("✅ Supabase integration is ready to use!")
    print("\nNext steps:")
    print("1. Run the example script: python examples/supabase_usage.py")
    print("2. Start your FastAPI server: uvicorn app.main:app --reload")
    print("3. Check the API documentation: http://localhost:8000/docs")
    print("4. Read the full documentation: docs/supabase_integration.md")
    
    return True


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1)
