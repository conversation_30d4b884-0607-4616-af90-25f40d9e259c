#!/usr/bin/env python3
"""
Start the FastAPI server for the Pydantic AI backend.
"""

import uvicorn
from app.main import app

if __name__ == "__main__":
    print("🚀 Starting Pydantic AI Backend with Supabase Integration...")
    print("📚 API Documentation will be available at: http://localhost:8000/docs")
    print("🔗 Supabase endpoints will be available at: http://localhost:8000/api/supabase/")
    print("🏥 Health check: http://localhost:8000/health")
    print("\nPress Ctrl+C to stop the server\n")
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
