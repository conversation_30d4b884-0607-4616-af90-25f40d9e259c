"""
Integration tests for the FastAPI application.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, AsyncMock

from app.main import app
from app.models import ChatResponse, ConversationSummary
from app.config import ProviderType


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


class TestHealthEndpoint:
    """Test health check endpoint."""
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
        assert "dependencies" in data


class TestRootEndpoint:
    """Test root endpoint."""
    
    def test_root_endpoint(self, client):
        """Test root endpoint."""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "docs" in data
        assert "health" in data


class TestToolsEndpoint:
    """Test tools listing endpoint."""
    
    def test_list_tools(self, client):
        """Test tools listing endpoint."""
        response = client.get("/tools")
        
        assert response.status_code == 200
        tools = response.json()
        assert isinstance(tools, list)
        assert len(tools) > 0
        
        # Check that each tool has required fields
        for tool in tools:
            assert "name" in tool
            assert "description" in tool
            assert "example" in tool


class TestChatEndpoint:
    """Test chat completion endpoint."""
    
    @patch('app.agent.get_agent')
    def test_chat_basic(self, mock_get_agent, client):
        """Test basic chat functionality."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="Hello! I'm a test response.",
            conversation_id="test-conv-123",
            model_used="gpt-4o-mini"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent
        
        # Make request
        response = client.post("/chat", json={
            "message": "Hello, AI!"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["response"] == "Hello! I'm a test response."
        assert data["conversation_id"] == "test-conv-123"
        assert data["model_used"] == "gpt-4o-mini"
    
    def test_chat_invalid_request(self, client):
        """Test chat with invalid request."""
        # Empty message should fail validation
        response = client.post("/chat", json={
            "message": ""
        })
        
        assert response.status_code == 422  # Validation error
    
    def test_chat_missing_message(self, client):
        """Test chat with missing message field."""
        response = client.post("/chat", json={})
        
        assert response.status_code == 422  # Validation error
    
    @patch('app.agent.get_agent')
    def test_chat_with_options(self, mock_get_agent, client):
        """Test chat with additional options."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="Custom response",
            conversation_id="custom-conv",
            model_used="gpt-4o-mini"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent
        
        # Make request with options
        response = client.post("/chat", json={
            "message": "Test message",
            "conversation_id": "custom-conv",
            "max_tokens": 500,
            "temperature": 0.5,
            "use_tools": False
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["response"] == "Custom response"
        assert data["conversation_id"] == "custom-conv"


class TestConversationEndpoints:
    """Test conversation management endpoints."""
    
    @patch('app.agent.get_agent')
    def test_get_conversation_not_found(self, mock_get_agent, client):
        """Test getting a non-existent conversation."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_agent.get_conversation_summary.return_value = None
        mock_get_agent.return_value = mock_agent
        
        response = client.get("/conversations/non-existent")
        
        assert response.status_code == 404
    
    @patch('app.agent.get_agent')
    def test_get_conversation_success(self, mock_get_agent, client):
        """Test getting an existing conversation."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_summary = {
            "conversation_id": "test-conv",
            "message_count": 5,
            "created_at": "2024-01-01T00:00:00",
            "last_updated": "2024-01-01T01:00:00"
        }
        mock_agent.get_conversation_summary.return_value = mock_summary
        mock_get_agent.return_value = mock_agent
        
        response = client.get("/conversations/test-conv")
        
        assert response.status_code == 200
        data = response.json()
        assert data["conversation_id"] == "test-conv"
        assert data["message_count"] == 5
    
    @patch('app.agent.get_agent')
    def test_list_conversations(self, mock_get_agent, client):
        """Test listing conversations."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_agent.conversations = {"conv1": None, "conv2": None}
        mock_agent.get_conversation_summary.side_effect = [
            {
                "conversation_id": "conv1",
                "message_count": 3,
                "created_at": "2024-01-01T00:00:00",
                "last_updated": "2024-01-01T00:30:00"
            },
            {
                "conversation_id": "conv2",
                "message_count": 7,
                "created_at": "2024-01-01T01:00:00",
                "last_updated": "2024-01-01T01:45:00"
            }
        ]
        mock_get_agent.return_value = mock_agent
        
        response = client.get("/conversations")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["conversation_id"] == "conv1"
        assert data[1]["conversation_id"] == "conv2"


class TestAgentConfigEndpoints:
    """Test agent configuration endpoints."""
    
    @patch('app.agent.get_agent')
    def test_get_agent_config(self, mock_get_agent, client):
        """Test getting agent configuration."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_agent.config = {
            "model": "gpt-4o-mini",
            "system_prompt": "Test prompt",
            "max_tokens": 1000,
            "temperature": 0.7,
            "enable_tools": True,
            "tools": []
        }
        mock_get_agent.return_value = mock_agent
        
        response = client.get("/agent/config")
        
        assert response.status_code == 200
        data = response.json()
        assert data["model"] == "gpt-4o-mini"
        assert data["max_tokens"] == 1000
    
    def test_update_agent_config(self, client):
        """Test updating agent configuration."""
        config_data = {
            "model": "gpt-4",
            "system_prompt": "Updated prompt",
            "max_tokens": 2000,
            "temperature": 0.5,
            "enable_tools": False,
            "tools": ["calculator"]
        }
        
        response = client.post("/agent/config", json=config_data)

        assert response.status_code == 200
        data = response.json()
        assert "message" in data


class TestMultiProviderEndpoints:
    """Test multi-provider specific endpoints."""

    @patch('app.agent.get_agent')
    def test_chat_with_provider_selection(self, mock_get_agent, client):
        """Test chat endpoint with provider selection."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="Hello from Groq!",
            conversation_id="test-conv-123",
            model_used="llama-3.3-70b-versatile"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent

        # Make request with provider selection
        response = client.post("/chat", json={
            "message": "Hello, AI!",
            "provider": "groq",
            "model": "llama-3.3-70b-versatile"
        })

        assert response.status_code == 200
        data = response.json()
        assert data["response"] == "Hello from Groq!"
        assert data["model_used"] == "llama-3.3-70b-versatile"

        # Verify agent.chat was called with provider
        mock_agent.chat.assert_called_once()
        call_args = mock_agent.chat.call_args
        assert call_args.kwargs["provider"] == ProviderType.GROQ
        assert call_args.kwargs["model_name"] == "llama-3.3-70b-versatile"

    def test_chat_with_invalid_provider(self, client):
        """Test chat endpoint with invalid provider."""
        response = client.post("/chat", json={
            "message": "Hello, AI!",
            "provider": "invalid_provider"
        })

        assert response.status_code == 400
        data = response.json()
        assert "Invalid provider" in data["detail"]

    @patch('app.agent.get_agent')
    def test_providers_endpoint(self, mock_get_agent, client):
        """Test providers information endpoint."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_agent.get_available_models.return_value = {
            ProviderType.OPENAI: ["gpt-4o-mini", "gpt-4o"],
            ProviderType.GROQ: ["llama-3.3-70b-versatile"]
        }
        mock_agent.get_current_provider_info.return_value = {
            "provider": "openai",
            "model": "gpt-4o-mini",
            "fallback_active": True,
            "available_providers": ["openai", "groq"]
        }
        mock_agent.get_recommended_free_models.return_value = [
            "gpt-4o-mini", "llama-3.3-70b-versatile"
        ]
        mock_get_agent.return_value = mock_agent

        response = client.get("/providers")

        assert response.status_code == 200
        data = response.json()
        assert "current_provider" in data
        assert "available_providers" in data
        assert "recommended_free_models" in data
        assert "provider_configs" in data
        assert data["current_provider"] == "openai"
        assert "openai" in data["available_providers"]
        assert "groq" in data["available_providers"]

    @patch('app.agent.get_agent')
    def test_health_check_with_provider_info(self, mock_get_agent, client):
        """Test health check includes provider information."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_agent.get_current_provider_info.return_value = {
            "provider": "openai",
            "model": "gpt-4o-mini",
            "fallback_active": True,
            "available_providers": ["openai", "groq"]
        }
        mock_get_agent.return_value = mock_agent

        response = client.get("/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "current_provider" in data["dependencies"]
        assert "current_model" in data["dependencies"]
        assert "available_providers" in data["dependencies"]
        assert "fallback_enabled" in data["dependencies"]


class TestContextPromptEndpoint:
    """Test context-prompt chat completion endpoint."""

    @patch('app.agent.get_agent')
    def test_context_prompt_basic(self, mock_get_agent, client):
        """Test basic context-prompt functionality."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="Paris is the capital of France.",
            conversation_id="test-conv-123",
            model_used="google:gemini-2.0-flash"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent

        # Make request
        response = client.post("/chat/context-prompt", json={
            "context": "You are a geography expert who provides accurate information about world capitals.",
            "prompt": "What is the capital of France?"
        })

        assert response.status_code == 200
        data = response.json()
        assert data["response"] == "Paris is the capital of France."
        assert data["conversation_id"] == "test-conv-123"
        assert data["model_used"] == "google:gemini-2.0-flash"

        # Verify agent.chat was called with correct parameters
        mock_agent.chat.assert_called_once()
        call_args = mock_agent.chat.call_args
        assert call_args.kwargs["message"] == "What is the capital of France?"
        assert call_args.kwargs["system_prompt"] == "You are a geography expert who provides accurate information about world capitals."

    @patch('app.agent.get_agent')
    def test_context_prompt_with_provider(self, mock_get_agent, client):
        """Test context-prompt with specific provider."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="The factorial function calculates n! = n × (n-1) × ... × 1",
            conversation_id="test-conv-456",
            model_used="groq:llama-3.3-70b-versatile"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent

        # Make request with provider
        response = client.post("/chat/context-prompt", json={
            "context": "You are a programming tutor who explains concepts clearly.",
            "prompt": "Explain what a factorial function does.",
            "provider": "groq",
            "max_tokens": 500,
            "temperature": 0.3
        })

        assert response.status_code == 200
        data = response.json()
        assert data["response"] == "The factorial function calculates n! = n × (n-1) × ... × 1"
        assert data["model_used"] == "groq:llama-3.3-70b-versatile"

        # Verify agent.chat was called with provider
        mock_agent.chat.assert_called_once()
        call_args = mock_agent.chat.call_args
        assert call_args.kwargs["provider"] == ProviderType.GROQ
        assert call_args.kwargs["max_tokens"] == 500
        assert call_args.kwargs["temperature"] == 0.3

    def test_context_prompt_empty_context(self, client):
        """Test context-prompt with empty context."""
        response = client.post("/chat/context-prompt", json={
            "context": "",
            "prompt": "What is the capital of France?"
        })

        assert response.status_code == 422  # Validation error
        data = response.json()
        assert "Context cannot be empty" in str(data["detail"])

    def test_context_prompt_empty_prompt(self, client):
        """Test context-prompt with empty prompt."""
        response = client.post("/chat/context-prompt", json={
            "context": "You are a helpful assistant.",
            "prompt": ""
        })

        assert response.status_code == 422  # Validation error
        data = response.json()
        assert "Prompt cannot be empty" in str(data["detail"])

    def test_context_prompt_missing_fields(self, client):
        """Test context-prompt with missing required fields."""
        # Missing context
        response = client.post("/chat/context-prompt", json={
            "prompt": "What is the capital of France?"
        })
        assert response.status_code == 422

        # Missing prompt
        response = client.post("/chat/context-prompt", json={
            "context": "You are a helpful assistant."
        })
        assert response.status_code == 422

    def test_context_prompt_invalid_provider(self, client):
        """Test context-prompt with invalid provider."""
        response = client.post("/chat/context-prompt", json={
            "context": "You are a helpful assistant.",
            "prompt": "What is the capital of France?",
            "provider": "invalid_provider"
        })

        assert response.status_code == 422  # Validation error
        data = response.json()
        assert "Provider must be one of" in str(data["detail"])

    @patch('app.agent.get_agent')
    def test_context_prompt_with_conversation_id(self, mock_get_agent, client):
        """Test context-prompt with conversation ID."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="Continuing our conversation about Python.",
            conversation_id="existing-conv-789",
            model_used="google:gemini-2.0-flash"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent

        # Make request with conversation ID
        response = client.post("/chat/context-prompt", json={
            "context": "We were discussing Python programming concepts.",
            "prompt": "Can you continue explaining list comprehensions?",
            "conversation_id": "existing-conv-789"
        })

        assert response.status_code == 200
        data = response.json()
        assert data["conversation_id"] == "existing-conv-789"

        # Verify agent.chat was called with conversation_id
        mock_agent.chat.assert_called_once()
        call_args = mock_agent.chat.call_args
        assert call_args.kwargs["conversation_id"] == "existing-conv-789"

    @patch('app.agent.get_agent')
    def test_context_prompt_tools_disabled(self, mock_get_agent, client):
        """Test context-prompt with tools disabled."""
        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="I'll provide a text-only response without using tools.",
            conversation_id="test-conv-no-tools",
            model_used="google:gemini-2.0-flash"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent

        # Make request with tools disabled
        response = client.post("/chat/context-prompt", json={
            "context": "You are a simple text assistant.",
            "prompt": "Calculate 2 + 2",
            "use_tools": False
        })

        assert response.status_code == 200
        data = response.json()
        assert data["response"] == "I'll provide a text-only response without using tools."

    @patch('app.config.get_available_providers')
    @patch('app.agent.get_agent')
    def test_context_prompt_provider_fallback(self, mock_get_agent, mock_get_providers, client):
        """Test context-prompt provider fallback logic."""
        # Mock available providers (Google available)
        mock_get_providers.return_value = [ProviderType.GOOGLE, ProviderType.GROQ]

        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="Response from Google provider.",
            conversation_id="test-conv-fallback",
            model_used="google:gemini-2.0-flash"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent

        # Make request without specifying provider (should default to Google)
        response = client.post("/chat/context-prompt", json={
            "context": "You are a helpful assistant.",
            "prompt": "Hello, how are you?"
        })

        assert response.status_code == 200

        # Verify agent.chat was called with Google provider
        mock_agent.chat.assert_called_once()
        call_args = mock_agent.chat.call_args
        assert call_args.kwargs["provider"] == ProviderType.GOOGLE

    @patch('app.config.get_available_providers')
    @patch('app.agent.get_agent')
    def test_context_prompt_groq_fallback(self, mock_get_agent, mock_get_providers, client):
        """Test context-prompt falls back to Groq when Google unavailable."""
        # Mock available providers (only Groq available)
        mock_get_providers.return_value = [ProviderType.GROQ]

        # Mock the agent
        mock_agent = AsyncMock()
        mock_response = ChatResponse(
            response="Response from Groq provider.",
            conversation_id="test-conv-groq",
            model_used="groq:llama-3.3-70b-versatile"
        )
        mock_agent.chat.return_value = mock_response
        mock_get_agent.return_value = mock_agent

        # Make request without specifying provider (should fallback to Groq)
        response = client.post("/chat/context-prompt", json={
            "context": "You are a helpful assistant.",
            "prompt": "Hello, how are you?"
        })

        assert response.status_code == 200

        # Verify agent.chat was called with Groq provider
        mock_agent.chat.assert_called_once()
        call_args = mock_agent.chat.call_args
        assert call_args.kwargs["provider"] == ProviderType.GROQ

    @patch('app.config.get_available_providers')
    def test_context_prompt_no_providers(self, mock_get_providers, client):
        """Test context-prompt when no providers are available."""
        # Mock no available providers
        mock_get_providers.return_value = []

        # Make request
        response = client.post("/chat/context-prompt", json={
            "context": "You are a helpful assistant.",
            "prompt": "Hello, how are you?"
        })

        assert response.status_code == 503
        data = response.json()
        assert "No AI providers are currently available" in data["detail"]

    @patch('app.agent.get_agent')
    def test_context_prompt_agent_error(self, mock_get_agent, client):
        """Test context-prompt when agent throws an error."""
        # Mock the agent to throw an error
        mock_agent = AsyncMock()
        mock_agent.chat.side_effect = Exception("Agent processing error")
        mock_get_agent.return_value = mock_agent

        # Make request
        response = client.post("/chat/context-prompt", json={
            "context": "You are a helpful assistant.",
            "prompt": "Hello, how are you?"
        })

        assert response.status_code == 500
        data = response.json()
        assert "Error processing context-prompt request" in data["detail"]
