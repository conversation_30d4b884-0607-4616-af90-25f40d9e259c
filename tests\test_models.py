"""
Tests for Pydantic models.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from app.models import (
    ChatMessage,
    ChatRequest,
    ChatResponse,
    ContextPromptRequest,
    MessageRole,
    AgentConfig,
    HealthResponse,
    ErrorResponse,
    WeatherInfo,
    CalculationResult,
)


class TestChatMessage:
    """Test ChatMessage model."""
    
    def test_valid_chat_message(self):
        """Test creating a valid chat message."""
        message = ChatMessage(
            role=MessageRole.USER,
            content="Hello, world!"
        )
        
        assert message.role == MessageRole.USER
        assert message.content == "Hello, world!"
        assert isinstance(message.timestamp, datetime)
        assert message.metadata is None
    
    def test_chat_message_with_metadata(self):
        """Test chat message with metadata."""
        metadata = {"source": "test", "priority": "high"}
        message = ChatMessage(
            role=MessageRole.ASSISTANT,
            content="Response",
            metadata=metadata
        )
        
        assert message.metadata == metadata


class TestChatRequest:
    """Test ChatRequest model."""
    
    def test_valid_chat_request(self):
        """Test creating a valid chat request."""
        request = ChatRequest(message="Hello, AI!")
        
        assert request.message == "Hello, AI!"
        assert request.conversation_id is None
        assert request.max_tokens == 1000
        assert request.temperature == 0.7
        assert request.use_tools is True
    
    def test_empty_message_validation(self):
        """Test that empty messages are rejected."""
        with pytest.raises(ValidationError):
            ChatRequest(message="")
        
        with pytest.raises(ValidationError):
            ChatRequest(message="   ")
    
    def test_token_limits(self):
        """Test token limit validation."""
        # Valid range
        request = ChatRequest(message="test", max_tokens=500)
        assert request.max_tokens == 500
        
        # Invalid range
        with pytest.raises(ValidationError):
            ChatRequest(message="test", max_tokens=0)
        
        with pytest.raises(ValidationError):
            ChatRequest(message="test", max_tokens=5000)
    
    def test_temperature_limits(self):
        """Test temperature validation."""
        # Valid range
        request = ChatRequest(message="test", temperature=1.5)
        assert request.temperature == 1.5
        
        # Invalid range
        with pytest.raises(ValidationError):
            ChatRequest(message="test", temperature=-0.1)
        
        with pytest.raises(ValidationError):
            ChatRequest(message="test", temperature=2.1)


class TestChatResponse:
    """Test ChatResponse model."""
    
    def test_valid_chat_response(self):
        """Test creating a valid chat response."""
        response = ChatResponse(
            response="Hello, human!",
            conversation_id="test-conv-123",
            model_used="gpt-4o-mini"
        )
        
        assert response.response == "Hello, human!"
        assert response.conversation_id == "test-conv-123"
        assert response.model_used == "gpt-4o-mini"
        assert isinstance(response.timestamp, datetime)


class TestAgentConfig:
    """Test AgentConfig model."""
    
    def test_default_config(self):
        """Test default agent configuration."""
        config = AgentConfig()
        
        assert config.model == "gpt-4o-mini"
        assert config.max_tokens == 1000
        assert config.temperature == 0.7
        assert config.enable_tools is True
        assert config.tools == []
    
    def test_custom_config(self):
        """Test custom agent configuration."""
        config = AgentConfig(
            model="gpt-4",
            max_tokens=2000,
            temperature=0.5,
            enable_tools=False,
            tools=["calculator", "weather"]
        )
        
        assert config.model == "gpt-4"
        assert config.max_tokens == 2000
        assert config.temperature == 0.5
        assert config.enable_tools is False
        assert config.tools == ["calculator", "weather"]


class TestHealthResponse:
    """Test HealthResponse model."""
    
    def test_default_health_response(self):
        """Test default health response."""
        health = HealthResponse()
        
        assert health.status == "healthy"
        assert health.version == "1.0.0"
        assert isinstance(health.timestamp, datetime)
        assert health.dependencies == {}


class TestErrorResponse:
    """Test ErrorResponse model."""
    
    def test_error_response(self):
        """Test error response creation."""
        error = ErrorResponse(
            error="Test error",
            detail="This is a test error",
            request_id="req-123"
        )
        
        assert error.error == "Test error"
        assert error.detail == "This is a test error"
        assert error.request_id == "req-123"
        assert isinstance(error.timestamp, datetime)


class TestWeatherInfo:
    """Test WeatherInfo model."""
    
    def test_weather_info(self):
        """Test weather info creation."""
        weather = WeatherInfo(
            location="New York",
            temperature=22.5,
            description="Sunny",
            humidity=65,
            wind_speed=10.2
        )
        
        assert weather.location == "New York"
        assert weather.temperature == 22.5
        assert weather.description == "Sunny"
        assert weather.humidity == 65
        assert weather.wind_speed == 10.2
        assert isinstance(weather.timestamp, datetime)


class TestCalculationResult:
    """Test CalculationResult model."""
    
    def test_successful_calculation(self):
        """Test successful calculation result."""
        result = CalculationResult(
            expression="2 + 2",
            result=4
        )
        
        assert result.expression == "2 + 2"
        assert result.result == 4
        assert result.error is None
        assert isinstance(result.timestamp, datetime)
    
    def test_failed_calculation(self):
        """Test failed calculation result."""
        result = CalculationResult(
            expression="invalid",
            result="Error",
            error="Invalid expression"
        )
        
        assert result.expression == "invalid"
        assert result.result == "Error"
        assert result.error == "Invalid expression"


class TestContextPromptRequest:
    """Test ContextPromptRequest model."""

    def test_valid_context_prompt_request(self):
        """Test creating a valid context-prompt request."""
        request = ContextPromptRequest(
            context="You are a helpful assistant that provides accurate information.",
            prompt="What is the capital of France?"
        )

        assert request.context == "You are a helpful assistant that provides accurate information."
        assert request.prompt == "What is the capital of France?"
        assert request.provider is None
        assert request.max_tokens == 1000
        assert request.temperature == 0.7
        assert request.conversation_id is None
        assert request.use_tools is True

    def test_context_prompt_request_with_options(self):
        """Test context-prompt request with all options."""
        request = ContextPromptRequest(
            context="You are a coding assistant.",
            prompt="Write a Python function to calculate factorial.",
            provider="google",
            max_tokens=2000,
            temperature=0.5,
            conversation_id="test-conv-123",
            use_tools=False
        )

        assert request.context == "You are a coding assistant."
        assert request.prompt == "Write a Python function to calculate factorial."
        assert request.provider == "google"
        assert request.max_tokens == 2000
        assert request.temperature == 0.5
        assert request.conversation_id == "test-conv-123"
        assert request.use_tools is False

    def test_empty_context_validation(self):
        """Test validation fails for empty context."""
        with pytest.raises(ValidationError) as exc_info:
            ContextPromptRequest(
                context="",
                prompt="What is the capital of France?"
            )

        assert "Context cannot be empty" in str(exc_info.value)

    def test_empty_prompt_validation(self):
        """Test validation fails for empty prompt."""
        with pytest.raises(ValidationError) as exc_info:
            ContextPromptRequest(
                context="You are a helpful assistant.",
                prompt=""
            )

        assert "Prompt cannot be empty" in str(exc_info.value)

    def test_whitespace_context_validation(self):
        """Test validation fails for whitespace-only context."""
        with pytest.raises(ValidationError) as exc_info:
            ContextPromptRequest(
                context="   \n\t   ",
                prompt="What is the capital of France?"
            )

        assert "Context cannot be empty" in str(exc_info.value)

    def test_whitespace_prompt_validation(self):
        """Test validation fails for whitespace-only prompt."""
        with pytest.raises(ValidationError) as exc_info:
            ContextPromptRequest(
                context="You are a helpful assistant.",
                prompt="   \n\t   "
            )

        assert "Prompt cannot be empty" in str(exc_info.value)

    def test_invalid_provider_validation(self):
        """Test validation fails for invalid provider."""
        with pytest.raises(ValidationError) as exc_info:
            ContextPromptRequest(
                context="You are a helpful assistant.",
                prompt="What is the capital of France?",
                provider="invalid_provider"
            )

        assert "Provider must be one of" in str(exc_info.value)

    def test_valid_providers(self):
        """Test all valid providers are accepted."""
        valid_providers = ['openai', 'anthropic', 'google', 'groq', 'mistral', 'cohere', 'huggingface', 'openrouter', 'deepseek', 'together']

        for provider in valid_providers:
            request = ContextPromptRequest(
                context="You are a helpful assistant.",
                prompt="What is the capital of France?",
                provider=provider
            )
            assert request.provider == provider.lower()

    def test_max_tokens_validation(self):
        """Test max_tokens validation."""
        # Test minimum boundary
        with pytest.raises(ValidationError):
            ContextPromptRequest(
                context="You are a helpful assistant.",
                prompt="What is the capital of France?",
                max_tokens=0
            )

        # Test maximum boundary
        with pytest.raises(ValidationError):
            ContextPromptRequest(
                context="You are a helpful assistant.",
                prompt="What is the capital of France?",
                max_tokens=5000
            )

        # Test valid values
        request = ContextPromptRequest(
            context="You are a helpful assistant.",
            prompt="What is the capital of France?",
            max_tokens=1500
        )
        assert request.max_tokens == 1500

    def test_temperature_validation(self):
        """Test temperature validation."""
        # Test minimum boundary
        with pytest.raises(ValidationError):
            ContextPromptRequest(
                context="You are a helpful assistant.",
                prompt="What is the capital of France?",
                temperature=-0.1
            )

        # Test maximum boundary
        with pytest.raises(ValidationError):
            ContextPromptRequest(
                context="You are a helpful assistant.",
                prompt="What is the capital of France?",
                temperature=2.1
            )

        # Test valid values
        request = ContextPromptRequest(
            context="You are a helpful assistant.",
            prompt="What is the capital of France?",
            temperature=1.0
        )
        assert request.temperature == 1.0
