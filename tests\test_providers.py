"""
Tests for multi-provider functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock

from app.config import ProviderType, Settings
from app.providers import ProviderFactory
from app.agent import PydanticAIAgent, create_agent
from app.models import AgentConfig


class TestProviderFactory:
    """Test ProviderFactory functionality."""
    
    @patch('app.providers.get_settings')
    def test_factory_initialization(self, mock_get_settings):
        """Test factory initialization with mock settings."""
        mock_settings = Mock()
        mock_settings.get_provider_configs.return_value = {
            ProviderType.OPENAI: Mock(api_key="test-key", models=["gpt-4o-mini"])
        }
        mock_get_settings.return_value = mock_settings
        
        factory = ProviderFactory()
        assert factory.settings == mock_settings
        assert ProviderType.OPENAI in factory.provider_configs
    
    @patch('app.providers.get_settings')
    @patch('app.providers.OpenAIModel')
    @patch('app.providers.OpenAIProvider')
    def test_create_openai_model(self, mock_provider_class, mock_model_class, mock_get_settings):
        """Test OpenAI model creation."""
        # Setup mocks
        mock_settings = Mock()
        mock_config = Mock()
        mock_config.api_key = "test-key"
        mock_config.models = ["gpt-4o-mini"]
        mock_config.timeout = None
        mock_config.max_retries = 3
        mock_settings.get_provider_configs.return_value = {
            ProviderType.OPENAI: mock_config
        }
        mock_settings.max_tokens = 1000
        mock_settings.temperature = 0.7
        mock_settings.prefer_free_models = False
        mock_get_settings.return_value = mock_settings
        
        mock_provider = Mock()
        mock_provider_class.return_value = mock_provider
        mock_model = Mock()
        mock_model_class.return_value = mock_model
        
        # Test
        factory = ProviderFactory()
        result = factory.create_model(ProviderType.OPENAI, "gpt-4o-mini")
        
        # Verify
        mock_provider_class.assert_called_once()
        mock_model_class.assert_called_once()
        assert result == mock_model
    
    @patch('app.providers.get_settings')
    def test_create_model_invalid_provider(self, mock_get_settings):
        """Test error handling for invalid provider."""
        mock_settings = Mock()
        mock_settings.get_provider_configs.return_value = {}
        mock_get_settings.return_value = mock_settings
        
        factory = ProviderFactory()
        
        with pytest.raises(ValueError, match="Provider .* is not configured"):
            factory.create_model(ProviderType.OPENAI)
    
    @patch('app.providers.get_settings')
    def test_create_fallback_model(self, mock_get_settings):
        """Test fallback model creation."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.primary_provider = ProviderType.OPENAI
        mock_settings.fallback_providers = [ProviderType.GROQ]
        mock_get_settings.return_value = mock_settings
        
        factory = ProviderFactory()

        # Mock the create_model method to return different models
        with patch.object(factory, 'create_model') as mock_create:
            mock_primary = Mock()
            mock_fallback_model = Mock()
            mock_create.side_effect = [mock_primary, mock_fallback_model]

            # Mock the actual fallback model creation
            with patch('app.providers.SimpleFallbackModel') as mock_fallback_class:
                mock_fallback = Mock()
                mock_fallback_class.return_value = mock_fallback

                result = factory.create_fallback_model()

                # Verify
                assert mock_create.call_count == 2
                mock_fallback_class.assert_called_once_with(mock_primary, mock_fallback_model)
                assert result == mock_fallback
    
    @patch('app.providers.get_settings')
    def test_get_recommended_model_free_preference(self, mock_get_settings):
        """Test recommended model selection with free preference."""
        mock_settings = Mock()
        mock_config = Mock()
        mock_config.models = ["gpt-4o-mini", "gpt-4o"]
        mock_config.priority = 1
        mock_settings.get_provider_configs.return_value = {
            ProviderType.OPENAI: mock_config
        }
        mock_settings.prefer_free_models = True
        mock_settings.free_models = ["gpt-4o-mini"]
        mock_get_settings.return_value = mock_settings
        
        factory = ProviderFactory()
        provider, model = factory.get_recommended_model(prefer_free=True)
        
        assert provider == ProviderType.OPENAI
        assert model == "gpt-4o-mini"


class TestMultiProviderAgent:
    """Test PydanticAIAgent with multi-provider support."""
    
    @patch('app.agent.get_provider_factory')
    @patch('app.agent.get_settings')
    def test_agent_initialization_with_provider(self, mock_get_settings, mock_get_factory):
        """Test agent initialization with specific provider."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.primary_provider = ProviderType.OPENAI
        mock_settings.enable_fallback = True
        mock_get_settings.return_value = mock_settings
        
        mock_factory = Mock()
        mock_model = Mock()
        mock_factory.create_fallback_model.return_value = mock_model
        mock_get_factory.return_value = mock_factory
        
        # Mock Agent class
        with patch('app.agent.Agent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent_class.return_value = mock_agent
            
            # Test
            config = AgentConfig(model="gpt-4o-mini")
            agent = PydanticAIAgent(
                config=config,
                provider=ProviderType.GROQ,
                model_name="llama-3.3-70b-versatile"
            )
            
            # Verify
            assert agent.provider == ProviderType.GROQ
            assert agent.model_name == "llama-3.3-70b-versatile"
            mock_factory.create_fallback_model.assert_called_once()
    
    @patch('app.agent.get_provider_factory')
    @patch('app.agent.get_settings')
    def test_switch_provider(self, mock_get_settings, mock_get_factory):
        """Test provider switching functionality."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.primary_provider = ProviderType.OPENAI
        mock_settings.enable_fallback = False
        mock_get_settings.return_value = mock_settings
        
        mock_factory = Mock()
        mock_initial_model = Mock()
        mock_new_model = Mock()
        mock_factory.create_model.side_effect = [mock_initial_model, mock_new_model]
        mock_factory.provider_configs = {
            ProviderType.OPENAI: Mock(models=["gpt-4o-mini"]),
            ProviderType.GROQ: Mock(models=["llama-3.3-70b-versatile"])
        }
        mock_get_factory.return_value = mock_factory
        
        # Mock Agent class
        with patch('app.agent.Agent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent_class.return_value = mock_agent
            
            # Create agent
            agent = PydanticAIAgent(provider=ProviderType.OPENAI)
            
            # Test provider switch
            result = agent.switch_provider(ProviderType.GROQ, "llama-3.3-70b-versatile")
            
            # Verify
            assert result is True
            assert agent.provider == ProviderType.GROQ
            assert agent.model_name == "llama-3.3-70b-versatile"
            assert mock_factory.create_model.call_count == 2
    
    @patch('app.agent.get_provider_factory')
    @patch('app.agent.get_settings')
    def test_switch_provider_failure(self, mock_get_settings, mock_get_factory):
        """Test provider switching failure handling."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.primary_provider = ProviderType.OPENAI
        mock_settings.enable_fallback = False
        mock_get_settings.return_value = mock_settings
        
        mock_factory = Mock()
        mock_initial_model = Mock()
        mock_factory.create_model.side_effect = [mock_initial_model, ValueError("Provider not available")]
        mock_get_factory.return_value = mock_factory
        
        # Mock Agent class
        with patch('app.agent.Agent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent_class.return_value = mock_agent
            
            # Create agent
            agent = PydanticAIAgent(provider=ProviderType.OPENAI)
            original_provider = agent.provider
            
            # Test failed provider switch
            result = agent.switch_provider(ProviderType.GROQ)
            
            # Verify
            assert result is False
            assert agent.provider == original_provider  # Should remain unchanged
    
    @patch('app.agent.get_provider_factory')
    @patch('app.agent.get_settings')
    def test_get_provider_info(self, mock_get_settings, mock_get_factory):
        """Test getting current provider information."""
        # Setup mocks
        mock_settings = Mock()
        mock_settings.primary_provider = ProviderType.OPENAI
        mock_settings.enable_fallback = True
        mock_get_settings.return_value = mock_settings
        
        mock_factory = Mock()
        mock_model = Mock()
        mock_factory.create_fallback_model.return_value = mock_model
        mock_factory.provider_configs = {
            ProviderType.OPENAI: Mock(),
            ProviderType.GROQ: Mock()
        }
        mock_get_factory.return_value = mock_factory
        
        # Mock Agent class and SimpleFallbackModel
        with patch('app.agent.Agent') as mock_agent_class:
            with patch('app.agent.SimpleFallbackModel') as mock_fallback_class:
                mock_agent = Mock()
                mock_agent_class.return_value = mock_agent
                
                # Make isinstance return True for FallbackModel
                def mock_isinstance(obj, cls):
                    return cls == mock_fallback_class
                
                with patch('builtins.isinstance', side_effect=mock_isinstance):
                    # Create agent
                    agent = PydanticAIAgent(provider=ProviderType.OPENAI, model_name="gpt-4o-mini")
                    
                    # Test
                    info = agent.get_current_provider_info()
                    
                    # Verify
                    assert info["provider"] == ProviderType.OPENAI.value
                    assert info["model"] == "gpt-4o-mini"
                    assert info["use_fallback"] is True
                    assert "available_providers" in info


class TestCreateAgent:
    """Test agent creation functions."""
    
    @patch('app.agent.PydanticAIAgent')
    def test_create_agent_with_provider(self, mock_agent_class):
        """Test creating agent with specific provider."""
        mock_agent = Mock()
        mock_agent_class.return_value = mock_agent
        
        config = AgentConfig(model="gpt-4o-mini")
        result = create_agent(
            config=config,
            provider=ProviderType.GROQ,
            model_name="llama-3.3-70b-versatile",
            use_fallback=False
        )
        
        mock_agent_class.assert_called_once_with(
            config=config,
            provider=ProviderType.GROQ,
            model_name="llama-3.3-70b-versatile",
            use_fallback=False
        )
        assert result == mock_agent
