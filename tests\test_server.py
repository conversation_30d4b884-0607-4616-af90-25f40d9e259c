#!/usr/bin/env python3
"""
Test if the FastAPI server is running and accessible.
"""

import requests
import time

def test_server():
    """Test if the server is running."""
    try:
        print("🔍 Testing server connection...")
        response = requests.get("http://localhost:8000/", timeout=5)
        print(f"✅ Server is running! Status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {e}")
        return False

def test_health_endpoint():
    """Test the health endpoint."""
    try:
        print("🏥 Testing health endpoint...")
        response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"✅ Health endpoint: {response.status_code} - {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_supabase_health():
    """Test the Supabase health endpoint."""
    try:
        print("🔗 Testing Supabase health endpoint...")
        response = requests.get("http://localhost:8000/api/supabase/health", timeout=5)
        print(f"✅ Supabase health: {response.status_code} - {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Supabase health error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Server Test Suite\n")
    
    # Wait a moment for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(2)
    
    success = True
    success &= test_server()
    success &= test_health_endpoint()
    success &= test_supabase_health()
    
    if success:
        print("\n✅ All server tests passed!")
        print("🌐 Server is running at: http://localhost:8000")
        print("📚 API Documentation: http://localhost:8000/docs")
    else:
        print("\n❌ Some server tests failed.")
        print("Make sure the server is running with: uvicorn app.main:app --reload")
