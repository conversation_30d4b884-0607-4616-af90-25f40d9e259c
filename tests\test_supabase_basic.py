#!/usr/bin/env python3
"""
Basic Supabase connection test without realtime features.
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_basic_connection():
    """Test basic Supabase connection without realtime."""
    try:
        # Try importing just the basic supabase client
        from supabase import create_client
        
        # Get credentials from environment
        url = "https://ejzhuraprnisfslhnagd.supabase.co"
        key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqemh1cmFwcm5pc2ZzbGhuYWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1NDMwNTMsImV4cCI6MjA2OTExOTA1M30.tFrn26J1EDBQzJ1VDdVBfkV70zL0t1rv1sAT5iBrIZ4"
        
        print("🔗 Testing Supabase connection...")
        
        # Create client
        supabase = create_client(url, key)
        print("✅ Supabase client created successfully")
        
        # Test database connection by querying conversations table
        print("📊 Testing database connection...")
        result = supabase.table('conversations').select('*').limit(1).execute()
        print(f"✅ Database connection successful. Found {len(result.data)} conversations")
        
        # Test auth connection
        print("🔐 Testing auth connection...")
        auth_result = supabase.auth.get_session()
        print("✅ Auth connection successful")
        
        print("\n🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_table_schema():
    """Test that our tables exist with correct schema."""
    try:
        from supabase import create_client
        
        url = "https://ejzhuraprnisfslhnagd.supabase.co"
        key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqemh1cmFwcm5pc2ZzbGhuYWdkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1NDMwNTMsImV4cCI6MjA2OTExOTA1M30.tFrn26J1EDBQzJ1VDdVBfkV70zL0t1rv1sAT5iBrIZ4"
        
        supabase = create_client(url, key)
        
        print("🔍 Testing table schema...")
        
        # Test conversations table
        conversations = supabase.table('conversations').select('*').limit(1).execute()
        print(f"✅ Conversations table accessible: {len(conversations.data)} records")
        
        # Test messages table  
        messages = supabase.table('messages').select('*').limit(1).execute()
        print(f"✅ Messages table accessible: {len(messages.data)} records")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Basic Supabase Integration Test\n")
    
    success = True
    success &= test_basic_connection()
    print()
    success &= test_table_schema()
    
    if success:
        print("\n✅ All tests passed! Supabase integration is working.")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
