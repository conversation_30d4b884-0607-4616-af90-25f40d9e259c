"""
Tests for AI tools.
"""

import pytest
import re
from app.tools import CalculatorTool, WeatherTool, UtilityTool, TextTool


class TestCalculatorTool:
    """Test CalculatorTool functionality."""
    
    def test_basic_arithmetic(self):
        """Test basic arithmetic operations."""
        result = CalculatorTool.calculate("2 + 2")
        assert result.result == 4
        assert result.error is None
        
        result = CalculatorTool.calculate("10 - 3")
        assert result.result == 7
        
        result = CalculatorTool.calculate("6 * 7")
        assert result.result == 42
        
        result = CalculatorTool.calculate("15 / 3")
        assert result.result == 5
    
    def test_math_functions(self):
        """Test mathematical functions."""
        result = CalculatorTool.calculate("sqrt(16)")
        assert result.result == 4
        
        result = CalculatorTool.calculate("sin(0)")
        assert abs(result.result) < 1e-10  # sin(0) should be very close to 0
        
        result = CalculatorTool.calculate("cos(0)")
        assert abs(result.result - 1) < 1e-10  # cos(0) should be very close to 1
    
    def test_constants(self):
        """Test mathematical constants."""
        result = CalculatorTool.calculate("pi")
        assert abs(result.result - 3.14159) < 0.001
        
        result = CalculatorTool.calculate("e")
        assert abs(result.result - 2.71828) < 0.001
    
    def test_invalid_expression(self):
        """Test handling of invalid expressions."""
        result = CalculatorTool.calculate("invalid_expression")
        assert result.result == "Error"
        assert result.error is not None
        
        result = CalculatorTool.calculate("1 / 0")
        assert result.result == "Error"
        assert result.error is not None


class TestWeatherTool:
    """Test WeatherTool functionality."""
    
    def test_get_weather(self):
        """Test weather information retrieval."""
        weather = WeatherTool.get_weather("New York")
        
        assert weather.location == "New York"
        assert isinstance(weather.temperature, float)
        assert -50 <= weather.temperature <= 50  # Reasonable temperature range
        assert weather.description in [
            "Sunny", "Cloudy", "Partly Cloudy", "Rainy", "Stormy", "Snowy"
        ]
        assert 0 <= weather.humidity <= 100
        assert 0 <= weather.wind_speed <= 50


class TestUtilityTool:
    """Test UtilityTool functionality."""
    
    def test_generate_uuid(self):
        """Test UUID generation."""
        uuid1 = UtilityTool.generate_uuid()
        uuid2 = UtilityTool.generate_uuid()
        
        # UUIDs should be different
        assert uuid1 != uuid2
        
        # Should be valid UUID format
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        assert re.match(uuid_pattern, uuid1)
        assert re.match(uuid_pattern, uuid2)
    
    def test_get_current_time(self):
        """Test current time retrieval."""
        time_str = UtilityTool.get_current_time()
        
        # Should be ISO format
        assert 'T' in time_str
        assert len(time_str) > 10
    
    def test_base64_encoding(self):
        """Test base64 encoding and decoding."""
        original_text = "Hello, World!"
        
        encoded = UtilityTool.encode_base64(original_text)
        decoded = UtilityTool.decode_base64(encoded)
        
        assert decoded == original_text
    
    def test_base64_invalid_decode(self):
        """Test base64 decoding with invalid input."""
        result = UtilityTool.decode_base64("invalid_base64!")
        assert "Error decoding" in result
    
    def test_generate_password(self):
        """Test password generation."""
        # Default length
        password = UtilityTool.generate_password()
        assert len(password) == 12
        
        # Custom length
        password = UtilityTool.generate_password(16)
        assert len(password) == 16
        
        # Minimum length
        password = UtilityTool.generate_password(2)
        assert len(password) == 4  # Should be adjusted to minimum
        
        # Maximum length
        password = UtilityTool.generate_password(100)
        assert len(password) == 50  # Should be adjusted to maximum
        
        # Different passwords should be generated
        password1 = UtilityTool.generate_password()
        password2 = UtilityTool.generate_password()
        assert password1 != password2


class TestTextTool:
    """Test TextTool functionality."""
    
    def test_count_words(self):
        """Test word counting."""
        text = "Hello world! This is a test."
        stats = TextTool.count_words(text)
        
        assert stats["words"] == 6
        assert stats["characters"] == len(text)
        assert stats["lines"] == 1
        assert stats["text_length"] == len(text)
    
    def test_count_words_multiline(self):
        """Test word counting with multiple lines."""
        text = "Line 1\nLine 2\nLine 3"
        stats = TextTool.count_words(text)
        
        assert stats["words"] == 6
        assert stats["lines"] == 3
    
    def test_reverse_text(self):
        """Test text reversal."""
        original = "hello"
        reversed_text = TextTool.reverse_text(original)
        assert reversed_text == "olleh"
        
        # Test with palindrome
        palindrome = "racecar"
        assert TextTool.reverse_text(palindrome) == palindrome
    
    def test_case_conversion(self):
        """Test case conversion."""
        text = "Hello World"
        
        assert TextTool.to_uppercase(text) == "HELLO WORLD"
        assert TextTool.to_lowercase(text) == "hello world"
    
    def test_extract_emails(self):
        """Test email extraction."""
        text = "Contact <NAME_EMAIL> or <EMAIL> for help."
        emails = TextTool.extract_emails(text)
        
        assert len(emails) == 2
        assert "<EMAIL>" in emails
        assert "<EMAIL>" in emails
    
    def test_extract_emails_no_emails(self):
        """Test email extraction with no emails."""
        text = "This text has no email addresses."
        emails = TextTool.extract_emails(text)
        
        assert len(emails) == 0
    
    def test_extract_emails_invalid_format(self):
        """Test email extraction with invalid formats."""
        text = "Invalid emails: @example.com, test@, incomplete@domain"
        emails = TextTool.extract_emails(text)
        
        # Should not extract invalid email formats
        assert len(emails) == 0
