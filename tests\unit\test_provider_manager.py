"""
Unit tests for provider management system.
"""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
from datetime import datetime, timedelta

from app.services.provider_manager import (
    ProviderSelector,
    ProviderHealth,
    CircuitBreaker,
    CircuitBreakerState,
    ProviderStatus
)
from app.core.config import ProviderConfig


class TestCircuitBreaker:
    """Test circuit breaker functionality."""
    
    def test_circuit_breaker_initial_state(self):
        """Test circuit breaker initial state."""
        cb = CircuitBreaker()
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.failure_count == 0
        assert cb.can_execute() is True
    
    def test_circuit_breaker_failure_threshold(self):
        """Test circuit breaker opens after failure threshold."""
        cb = CircuitBreaker(failure_threshold=3)
        
        # Record failures
        for i in range(3):
            cb.record_failure()
            if i < 2:
                assert cb.state == CircuitBreakerState.CLOSED
            else:
                assert cb.state == CircuitBreakerState.OPEN
        
        assert cb.can_execute() is False
    
    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery after timeout."""
        cb = CircuitBreaker(failure_threshold=2, recovery_timeout=1)
        
        # Open circuit breaker
        cb.record_failure()
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        
        # Simulate timeout
        cb.last_failure_time = datetime.utcnow() - timedelta(seconds=2)
        
        # Should allow execution and move to half-open
        assert cb.can_execute() is True
        assert cb.state == CircuitBreakerState.HALF_OPEN
    
    def test_circuit_breaker_half_open_success(self):
        """Test circuit breaker closes after successful half-open calls."""
        cb = CircuitBreaker(half_open_max_calls=2)
        cb.state = CircuitBreakerState.HALF_OPEN
        
        # Record successful calls
        cb.record_success()
        assert cb.state == CircuitBreakerState.HALF_OPEN
        
        cb.record_success()
        assert cb.state == CircuitBreakerState.CLOSED
    
    def test_circuit_breaker_half_open_failure(self):
        """Test circuit breaker opens again after half-open failure."""
        cb = CircuitBreaker()
        cb.state = CircuitBreakerState.HALF_OPEN
        
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN


class TestProviderHealth:
    """Test provider health monitoring."""
    
    def test_provider_health_initial_state(self):
        """Test provider health initial state."""
        health = ProviderHealth("test-provider")
        assert health.provider_name == "test-provider"
        assert health.status == ProviderStatus.HEALTHY
        assert health.metrics.total_requests == 0
        assert health.metrics.success_rate == 1.0
    
    def test_provider_health_success_recording(self):
        """Test recording successful requests."""
        health = ProviderHealth("test-provider")
        
        health.record_request(
            success=True,
            response_time=0.5,
            tokens_used=100,
            cost=0.01
        )
        
        assert health.metrics.total_requests == 1
        assert health.metrics.successful_requests == 1
        assert health.metrics.success_rate == 1.0
        assert health.metrics.average_response_time == 0.5
        assert health.metrics.total_tokens_used == 100
        assert health.metrics.total_cost == 0.01
        assert health.status == ProviderStatus.HEALTHY
    
    def test_provider_health_failure_recording(self):
        """Test recording failed requests."""
        health = ProviderHealth("test-provider")
        
        # Record some failures
        for _ in range(3):
            health.record_request(success=False, response_time=1.0)
        
        assert health.metrics.total_requests == 3
        assert health.metrics.failed_requests == 3
        assert health.metrics.success_rate == 0.0
        assert health.status == ProviderStatus.UNHEALTHY
    
    def test_provider_health_degraded_status(self):
        """Test provider degraded status."""
        health = ProviderHealth("test-provider")
        
        # Record mixed success/failure (70% success rate)
        for _ in range(7):
            health.record_request(success=True, response_time=0.5)
        for _ in range(3):
            health.record_request(success=False, response_time=1.0)
        
        assert health.metrics.success_rate == 0.7
        assert health.status == ProviderStatus.DEGRADED
    
    def test_provider_health_score_calculation(self):
        """Test health score calculation."""
        health = ProviderHealth("test-provider")
        
        # Perfect health
        health.record_request(success=True, response_time=0.1)
        score = health.get_health_score()
        assert score > 0.9
        
        # Poor health
        health = ProviderHealth("test-provider")
        for _ in range(5):
            health.record_request(success=False, response_time=5.0)
        score = health.get_health_score()
        assert score < 0.5


class TestProviderSelector:
    """Test provider selection logic."""
    
    def setup_method(self):
        """Setup test method."""
        self.selector = ProviderSelector()
    
    def test_provider_registration(self):
        """Test provider registration."""
        config = ProviderConfig(
            enabled=True,
            priority=1,
            models=["gpt-4", "gpt-3.5-turbo"]
        )
        
        self.selector.register_provider("openai", config)
        
        assert "openai" in self.selector.provider_configs
        assert "openai" in self.selector.provider_health
        assert self.selector.provider_configs["openai"] == config
    
    def test_provider_selection_by_priority(self):
        """Test provider selection by priority."""
        # Register providers with different priorities
        high_priority_config = ProviderConfig(enabled=True, priority=1)
        low_priority_config = ProviderConfig(enabled=True, priority=2)
        
        self.selector.register_provider("openai", high_priority_config)
        self.selector.register_provider("anthropic", low_priority_config)
        
        selected = self.selector.select_provider()
        assert selected == "openai"  # Higher priority (lower number)
    
    def test_provider_selection_with_model_requirement(self):
        """Test provider selection with model requirements."""
        openai_config = ProviderConfig(
            enabled=True,
            priority=1,
            models=["gpt-4", "gpt-3.5-turbo"]
        )
        anthropic_config = ProviderConfig(
            enabled=True,
            priority=2,
            models=["claude-3", "claude-2"]
        )
        
        self.selector.register_provider("openai", openai_config)
        self.selector.register_provider("anthropic", anthropic_config)
        
        # Should select anthropic for claude-3
        selected = self.selector.select_provider(model_required="claude-3")
        assert selected == "anthropic"
        
        # Should select openai for gpt-4
        selected = self.selector.select_provider(model_required="gpt-4")
        assert selected == "openai"
        
        # Should return None for unsupported model
        selected = self.selector.select_provider(model_required="unsupported-model")
        assert selected is None
    
    def test_provider_selection_excludes_unhealthy(self):
        """Test provider selection excludes unhealthy providers."""
        config = ProviderConfig(enabled=True, priority=1)
        
        self.selector.register_provider("openai", config)
        self.selector.register_provider("anthropic", config)
        
        # Make openai unhealthy
        openai_health = self.selector.provider_health["openai"]
        openai_health.circuit_breaker.state = CircuitBreakerState.OPEN
        
        selected = self.selector.select_provider()
        assert selected == "anthropic"
    
    def test_provider_usage_recording(self):
        """Test provider usage recording."""
        config = ProviderConfig(enabled=True, priority=1)
        self.selector.register_provider("openai", config)
        
        # Record successful usage
        self.selector.record_provider_usage(
            "openai",
            success=True,
            response_time=0.5,
            tokens_used=100,
            cost=0.01
        )
        
        health = self.selector.provider_health["openai"]
        assert health.metrics.total_requests == 1
        assert health.metrics.successful_requests == 1
        assert health.metrics.total_tokens_used == 100
        assert health.metrics.total_cost == 0.01
    
    def test_provider_status_retrieval(self):
        """Test provider status retrieval."""
        config = ProviderConfig(
            enabled=True,
            priority=1,
            models=["gpt-4"]
        )
        self.selector.register_provider("openai", config)
        
        status = self.selector.get_provider_status("openai")
        
        assert status is not None
        assert status["name"] == "openai"
        assert status["status"] == "healthy"
        assert "health_score" in status
        assert "metrics" in status
        assert "config" in status
    
    def test_circuit_breaker_reset(self):
        """Test manual circuit breaker reset."""
        config = ProviderConfig(enabled=True, priority=1)
        self.selector.register_provider("openai", config)
        
        # Open circuit breaker
        health = self.selector.provider_health["openai"]
        health.circuit_breaker.state = CircuitBreakerState.OPEN
        health.status = ProviderStatus.CIRCUIT_OPEN
        
        # Reset circuit breaker
        result = self.selector.reset_provider_circuit_breaker("openai")
        
        assert result is True
        assert health.circuit_breaker.state == CircuitBreakerState.CLOSED
        assert health.status == ProviderStatus.HEALTHY
    
    def test_all_provider_status(self):
        """Test getting all provider status."""
        config1 = ProviderConfig(enabled=True, priority=1)
        config2 = ProviderConfig(enabled=True, priority=2)
        
        self.selector.register_provider("openai", config1)
        self.selector.register_provider("anthropic", config2)
        
        all_status = self.selector.get_all_provider_status()
        
        assert len(all_status) == 2
        assert any(status["name"] == "openai" for status in all_status)
        assert any(status["name"] == "anthropic" for status in all_status)
